'use client';

import { useEffect } from 'react';
import posthog from 'posthog-js';
import { PostHog<PERSON>rovider as PHProvider } from 'posthog-js/react';

const key = process.env.NEXT_PUBLIC_POSTHOG_API_KEY;
const host = process.env.NEXT_PUBLIC_POSTHOG_HOST;
const isTrackingEnabled = process.env.NEXT_PUBLIC_POSTHOG_TRACKING_ENABLED === 'true';

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (isTrackingEnabled && key && host) {
      posthog.init(key, {
        api_host: host,
        capture_pageview: 'history_change',
        person_profiles: 'always',
        capture_pageleave: false,
        capture_performance: {
          web_vitals: false,
        },
      });
    }
  }, [isTrackingEnabled, key, host]);

  if (isTrackingEnabled && key) {
    return <PHProvider client={posthog}>{children}</P<PERSON><PERSON>ider>;
  }
  return <>{children}</>;
}
