import { memo } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

const PaymentMethodTag = ({ method }: { method: string }) => {
  const t = useTranslations('checkout.payment_methods');
  
  return (
    <Image
      style={{ marginRight: 10, borderRadius: '17%', border: '1px solid #bbb' }}
      src={`/checkout/payment-methods/${method}.png`}
      alt={`Icon of ${t(method)} payment method.`}
      width={52}
      height={32}
    />
  );
};

export default memo(PaymentMethodTag);
