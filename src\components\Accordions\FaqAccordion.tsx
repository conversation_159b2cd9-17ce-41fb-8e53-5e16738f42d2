'use client';

import { useState, useRef, useEffect, memo } from 'react';

const FaqAccordion = ({ item, index }: { item: { title: string; text: JSX.Element }; index: number }) => {
  const [visible, setVisible] = useState<boolean>(index === 0 ? true : false);
  const [maxHeight, setMaxHeight] = useState<number>(0);
  const panelRef = useRef(null);

  useEffect(() => {
    if (panelRef.current) {
      //@ts-ignore
      visible ? setMaxHeight(panelRef.current.scrollHeight) : setMaxHeight(0);
    }
  }, [visible]);

  return (
    <div
      key={index}
      onClick={() => {
        setVisible(!visible);
      }}
      style={{ maxWidth: 736 }}>
      <button
        className={`accordion ${visible ? 'text-primary' : 'text-[#191919]'}`}
        style={{ padding: index === 0 ? '0 0 24px 0' : '24px 0' }}>
        {item.title}{' '}
        <div
          className={`chevron ${visible ? 'text-primary' : 'text-[#191919]'}`}
          style={{ transform: visible ? 'rotate(270deg)' : 'rotate(90deg)' }}>
          ❯
        </div>
      </button>

      <div className="panel" style={{ maxHeight, paddingBottom: visible ? 24 : 0 }} ref={panelRef}>
        {item.text}
      </div>
    </div>
  );
};

export default memo(FaqAccordion);
