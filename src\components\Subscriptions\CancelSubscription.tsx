'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Modal } from 'react-responsive-modal';
import { httpsCallable } from 'firebase/functions';
import { auth, functions } from '@/utils/firebase';
import { PaymentProvider } from '@/store/types';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';

interface CancelSubscriptionProps {
  onProcessing: (loading: boolean) => void;
}

interface CancelSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

function CancelSubscriptionModal({ isOpen, onClose, onConfirm }: CancelSubscriptionModalProps) {
  const t = useTranslations('members.subscription');

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      center
      showCloseIcon={false}
      classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
      <h2 className="text-lg text-center tracking-normal w-full">{t('cancel_modal.title')}</h2>
      <div className="flex w-full justify-center gap-5 mt-5">
        <button
          onClick={() => {
            onClose();
            onConfirm();
          }}
          className="secondary !p-3 rounded-md w-[120px]">
          {t('cancel_modal.btn_yes_cancel')}
        </button>
        <button onClick={onClose} className="primary !p-3 rounded-md w-[120px]">
          {t('cancel_modal.btn_no')}
        </button>
      </div>
    </Modal>
  );
}

export default function CancelSubscription({ onProcessing }: CancelSubscriptionProps) {
  const t = useTranslations('members.subscription');
  const { user, canCancelSubscription } = useSubscriptionPlan();
  const [modalOpen, setModalOpen] = useState(false);

  /**
   * Cancels the user's subscription.
   */
  async function cancelSubscription(): Promise<void> {
    if (!user || !canCancelSubscription) return;

    onProcessing(true);

    try {
      await httpsCallable(
        functions,
        user.activeSubscriptionType === PaymentProvider.SOLIDGATE ? 'cancelSubscriptionSolidgate' : 'cancelSubscription'
      )();

      // Force refresh ID token to reflect subscription status change
      if (auth.currentUser) {
        await auth.currentUser.getIdToken(true);
      }
    } catch (error) {
      console.log('Error while cancelling subscription:', error);
    } finally {
      onProcessing(false);
    }
  }

  if (!canCancelSubscription) {
    return null;
  }

  return (
    <>
      <button onClick={() => setModalOpen(true)} className="text-sm text-gray-400 mt-1">
        {t('btn_cancel_subscription')}
      </button>

      <CancelSubscriptionModal isOpen={modalOpen} onClose={() => setModalOpen(false)} onConfirm={cancelSubscription} />
    </>
  );
}
