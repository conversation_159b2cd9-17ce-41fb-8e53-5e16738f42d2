import { useState, useCallback } from 'react';
import lscache from 'lscache';
import { remove, update } from '@/store/utils';

export const DEFAULT_ATTEMPTS_KEY = 'merchant-data-attempts';
export const DEFAULT_MAX_ATTEMPTS = 3;

/**
 * React hook to manage and persist the number of payment intent creation attempts.
 *
 * This hook keeps track of how many times a user has tried to create a payment intent,
 * storing the count in both React state and local storage (via lscache) for persistence.
 * It provides the current attempt count, a function to increment the count, and the max allowed attempts.
 *
 * @param key - (optional) The local storage key to use for tracking attempts. Defaults to 'merchant-data-attempts'.
 * @param max - (optional) The maximum number of allowed attempts. Defaults to 1.
 * @returns An object with:
 *   - attempts: The current number of attempts.
 *   - incrementAttempts: A function to increment the attempt count.
 *   - maxAttempts: The maximum allowed attempts.
 *
 * @example
 * const { attempts, incrementAttempts, maxAttempts } = usePaymentIntentAttempts();
 * if (attempts >= maxAttempts) { ... }
 * incrementAttempts();
 */
export function usePaymentIntentAttempts(
  key: string = DEFAULT_ATTEMPTS_KEY,
  maxAttempts: number = DEFAULT_MAX_ATTEMPTS
) {
  const getAttempts = () => parseInt(lscache.get(`se-${key}`) || '0', 10);
  const [attempts, setAttempts] = useState(getAttempts());

  // Increments the payment intent attempts counter in both React state and local storage using the update utility
  const incrementAttempts = useCallback(() => {
    update({
      contextPrefix: 'se',
      variableName: key,
      newVariable: attempts + 1,
      oldVariable: attempts,
      setter: setAttempts,
      getter: () => attempts + 1,
    });
  }, [attempts, key]);

  // Clears both the payment intent and attempts from local storage and resets state
  const clearAttempts = useCallback(() => {
    remove({
      contextPrefix: 'se',
      variableName: key,
      newVariable: attempts,
      setter: setAttempts,
    });
  }, [key, attempts]);

  return { attempts, incrementAttempts, maxAttempts, clearAttempts };
}
