'use client';

import '@/sass/form.scss';
import { useEffect, useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { z } from 'zod';
import { Link } from '@/lib/i18n/routing';
import { functions } from '@/utils/firebase';
import { useTranslations } from 'next-intl';

const passwordStrength = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);

  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
};

export default function ResetPassword() {
  const t = useTranslations('reset_password');
  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirmed, setNewPasswordConfirmed] = useState('');
  const [message, setMessage] = useState('');
  const [processing, setProcessing] = useState(false);
  const [uid, setUid] = useState('');
  const [token, setToken] = useState('');
  const [success, setSuccess] = useState(false);

  async function resetPassword() {
    setProcessing(true);
    if (newPassword !== newPasswordConfirmed) {
      setMessage(t('password_mismatch'));
      setProcessing(false);
      return;
    }
    const isValidPassword = z.string().refine(passwordStrength).safeParse(newPassword).success;

    if (!isValidPassword) {
      setMessage(t('password_requirements'));
      setProcessing(false);
      return;
    }

    setMessage('');
    const sendEmail = httpsCallable(functions, 'resetPassword');
    const result: any = await sendEmail({ uid, token, password: newPassword });
    if (result.data.success)
        setSuccess(true);
    else 
        setMessage(result.data.message);

    setProcessing(false);
  }

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const token = query.get('token');
    const uid = query.get('uid');

    if (!token || !uid) {
        setMessage(t('invalid_link'));
        return;
    }

    setUid(uid);
    setToken(token);
  }, [t]);

  if (message == t('invalid_link')) {
    return <div className="flex flex-col justify-center items-center">{t('invalid_link')}</div>;
  } else if (success) {
    return <div className="flex flex-col justify-center items-center gap-5 px-5">
        <h3 className='text-center'>{t('success_title')}</h3>
        <p className='text-center'>{t('success_message')}</p>
        <Link href="/login" className="primary rounded-lg mt-2 flex justify-center">{t('btn_login')}</Link>
    </div>;
  }

  return (
    <div className="flex flex-col justify-center items-center">
      <h3>{t('title')}</h3>
      <div id="msform" className='flex flex-col items-center justify-center'>
        <input
          type="password"
          name="new_password"
          value={newPassword}
          onChange={e => setNewPassword(e.target.value)}
          placeholder={t('new_password_placeholder')}
          required
        />
        <input
          type="password"
          name="new_password_confirmed"
          value={newPasswordConfirmed}
          onChange={e => setNewPasswordConfirmed(e.target.value)}
          placeholder={t('new_password_confirmed_placeholder')}
          required
        />
        {message != '' && <span className="block mb-2 text-left mt-2 pl-[10px]">{message}</span>}
        <button disabled={processing} onClick={resetPassword} className="primary rounded-lg mt-2 flex justify-center">
          {processing ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2" /> : ''}
          {t('btn_set_password')}
        </button>
      </div>
    </div>
  );
}
