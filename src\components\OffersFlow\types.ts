// Common handler function types
export type HandleNext = () => void;
export type HandleBack = () => void;
export type HandleReset = () => void;

// Base interface for components with common handlers
export interface BaseNextHandlerProps {
  handleNext: HandleNext;
}

export interface BaseBackHandlerProps {
  handleBack: HandleBack;
}

export interface BaseResetHandlerProps extends BaseNextHandlerProps {
  handleReset: HandleReset;
}
