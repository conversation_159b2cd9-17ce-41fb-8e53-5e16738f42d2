import dayjs from 'dayjs';

/**
 * Converts an amount in cents to a string with two decimal places.
 *
 * @param cents - The amount in cents.
 * @returns The formatted amount in dollars, e.g. "4.90" or "-3.25".
 */
export const centsToCurrency = (cents: number): string => {
  const amount = cents / 100;
  return amount.toFixed(2);
};

/**
 * Formats a timestamp to DD.MM.YYYY format
 * @param timestamp - Unix timestamp in seconds or milliseconds
 * @param isMilliseconds - Whether the timestamp is in milliseconds (default: false)
 * @returns Formatted date string
 */
export const formatInvoiceDate = (timestamp: number | string, isMilliseconds = false): string => {
  const multiplier = isMilliseconds ? 1 : 1000;
  const numericTimestamp = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;
  return dayjs(numericTimestamp * multiplier).format('DD.MM.YYYY');
};

/**
 * Formats a date string to DD.MM.YYYY format
 * @param dateString - Date string
 * @returns Formatted date string
 */
export const formatDateString = (dateString: string | number): string => {
  return dayjs(dateString).format('DD.MM.YYYY');
};
