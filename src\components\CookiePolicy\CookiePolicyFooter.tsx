'use client';

import { useTranslations } from 'next-intl';

const CookiePolicyFooter = () => {
  const t = useTranslations('cookie_policy');

  return (
    <div className="cookie-policy-footer">
      <p className='cookie-policy-p'>
        {t('sections.cookie_policy_generated_by')}
        <a target='_blank' href='https://www.cookieyes.com/?utm_source=CP&utm_medium=footer&utm_campaign=UW'>
          {t('sections.cookie_policy_generated_by_link')}
        </a>
        .
      </p>
    </div>
  );
};

export default CookiePolicyFooter; 