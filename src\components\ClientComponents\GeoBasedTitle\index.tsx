'use client';

import { useContext, useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import Cookies from 'js-cookie';
import CountryFlag from '@/components/CountryFlag';
import GeoBasedLoading from '@/components/Loaders/GeoBasedLoading';
import { useCountryName } from '@/hooks/useCountryTranslations';
import UiContext from '@/store/UiContext';

interface Country {
  id: string;
  iq: number;
  name: string;
}

interface GeoBasedTitleProps {  
  countries: Country[];
}

const GeoBasedTitle = ({ countries }: GeoBasedTitleProps) => {
  const t = useTranslations('home_page');
  const { completedByNumber } = useContext(UiContext);
  const [number, setNumber] = useState(completedByNumber);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize cookie values and country data to prevent recalculation
  const { country, locale, region } = useMemo(() => {
    const locale = Cookies.get('locale')?.toLowerCase();
    const region = Cookies.get('region')?.toLowerCase();
    
    // Find the correct country data
    const country = locale === 'us' && region
      ? countries.find((c) => c.id === `us-${region}`)
      : countries.find((c) => c.id === locale) || countries.find((c) => c.id === 'us');

    return { country, locale, region };
  }, [countries]); // Only recalculate if countries prop changes

  // Get translated country name - this hook will only run when country.id changes
  const countryName = useCountryName(country?.id || '');

  useEffect(() => {
    setNumber(completedByNumber);
  }, [completedByNumber]);

  // Handle loading state based on region/country availability
  useEffect(() => {
    if (locale === 'us') {
      // For US users, wait for region
      setIsLoading(!locale);
    } else {
      // For non-US users, wait for country data
      setIsLoading(!country);
    }
  }, [locale, region, country]);

  if (isLoading || !country) {
    return <GeoBasedLoading />;
  }

  return (
    <div>
      <div style={{ lineHeight: '20px', marginBottom: 26 }}>
        <CountryFlag
          className="float-left mr-2.5"
          countryId={country.id}
          countryName={countryName}
          width={30}
          height={16}
          priority={true}
        />
        <span suppressHydrationWarning={true} style={{ fontSize: 16, lineHeight: '125%', color: '#8893AC' }}>
          {t('header_users_today', { number })}
        </span>
      </div>
      <h1>{t('header_avg_IQ', { country: countryName, score: country.iq.toFixed() })}</h1>
    </div>
  );
};

export default GeoBasedTitle;
