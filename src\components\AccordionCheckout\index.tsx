"use client";
import { useState } from "react";
import Image from "next/image";
const AccordionCheckout = ({ title, content }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="w-full px-5 border-b border-[#E5EBFA]">
      <div className="border-gray-300">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`w-full transition-all text-left py-3 md:py-[18px] font-medium text-gray-700 focus:outline-none flex justify-between items-center text-[#2B2D42]"`}
        >
          <div
            className="text-[#2B2D42] font-segoe font-semibold transition-all text-[14px] md:text-[18px] leading-[24px]"
          >
            {title}
          </div>
          <Image src='/images/checkout-v3/collapse_button.svg' width={18} height={18} 
            alt="..."
            className={`"w-[18px] transform transition-transform duration-300 ${isOpen ? "rotate-90" : ""}`}/>
        </button>
        <div
            className={`transition-max-height duration-500 ease-in-out overflow-hidden ${
              isOpen ? "max-h-screen" : "max-h-0"
            }`}
          >
          <p className="font-segoe font-normal text-[#2B2D42] text-[13px] md:text-[13px] leading-[20px]">
            {content}
        </p>
        </div>
      </div>
    </div>
  );
};

export default AccordionCheckout;