import { useTranslations } from 'next-intl';

const RequestRefundAnswer = () => {
  const t = useTranslations('support_page.faq_details.request-refund.answer');

  // Get the list items safely
  const currencyItems = t.raw('currency_items') || [];
  const sectionsItems = t.raw('sections_items') || [];

  return (
    <>
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph1') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph2') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph3') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph4') }} 
      />
      
      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {currencyItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>
      
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph5') }} 
      />
      
      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {sectionsItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>
      
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph6') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph7') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph8') }} 
      />
    </>
  );
};

export default RequestRefundAnswer; 