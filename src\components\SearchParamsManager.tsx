'use client';

import { useEffect, useRef } from 'react';
import {
  useSetCheckoutVersion,
  useSetClaimOffer,
  useSetPaymentSystem,
  useSetPlanTypeAndPlan,
  useSetSubscriptionPlan,
} from '@/hooks';

/**
 * SearchParamsManager is a component that manages the search parameters for the client-side application.
 * It updates the session context with the search parameters.
 *
 * @returns
 */
export default function SearchParamsManager() {
  const hasMounted = useRef(false);

  const { setCheckoutVersion } = useSetCheckoutVersion();
  const { setClaimOffer } = useSetClaimOffer();
  const { setPaymentSystem } = useSetPaymentSystem();
  const { setPlanTypeAndPlan } = useSetPlanTypeAndPlan();
  const { setSubscriptionPlan } = useSetSubscriptionPlan();

  useEffect(() => {
    // Ensure setPaymentSystem is called once after mount
    if (!hasMounted.current) {
      hasMounted.current = true;
      setCheckoutVersion();
      setClaimOffer();
      setPaymentSystem();
      setPlanTypeAndPlan();
      setSubscriptionPlan();
    }
  }, [setCheckoutVersion, setClaimOffer, setPaymentSystem, setPlanTypeAndPlan, setSubscriptionPlan]);

  return null;
}
