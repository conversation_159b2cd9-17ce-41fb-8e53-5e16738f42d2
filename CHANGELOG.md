# Changelog

All notable changes to this project will be documented here.

This project adheres to [Semantic Versioning](https://semver.org/) and follows [Keep a Changelog](https://keepachangelog.com/en/1.0.0/).

---

## [Unreleased]

## [2024-07-10]

### Added

- [#IQ-99] Added a separate "Payment Status" page for Stripe payment providers. Contributed by @rahul-rocket.
- [#IQ-102] Added "Plan Type" and "Plan" to the data layer (with the same Conversion Type) for Google Tag Manager. Contributed by @rahul-rocket.
- [#IQ-107] Added "Payment Failed" events for PostHog. Contributed by @GloireMutaliko21.
- Added a centralized `usePostHogEvents` hook for managing PostHog events. Contributed by @rahul-rocket.
- Completed the UI of /academy/toolkits page for both desktop and mobile views. Contributed by @AngelaZafirovska.

### Changed

- [#IQ-104] Adjusted Love/EQ Checkout and Payment pages. Contributed by @GloireMutaliko21.
- [#IQ-99] Modified the existing Stripe Checkout form to support the additional payment status page. Contributed by @rahul-rocket.
- Improved Stripe/Solidgate payment provider form events (GTM, PostHog). Contributed by @rahul-rocket.
- Updated `PostHogProvider` to enable/disable PostHog events. Contributed by @rahul-rocket.
- Updated translations for several pages. Contributed by @rahul-rocket.
- Refactored the Solidgate payment provider's create intent method. Contributed by @rahul-rocket.
- [#IQ-45] Implemented a disappearing animation for the confirmation message on Screen 1. Contributed by @AngelaZafirovska.
- [#IQ-45] Reduced the text size as previously suggested (approximately 80–90% of the original size). Contributed by @AngelaZafirovska.

### Fixed

- Fixed the correct subscription amount and currency for `begin_checkout` and `purchase` GTM events. Contributed by @rahul-rocket.
- Disabled weekly subscriptions for the Solidgate payment provider. Contributed by @rahul-rocket.
- Disabled one-time payments for the Solidgate payment provider. Contributed by @rahul-rocket.
- Limited Payment Intent creation for Stripe and Solidgate when the page is refreshed. Contributed by @rahul-rocket.
- [#IQ-45] Adjusted the layout to ensure all bottom buttons remain consistently visible, addressing recurring visibility issues. Contributed by @AngelaZafirovska.
- [#IQ-45] Refactored the currency handling by using a constant. Contributed by @AngelaZafirovska.

## [2024-07-08]

### Added

- [#IQ-47] Added support for one-time payments (USD price only). Contributed by @rahul-rocket.
- [#IQ-47] Added translation for one-time payment access expiry. Contributed by @rahul-rocket.
- [#IQ-47] Added a centralized `useSubscriptionPlan` hook for managing subscription plans and user subscriptions. Contributed by @rahul-rocket.
- Added public static images related to upsells. Contributed by @AngelaZafirovska.

### Changed

- [#IQ-47] Refactored subscription management components for better modularity. Contributed by @rahul-rocket.
- Updated the structure and paths for upsell pages and initialized the new structure. Contributed by @AngelaZafirovska.
- [#IQ-47] Made the invoice page require authentication. Contributed by @rahul-rocket.
- [#IQ-47] Updated the SolidgateInvoice and StripeInvoice components. Contributed by @rahul-rocket.
- Updated texts in the Solidgate/Stripe payment form details page. Contributed by @rahul-rocket.
- Updated translations for several more pages. Contributed by @rahul-rocket.

### Fixed

- Fixed an issue where labels were not positioned correctly on upsell pages. Contributed by @AngelaZafirovska.
- [#IQ-82] Fixed compliance checkout version to V1 and used a constant for versioning. Contributed by @rahul-rocket.
- [#IQ-95] Fixed pricing in Checkout v3 for weekly subscription. Contributed by @rahul-rocket.
- Fixed conversion of one-time prices to trial prices, as it was previously set to trial. Contributed by @rahul-rocket.

## [2024-07-02]

### Added

- [#IQ-49] Added a Weekly subscription option (in addition to monthly). Contributed by @rahul-rocket.
- [#IQ-86] Implemented a flag to bypass waiting time for Checkout v3 for testing purposes (disabled in prod). Contributed by @rahul-rocket.
- [#IQ-49] Added new Stripe prices for weekly subscription. Contributed by @rahul-rocket.
- Added a centralized environment config file (`src/config/env.ts`) to manage environment flags (dev, stage, prod) based on NEXT_PUBLIC_ENV. Contributed by @rahul-rocket.
- Added a weekly subscription option in the UI. Contributed by @rahul-rocket.

### Changed

- [#IQ-90] Made small adjustments to Checkout v3. Contributed by @rahul-rocket.
- Updated PostHog user properties to reflect the correct checkout version (`compliant`), based on eligibility after test completion. Contributed by @GloireMutaliko21.
- Send more data to Stripe metadata including device type, OS and browser.
- Refactored the client layout and added a component to store search query parameters in local storage. Contributed by @rahul-rocket.
- Refactored search params components and created separate hooks to manage query parameters. Contributed by @rahul-rocket.

### Fixed

- Fixed ESLint and Next.js build issues, and removed unnecessary packages. Contributed by @rahul-rocket.
- Fixed translation-related issues. Contributed by @rahul-rocket.
- Enabled a subscription trial period for all countries, including the UAE. Contributed by @rahul-rocket.

## [2024-06-26]

### Added

- Introduced dedicated hooks for payment intent creation and session saving, making the checkout process more modular and maintainable.
- Integrated Stripe client and configuration with Zod validation to ensure safer and more reliable payment setup.

### Changed

- Refactored Google Tag Manager (GTM) event tracking to use shared hooks, improving consistency and simplifying analytics integration.

### Fixed

- Fixed an issue where Love Languages and EQ Tests results were not appearing in users' 'Test history'. #IQ-70 & IQ-76
- Fixed a bug that caused duplicate session saves and reports when the Checkout page was refreshed multiple times.

## [2025-06-24]

### Added

- [#IQ-71] Standardized editor setup using Prettier and ESLint for consistent frontend formatting.
- Improved default fallback behavior for Checkout v3 to auto-load:
  - The default checkout version when `localStorage` is empty.
  - The default payment provider when no value is stored.

### Changed

- Updated "Go to Invoices" navigation to adapt based on the active subscription type.
- Enhanced payment provider logic handling in Checkout v3 to reduce errors and improve UX.

### Fixed

- Corrected redirection logic for compliant checkout versions.
- Fixed multiple React warnings in the Order Form and Checkout v3 components.
- Implemented PostHog tracking fixes (contributed by @Gloire Mutaliko).
