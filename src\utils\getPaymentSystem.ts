import { PaymentProvider } from '@/store/types';

/**
 * Retrieves the payment system from environment variable with validation
 * @returns PaymentProvider - defaults to 'stripe' if no env var or invalid
 */
export function getPaymentSystem(): PaymentProvider {
  const envPaymentSystem = process.env.NEXT_PUBLIC_USE_PAYMENT;

  if (!envPaymentSystem) {
    return getDefaultPaymentSystem();
  }

  // Validate against PaymentProvider enum values
  if (envPaymentSystem === PaymentProvider.STRIPE || envPaymentSystem === PaymentProvider.SOLIDGATE) {
    return envPaymentSystem as PaymentProvider;
  }

  // If invalid value, return default
  console.warn(`Invalid payment system in NEXT_PUBLIC_USE_PAYMENT: ${envPaymentSystem}.`);
  return PaymentProvider.STRIPE;
}

/**
 * Gets the default payment system
 * @returns PaymentProvider - always returns 'stripe' as default
 */
export function getDefaultPaymentSystem(): PaymentProvider {
  return PaymentProvider.STRIPE;
}
