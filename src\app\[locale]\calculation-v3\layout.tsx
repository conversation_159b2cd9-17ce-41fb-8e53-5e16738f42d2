import '@/sass/animation.scss';

import Link from 'next/link';
import Image from 'next/image';

export default function CalculationV3Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="w-full bg-[#FFFCFC]">
      <div className="flex justify-start items-center h-[64px] px-4 md:px-[150px] border shadow border-grey-91 bg-white">
        <Link href="/">
          <Image src="/images/Logo.svg" alt="Logo" width={300} height={36} className="w-[217px] md:w-full h-auto" />
        </Link>
      </div>
      {children}
    </div>
  );
}
