'use client';

import React, { useContext } from 'react';
import { Link } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

type HeaderContentProps = {
  totalScore: number;
  totalTitle: string;
  totalDescription: string;
};

const HeaderContent: React.FC<HeaderContentProps> = ({ totalScore, totalTitle, totalDescription }) => {
  const { resetEmotionalTestState } = useContext(SessionContext); // Access the reset function

  return (
    <div className="max-w-[1440px] mx-auto">
      <div className="flex flex-col md:max-w-[715px] px-4 md:px-0 md:pl-[82px] mt-4 md:mt-[147px] mb-[40px] md:mb-[100px]">
        <span className="font-ppmori font-normal text-[#0C0113] text-[14px] md:text-[18px] leading-[20px] md:leading-[24px] mb-[15px] md:mb-[36px] mt-[36px] md:mt-0 order-4 md:order-none">
          Unsure of Your Results?{' '}
          <Link
            href="/emotional-intelligence/test"
            className="underline font-semibold tracking-[-0.03em]"
            onClick={() => resetEmotionalTestState()} // Call the reset function
          >
            Take the Test Again
          </Link>
        </span>
        <div className="flex flex-row w-full items-center gap-2 md:gap-3 mb-[12px] md:mb-[20px] order-1 md:order-none">
          <span className="font-ppmori font-semibold text-[#351C44] text-[18px] md:text-[32px] leading-[24px] md:leading-[36px] tracking-[-0.03em]">
            You scored
          </span>
          <span className="rounded-[40px] bg-[#8C36D01A] border border-[#8C36D014] text-[16px] md:text-[24px] leading-[26px] md:leading-[34px] text-[#B685DD] py-1 md:py-2 px-2 md:px-[14px]">
            <span className="font-ppmori font-semibold text-[22px] md:text-[32px] leading-[26px] md:leading-[34px] text-[#8C36D0]">
              {totalScore}
            </span>
            /300
          </span>
          <span className="font-ppmori font-semibold text-[#351C44] text-[18px] md:text-[32px] leading-[24px] md:leading-[36px] tracking-[-0.03em]">
            showing you have
          </span>
        </div>
        <span className="font-ppmori font-semibold text-[#8C36D0] text-[48px] md:text-[70px] leading-[52px] md:leading-[76px] tracking-[-0.03em] mb-4 md:mb-6 order-2 md:order-none">
          {totalTitle}
        </span>
        <span className="font-ppmori font-normal text-[#8C8492] text-[16px] md:text-[18px] leading-[24px] md:leading-[27px] order-3 md:order-none">
          {totalDescription}
        </span>
      </div>
    </div>
  );
};

export default HeaderContent;
