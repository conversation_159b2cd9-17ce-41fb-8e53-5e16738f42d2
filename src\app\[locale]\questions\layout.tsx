import '@/sass/steps.scss';
import Timer from '@/components/ClientComponents/Timer';
import Brand from '@/components/Brand';

export default function QuestionsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div>
      <div className='flex justify-between items-center mt-4 md:mt-7 px-[5%] md:px-[5.69444%]'>
        <Brand />
        <div className='ml-auto'>
          <Timer />
        </div>
      </div>
      {children}
    </div>
  );
}
