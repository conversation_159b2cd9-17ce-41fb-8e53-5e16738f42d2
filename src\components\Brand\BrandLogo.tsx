'use client';

import Image from 'next/image';
import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import SessionContext from '@/store/SessionContext';

interface BrandLogoProps {
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
  quality?: number;
}

const BrandLogo = ({ className = '', style = {}, priority = true, quality = 100 }: BrandLogoProps) => {
  const { siteConfig } = useContext(SessionContext);
  const t = useTranslations('common.brand');

  return (
    <Image
      alt={t('alt.logo')}
      className={className}
      src={siteConfig.logo.path}
      width={siteConfig.logo.width}
      height={siteConfig.logo.height}
      quality={quality}
      priority={priority}
      style={style}
    />
  );
};

export default BrandLogo; 