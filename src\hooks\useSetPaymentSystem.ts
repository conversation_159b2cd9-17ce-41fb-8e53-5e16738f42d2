import { useCallback, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import { usePaymentIntentAttempts } from '@/hooks/usePaymentIntentAttempts';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';

export function useSetPaymentSystem() {
  const searchParams = useSearchParams();
  const { updatePaymentSystem } = useContext(SessionContext);
  const { clearAttempts } = usePaymentIntentAttempts();

  // Set payment provider using callback to prevent re-rendering
  const setPaymentSystem = useCallback(() => {
    const pp = searchParams.get('payment') as PaymentProvider;

    // Update payment provider in localStorage if paymentParam is a valid PaymentProvider
    const providers = new Set([PaymentProvider.STRIPE, PaymentProvider.SOLIDGATE]);

    if (pp && providers.has(pp)) {
      clearAttempts();
      updatePaymentSystem(pp);
    }
    // If paymentParam is not present, do not touch localStorage
  }, [searchParams, updatePaymentSystem]);

  return { setPaymentSystem };
}
