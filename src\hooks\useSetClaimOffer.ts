import { useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useClaimOffer } from '@/store/hooks';
import { parseQueryParamBoolean } from '@/utils/parseQueryParam';

export function useSetClaimOffer() {
  const searchParams = useSearchParams();
  const { updateClaimOffer } = useClaimOffer();

  // Set claim offer using callback to prevent re-rendering
  const setClaimOffer = useCallback(() => {
    const cop = searchParams.get('cop');

    // If co (claim offer) is present, update it in localStorage
    if (cop) {
      const claimOffer = parseQueryParamBoolean(cop);
      updateClaimOffer(claimOffer);
    }
    // If co is not present, do not touch localStorage
  }, [searchParams, updateClaimOffer]);

  return { setClaimOffer };
}
