/**
 * Generic utility to parse query parameters with customizable return values.
 *
 * @param value - The query parameter value (can be null or string)
 * @param options - Configuration object
 * @param options.defaultValue - Value to return if input is null/undefined
 * @param options.falsyValue - Value to return for falsy inputs ('false', '0', '', 'null', 'undefined')
 * @param options.truthyValue - Value to return for truthy inputs, or a function for custom logic
 * @returns Parsed value based on configuration
 */
export function parseQueryParam<T>(
  value: string | null,
  options: {
    defaultValue: T;
    falsyValue: T;
    truthyValue: T | ((normalizedValue: string) => T);
  }
): T {
  if (!value) return options.defaultValue;

  const normalized = value.trim().toLowerCase();

  // Check for explicitly falsy values
  if (
    normalized === 'false' ||
    normalized === '0' ||
    normalized === '' ||
    normalized === 'null' ||
    normalized === 'undefined'
  ) {
    return options.falsyValue;
  }

  // Handle truthy values
  if (typeof options.truthyValue === 'function') {
    return (options.truthyValue as (normalizedValue: string) => T)(normalized);
  }

  return options.truthyValue;
}

/**
 * Parses boolean query parameters (true/false, 1/0).
 *
 * @param value - The query parameter value
 * @returns true for truthy values, false for falsy values
 */
export function parseQueryParamBoolean(value: string | null): boolean {
  return parseQueryParam(value, {
    defaultValue: false,
    falsyValue: false,
    truthyValue: true,
  });
}
