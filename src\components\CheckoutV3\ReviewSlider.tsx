import { useRef, useState, useEffect, FC } from 'react';
import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper/modules';
import Image from 'next/image';
import StarRating from './StarRating';
import { siteConfig } from '../../../site.config';

const reviews = [
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'Really effective, but I wish the subscription was cheaper.',
    date: 'Wellington, New Zealand, 5 hours ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: `Incredibly impressive! Taking the IQ test with ${siteConfig.siteName} boosted my confidence.`,
    date: 'Brisbane, Australia, 5 days ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: `The questions were a bit tricky, but enjoyable! I trust the accuracy of ${siteConfig.siteName}'s results.`,
    date: 'London, United Kingdom, a week ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: `Incredibly impressive! Taking the IQ test with ${siteConfig.siteName} boosted my confidence.`,
    date: 'London, United Kingdom, a week ago',
  },
  {
    name: 'Jane Smith',
    verified: true,
    rating: 4,
    text: `The questions were a bit tricky, but enjoyable! I trust the accuracy of ${siteConfig.siteName}'s results.`,
    date: 'London, United Kingdom, a week ago',
  },
];

interface ReviewSliderProps {
  page: string;
}

const ReviewSlider: FC<ReviewSliderProps> = ({ page }) => {
  const swiperRef = useRef<SwiperRef | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    // Handle window resize event
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Set initial window width
    setWindowWidth(window.innerWidth);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handlePrevClick = () => {
    // Check if swiperRef is defined before trying to access swiper instance
    if (swiperRef.current) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNextClick = () => {
    // Check if swiperRef is defined before trying to access swiper instance
    if (swiperRef.current) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handleSlideChange = () => {
    if (swiperRef.current) {
      setCurrentSlide(swiperRef.current.swiper.realIndex); // Update current slide index
    }
  };

  const slidesPerView = windowWidth < 768 ? 1 : windowWidth < 1024 ? 2 : 3; // Adjust the number of slides based on the screen width

  return (
    <div className="pt-[20px] md:pt-[30px]">
      {page == 'checkout' ? (
        <div>
          <div className="font-segoe font-semibold text-[28px] leading-[28px] md:text-[39px] md:leading-[39px] tracking-tight text-[#001B36] text-center">
            Reviews
          </div>
          <div className="flex md:justify-center md:items-center flex-col gap-1 my-5">
            <div className="flex justify-center items-center gap-1">
              <div
                className="text-[#2C3345] font-semibold font-segoe text-base tracking-tight pt-[2px]"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                Excellent
              </div>
              <div className="flex text-[#F7B635] flex-row md:gap-0">
                <StarRating rating={4.58} size={24} />
              </div>
            </div>
            <div
              className="font-segoe text-[#0D0D0E] text-[14px] leading-[21px] text-center"
              style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
              <span className="font-semibold">4.58 </span>based on <span className="font-semibold">1046</span> reviews
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className="hidden md:block font-segoe font-semibold text-[28px] leading-[28px] md:text-[39px] md:leading-[39px] tracking-tight text-[#001B36] text-center">
            Reviews
          </div>
          <div className="flex justify-center items-center gap-1 py-3 md:py-0 -mt-8  mb-5 md:my-5">
            <div className="flex justify-start items-center">
              <div
                className="text-[#2C3345] font-semibold font-segoe text-base tracking-tight pt-[2px]"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                Excellent
              </div>
              <div className="flex text-[#F7B635] flex-row gap-0">
                <StarRating rating={4.58} size={24} />
              </div>
            </div>
            <div
              className="font-segoe text-[#0D0D0E] text-[14px] leading-[21px] text-center"
              style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
              <span className="font-semibold">4.58 </span>based on <span className="font-semibold">1046</span> reviews
            </div>
          </div>
        </div>
      )}
      <div className="flex flex-row gap-0 mx-auto md:m-0">
        <div className="flex justify-center items-center">
          <button className="flex flex-row justify-center items-center w-8 h-8 bg-white" onClick={handlePrevClick}>
            <Image
              width={20}
              height={20}
              className={'md:h-[20px] md:w-[20px]'}
              src={currentSlide === 0 ? '/images/checkout-v3/previous.svg' : '/images/checkout-v3/previous.svg'}
              alt="prev disabled"
            />
          </button>
        </div>
        <Swiper
          ref={swiperRef}
          slidesPerView={1}
          spaceBetween={20}
          // pagination={{ clickable: true }}
          navigation={false}
          modules={[Pagination, Navigation]}
          breakpoints={{
            640: { slidesPerView: 1 },
            768: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
          }}
          onSlideChange={handleSlideChange}>
          {reviews.map((review, index) => (
            <SwiperSlide key={index} className="p-2 flex flex-col gap-0 border shadow-[0_0_1px_0_#0000004D,_0_2px_10px_0_#0000000F] rounded-[14px] p-3">
              <div className="pt-3 px-3 pb-[5px] flex flex-col gap-y-1">
                <div className="flex flex-row gap-2 items-center gap-x-2">
                  <span className="font-segoe font-semibold md:text-[12px] text-[#0D0D0E] md:leading-[18px]">
                    {review.name}
                  </span>
                  <StarRating rating={review.rating} size={18} />
                </div>
                {review.verified && (
                  <span className="text-[#6DCAFF] flex items-center">
                    <Image
                      width={15}
                      height={15}
                      className={'md:h-[15px] md:w-[15px]'}
                      src="/images/checkout-v3/verify.svg"
                      alt="verified"
                    />
                    <span className="font-segoe font-normal md:text-[11px] text-[#0D0D0E] md:leading-[16px]">
                      Verified Customer
                    </span>
                  </span>
                )}
              </div>
              <div className="px-3 pb-3">
                <div className="font-segoe font-normal text-[#0D0D0E] md:text-[14px] md:leading-[21px]">
                  {review.text}
                </div>
              </div>
              <div
                className="p-3 font-raleway text-[11px] leading-[16px] w-full text-[#0D0D0E] tracking-0"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                {review.date}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        <div className="flex justify-center items-center">
          <button className="flex flex-row justify-center items-center w-8 h-8 bg-white" onClick={handleNextClick}>
            <Image
              width={20}
              height={20}
              className={'md:h-[20px] md:w-[20px]'}
              src={currentSlide === reviews.length - slidesPerView ? '/images/checkout-v3/next.svg' : '/images/checkout-v3/next.svg'}
              alt="next disabled"
            />
          </button>
        </div>
      </div>
      <div className="px-10 mt-5 flex justify-end">
        <Image width={20} height={20} className={'md:h-[20px] w-[124px]'} src="/images/checkout-v3/review.svg" alt="star" />
      </div>
    </div>
  );
};

export default ReviewSlider;
