'use client';

import { useState, useContext, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { z } from 'zod';
import { Loader2 } from 'lucide-react';
import SessionContext from '@/store/SessionContext';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';
import { getClickIdFromStorage } from '@/utils/getClickIdFromStorage';

const emailCheck = httpsCallable(functions, 'checkIfEmailExists');

const OrderForm = () => {
  const searchParams = useSearchParams();
  const { captureEvent, identifyUser } = usePostHogAnalytics();
  const { checkoutVersion, formData, updateFormData, paymentSystem } = useContext(SessionContext);

  const origin = searchParams.get('origin');
  const [messages, setMessages] = useState<{ email: string }>({ email: '' });
  const [optedIn, setOptedIn] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const router = useRouter();
  const clickId = useMemo(() => getClickIdFromStorage(), []);

  useEffect(() => {
    if (origin === 'questions') {
      captureEvent(PostHogEventEnum.ACCOUNT_CREATION_PAGE_VIEWED, {});
    }

    // eslint-disable-next-line
  }, [origin]);

  const onSubmit = async () => {
    setLoading(true);
    captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED, { email: formData.email, name: formData.name });
    const isValidEmail = z.string().email().safeParse(formData.email).success;
    if (!isValidEmail) setMessages(prev => ({ ...prev, email: 'Please enter a valid email address' }));

    if (!isValidEmail || !formData.name) {
      setLoading(false);
      captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_ERROR, { email: formData.email, name: formData.name });
      return;
    }

    const emailCheckResponse: any = await emailCheck({ email: formData.email });

    if (emailCheckResponse.data.exists) {
      setMessages({
        email: 'This email is already in use. Please log in or use a different email.',
      });
      setLoading(false);
      captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_ERROR, { email: formData.email });
      return;
    }

    captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_SUCCESS, { email: formData.email });
    identifyUser(formData.email, { email: formData.email, paymentSystem, checkoutVersion });

    router.push('/results');
  };

  return (
    //@ts-ignore
    <div className="pt-[10px] px-4 md:px-0 md:pt-[56px] max-w-[740px] mx-auto">
      <form
        className="p-4 flex flex-col gap-3 md:gap-y-[32px] items-center justify-center"
        onSubmit={e => e.preventDefault()}
        noValidate>
        <div className="block md:hidden font-inter font-bold text-[26px] leading-[32px] tracking-0 text-[#3F425E]">
          Discover Your <span className="text-primary">Love Style!</span>
        </div>
        <div className="hidden md:block font-inter font-bold text-[30px] leading-[36px] tracking-0 text-[#3F425E]">
          Discover Your <span className="text-primary">IQ Score...</span>
        </div>
        <div className="font-inter font-normal text-[17px] md:text-[22px] leading-5 md:leading-[32px] tracking-lg text-[#3F425E] text-center md:px-5">
          Enter your email to access your personalized IQ report. Understand your unique intellectual profile and take
          the first step towards boosting your cognitive skills.
        </div>
        <fieldset className="md:w-[488px] mx-auto flex flex-col gap-3 md:gap-y-5">
          <input
            type="email"
            name="email"
            placeholder="Email"
            value={formData.email}
            className="rounded-lg bg-[#F8F8F8] h-[45px] md:h-[64px] px-4 py-[10px] block w-full focus:border-primary text-[16px] leading-[24px]"
            required
            onChange={e => {
              updateFormData({ ...formData, [e.target.name]: e.target.value });
              setMessages({ email: '' });
            }}
          />
          {messages.email && (
            <span className="block mb-2 text-left pl-[10px] text-red-500 font-bold">{messages.email}</span>
          )}
          <div className="flex">
            <label className="checkbox-container1 flex flex-wrap items-center">
              <input className="h-3" type="checkbox" id="consent" name="consent" onClick={() => setOptedIn(!optedIn)} />
              <span className="checkmark1"></span>
            </label>
            <p className="text-[#3F425E] font-inter font-normal text-[14px] md:text-[16px] leading-[20px] pt-1">
              I accept the{' '}
              <a href="/terms-and-conditions" className="text-primary" target="_blank">
                {' '}
                Terms & Conditions
              </a>{' '}
              and{' '}
              <a target="_blank" href="/privacy" className="text-primary">
                Privacy Policy
              </a>
              .
            </p>
          </div>
          <button
            disabled={loading || !optedIn}
            onClick={onSubmit}
            className="w-full min-w-[80px] max-w-[400px] md:max-w-[488px] rounded-lg px-4 py-[9px] md:py-[10px] bg-primary flex justify-center items-center">
            {loading ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2 text-white" /> : ''}
            <span className="pt-1 md:text-[20px] leading-[22px] font-inter text-white">Continue</span>
          </button>
        </fieldset>
      </form>
    </div>
  );
};

export default OrderForm;
