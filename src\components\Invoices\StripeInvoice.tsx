'use client';

import React, { useContext } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { formatInvoiceDate } from '@/app/utils';
import InvoiceItem from '@/components/Invoices/InvoiceItem';
import { Link } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

const StripeInvoice: React.FC = () => {
  const t = useTranslations('invoices');
  const { stripeInvoices } = useContext(SessionContext);

  if (!stripeInvoices?.data?.length) {
    return null;
  }

  return (
    <>
      {stripeInvoices.data.map(invoice => {
        const date = formatInvoiceDate(invoice.effective_at, false);
        const status = invoice.paid ? t('status_values.paid') : t('status_values.not_paid');

        return (
          <InvoiceItem
            key={invoice.id}
            amount={invoice.total}
            currency={invoice.currency.toUpperCase()}
            status={status}
            date={date}>
            {invoice.hosted_invoice_url && (
              <Link href={invoice.hosted_invoice_url} className="cursor-pointer">
                <Image
                  src={'/images/external-link-outline.svg'}
                  alt={t('alt.external_link')}
                  width={18}
                  height={18}
                  className="w-[16px] h-[16px]"
                />
              </Link>
            )}
          </InvoiceItem>
        );
      })}
    </>
  );
};

export default StripeInvoice;
