import { ALLOWED_FIXED_PRICES } from '@/utils/constants';

/**
 * Enum for plan type.
 */
export enum PlanTypeEnum {
  Onetime = 'onetime',
  Subscription = 'subscription',
}

/**
 * Plan type: 'onetime' or 'subscription'
 */
export type PlanType = `${PlanTypeEnum}`; // 'onetime' | 'subscription'

/**
 * Enum for allowed currencies for fixed price plans
 */
export enum FixedCurrencyEnum {
  USD = 'USD',
  // EUR = 'EUR', // Add more as needed
}

/**
 * Fixed currency: 'USD' | 'EUR'
 */
export type FixedCurrency = `${FixedCurrencyEnum}`; // 'USD' | 'EUR'

/**
 * Allowed USD prices for fixed plans
 */
export type FixedPrice = (typeof ALLOWED_FIXED_PRICES)[FixedCurrencyEnum]['prices'][number];

/**
 * The only available onetime plan and its duration
 */
export type FixedPlanDuration = 30;

/**
 * Enum for subscription plans.
 */
export enum SubscriptionPlanEnum {
  Weekly = 'weekly',
  Monthly = 'monthly',
  Fix = 'fix',
}

/**
 * Subscription plans: 'weekly' or 'monthly'
 */
export type SubscriptionPlan = `${SubscriptionPlanEnum}`; // 'weekly' | 'monthly'
