/**
 * Valid locale prefix strategies
 */
export type LocalePrefix = 'always' | 'as-needed' | 'never';

/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  Es = 'es',
  Fr = 'fr',
  De = 'de',
  Pt = 'pt',
  El = 'el'
}

/** All available locales */
const allLocales: string[] = Object.values(Locale);

/** Environment-based locale configuration utility */
export const isMultiLanguageEnabled = process.env.NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE === 'true';

/** Helper function to check if a value is a valid locale */
export const isValidLocale = (value: string): value is Locale => {
  return Object.values(Locale).includes(value as Locale);
};

/**
 * Get locale prefix strategy from environment variable.
 * Defaults to 'never' if the value is invalid or undefined.
 */
export const getLocalePrefix = (): LocalePrefix => {
  const prefix = process.env.NEXT_PUBLIC_LOCALE_PREFIX;
  
  return (['always', 'as-needed', 'never'] as const).includes(prefix as LocalePrefix)
    ? (prefix as LocalePrefix)
    : 'never';
};

/** Get default locale from environment variable with fallback */
export const getDefaultLocale = (): Locale => {
  // Always return English as the default locale
  return Locale.En;
};

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = getDefaultLocale();

/** List of supported locales based on environment */
export const locales: string[] = isMultiLanguageEnabled ? allLocales : [defaultLocale];

/** The country that will be used when no match is found. */
export const defaultCountry: string = 'US';
