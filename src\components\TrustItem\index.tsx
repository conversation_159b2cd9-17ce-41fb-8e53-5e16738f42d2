import Image from 'next/image';
import { FC } from 'react';

interface TrustItemProps {
  imageSource?: String;
  title?: String;
  text?: String;
}

const TrustItem: FC<TrustItemProps> = ({ imageSource, title, text }) => {
  return (
    <div className="flex flex-row gap-4">
      <Image src={`/images/checkout-v3/${imageSource}.svg`} width={32} height={32} className="w-8 h-8" alt="graduate_cap" />
      <div className="flex flex-col gap-[6px]">
        <div className="font-segoe font-semibold text-[14px] leading-[21px] tracking-[0px] text-[#3F3F46]">{title}</div>
        <div className="font-segoe font-normal text-[12px] leading-[15px] tracking-[0px] text-[#545758]">{text}</div>
      </div>
    </div>
  );
};

export default TrustItem;
