import React from 'react';

type SecondaryLoveStyleProps = {
  title: string;
  description: string;
};

const SecondaryLoveStyle: React.FC<SecondaryLoveStyleProps> = ({ title, description }) => {
  return (
    <>
      <h2 className="font-raleway font-bold text-[#FF5D5D] text-[32px] md:text-[48px] leading-[36px] md:leading-[58px] tracking-[-0.03em] mb-3 md:mb-6">
        {title}
      </h2>
      <p className="font-raleway font-medium text-[#A290A5] text-[16px] md:text-[18px] leading-[24px] md:leading-[27px]">
        {description}
      </p>
    </>
  );
};

export default SecondaryLoveStyle;
