<svg width="101" height="101" viewBox="0 0 101 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bdii_4076_3617)">
<rect x="84.2246" y="79.6377" width="64.9892" height="64.9892" rx="10.8315" transform="rotate(-169.28 84.2246 79.6377)" fill="white" fill-opacity="0.12"/>
<rect x="83.8767" y="79.1273" width="64.1157" height="64.1157" rx="10.3948" transform="rotate(-169.28 83.8767 79.1273)" stroke="url(#paint0_linear_4076_3617)" stroke-width="0.873488"/>
<g clip-path="url(#clip0_4076_3617)">
<path d="M67.7341 37.0578C66.917 41.3738 62.7556 44.2116 58.4396 43.3945C54.1236 42.5774 51.2875 38.4149 52.1045 34.0989C52.9216 29.783 57.0828 26.9465 61.3987 27.7636C65.7147 28.5807 68.5512 32.7418 67.7341 37.0578Z" fill="white"/>
<path d="M46.3764 37.9773L44.9316 38.6108C44.3846 38.8508 44.1363 39.4869 44.375 40.0337C44.5239 40.3725 44.826 40.5987 45.1657 40.6631C45.3713 40.702 45.5925 40.6806 45.7979 40.5903L47.2437 39.9584C47.7908 39.7184 48.039 39.0823 47.8003 38.5355C47.5603 37.9884 46.9246 37.7389 46.3764 37.9773Z" fill="white"/>
<path d="M47.6567 33.2568C47.7678 32.6704 47.3827 32.1055 46.7963 31.9945L45.2665 31.7048C44.6801 31.5938 44.1152 31.9789 44.0041 32.5653C43.8931 33.1518 44.2782 33.7167 44.8646 33.8277L46.3944 34.1173C46.9808 34.2283 47.5457 33.8433 47.6567 33.2568Z" fill="white"/>
<path d="M48.6003 25.7547C48.1775 25.331 47.4955 25.3325 47.0724 25.7527C46.649 26.1741 46.6502 26.8574 47.0704 27.2805L48.1852 28.3973C48.3447 28.5566 48.5414 28.6571 48.7471 28.696C49.0854 28.7601 49.4491 28.6613 49.713 28.3993C50.1364 27.9778 50.1352 27.2945 49.7151 26.8714L48.6003 25.7547Z" fill="white"/>
<path d="M71.6725 42.7648C71.2497 42.3412 70.568 42.3413 70.1446 42.7628C69.7212 43.1843 69.7224 43.8675 70.1426 44.2907L71.2574 45.4074C71.4169 45.5668 71.6136 45.6672 71.8193 45.7062C72.1576 45.7702 72.5213 45.6714 72.7852 45.4094C73.2086 44.9879 73.2074 44.3046 72.7872 43.8815L71.6725 42.7648Z" fill="white"/>
<path d="M74.9838 37.3304L73.454 37.0408C72.8676 36.9298 72.3027 37.3148 72.1916 37.9013C72.0806 38.4877 72.4657 39.0526 73.0521 39.1636L74.5819 39.4532C75.1683 39.5643 75.7332 39.1792 75.8442 38.5928C75.9553 38.0063 75.5702 37.4414 74.9838 37.3304Z" fill="white"/>
<path d="M72.8437 33.2585C73.0493 33.2974 73.2705 33.2761 73.4759 33.1858L74.9217 32.5538C75.4688 32.3138 75.717 31.6777 75.4783 31.1309C75.2394 30.5854 74.6036 30.3359 74.0555 30.5743L72.6096 31.2063C72.0625 31.4463 71.8143 32.0823 72.053 32.6292C72.2032 32.9682 72.504 33.1942 72.8437 33.2585Z" fill="white"/>
<path d="M63.6325 44.9772C61.912 45.6589 59.9913 45.8876 58.0435 45.5189C56.0957 45.1502 54.3905 44.235 53.0393 42.9718C49.5087 43.6035 46.5612 46.3371 45.8541 50.072L45.6175 51.3218C45.4412 52.2532 45.9433 53.1797 46.8243 53.5279C49.3407 54.5224 52.4302 55.51 56.023 56.1901C59.6159 56.8703 62.8527 57.0804 65.5583 57.0745C66.5069 57.0727 67.3119 56.3922 67.488 55.4622L67.7246 54.2123C68.4317 50.4775 66.6891 46.8548 63.6319 44.9771L63.6325 44.9772Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_bdii_4076_3617" x="-19.6309" y="-36.3057" width="155.943" height="155.943" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4076_3617"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.72714" dy="8.83102"/>
<feGaussianBlur stdDeviation="6.98791"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0174417 0 0 0 0 0.031694 0 0 0 0 0.37375 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_4076_3617" result="effect2_dropShadow_4076_3617"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4076_3617" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.25"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_4076_3617"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.25"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_4076_3617" result="effect4_innerShadow_4076_3617"/>
</filter>
<linearGradient id="paint0_linear_4076_3617" x1="116.719" y1="79.6377" x2="116.719" y2="144.627" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.1"/>
</linearGradient>
<clipPath id="clip0_4076_3617">
<rect width="32.4946" height="32.4946" fill="white" transform="translate(45.834 22.6797) rotate(10.72)"/>
</clipPath>
</defs>
</svg>
