import Image from "next/image";
import React, { ReactNode } from "react";

type IconWithDescriptionProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithDescription: React.FC<IconWithDescriptionProps> = ({
  source,
  alt,
  children,
}) => {
  return (
    <div className="flex flex-row gap-[16px] items-center">
      <Image
        src={source}
        alt={alt}
        width={48}
        height={48}
        className="w-[40px] h-[40px] md:w-[48px] md:h-[48px]"
      />
      <label className="text-[#8C8492] font-ppmori text-[14px] leading-[20px] md:text-[16px] md:leading-[24px] font-normal">
        {children}
      </label>
    </div>
  );
};

export default IconWithDescription;
