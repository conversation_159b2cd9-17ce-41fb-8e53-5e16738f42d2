'use client';

import React, { useContext, useEffect } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Link, useRouter } from '@/lib/i18n/routing';
import { useSearchParams } from 'next/navigation';
import { sendGTMEvent } from '@next/third-parties/google';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import { addFormDataToSessionDb } from '@/services/session';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { additionalinsights } from './AdditionalInsights';
import { mainlovetext } from './MainLoveStyleText';
import { secondarylovetext } from './SecondaryLoveStyleText';
import Account from '@/components/LoveLanguages/Account';
import BoxWithInsights from '@/components/LoveLanguages/BoxWithInsights';
import Footer from '@/components/LoveLanguages/GFooter';
import Logo from '@/components/LoveLanguages/Logo';
import MainLoveStyle from '@/components/LoveLanguages/MainLoveStyle';
import PersonalGrowthChallenge from '@/components/LoveLanguages/PersonalGrowthChallenge';
import PersonalGrowthChallengeSecondaryFocus from '@/components/LoveLanguages/PersonalGrowthChallengeSecondaryFocus';
import PieChart from '@/components/LoveLanguages/Piechart';
import SecondaryLoveStyle from '@/components/LoveLanguages/SecondaryLoveStyle';

// Update to fix type issue
type Score = {
  category: string;
  sum: number;
  percentage: string; // This is likely the issue, change to number
};

const Results: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('love-languages.results');
  const showResults = searchParams.get('showResults');
  const isShowResults = showResults === 'true';
  const { results, resetTestState, fetchAllSessionData, checkoutId, prices, resetAnswers } = useContext(SessionContext);
  const sourceFromUrl = searchParams.get('source');
  const { time } = useContext(UiContext);

  useEffect(() => {
    if (sourceFromUrl === 'qr') return;

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on Love Languages Results page: Event not sent');
      return;
    }

    //TODO: Move this GTM event to the checkout page when payment is successful
    sendGTMEvent({
      event: 'purchase',
      ecommerce: {
        currency: prices.currency.toUpperCase(),
        value: prices.trial.amount,
        transaction_id: checkoutId,
        items: [
          {
            item_name: 'LL test',
            item_id: 'lltest',
            price: prices.trial.amount.toString(),
            quantity: 1,
          },
        ],
      },
    });

    resetAnswers();
  }, [sourceFromUrl, checkoutId, prices]);

  useEffect(() => {
    if (results.length === 0) {
      router.push('/love-languages/test');
    }
    const fetchData = async () => {
      addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'love-languages', loveScore: results });
    };

    !isShowResults && fetchData();
  }, []);

  // Fixing the type by converting percentage to number
  const formattedResults = results.map((result: Score) => ({
    ...result,
    percentage: parseFloat(result.percentage), // Ensure percentage is a number
  }));

  return (
    <div className="relative w-full overflow-x-hidden">
      <Image
        src="/images/love-languages/result-gradient-desktop.png"
        alt={t('alt_text.desktop_gradient')}
        width={1128}
        height={829}
        className="absolute left-[calc(50%-168px)] top-[0px] w-[1128px] h-auto md:h-auto hidden md:block z-10"
        priority
      />
      <div className="max-w-[1440px] mx-auto relative">
        <Image
          src="/images/love-languages/result-photo-desktop.png"
          alt={t('alt_text.desktop_photos')}
          width={626}
          height={641}
          className="absolute right-[61.08px] top-[105px] w-[620.27px] h-auto md:h-auto hidden md:block z-10"
        />
      </div>
      <header className="max-w-[1440px] mx-auto flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px] z-20">
        <Logo />
        <Account />
      </header>
      <main>
        <div className="max-w-[1440px] mx-auto">
          <div className="md:flex md:flex-col md:h-[659px] md:justify-center px-[16px] md:pl-[82px] md:max-w-[715px] mt-5 md:mt-0 mb-[38px] md:mb-[0px] md:pb-[122px] z-20">
            <p className="font-raleway font-medium text-[#0E2432] text-[16px] md:text-[18px] leading-[20px] md:leading-[24px] mb-[15px] md:mb-[21px] z-20">
              {t('main_section.unsure_text')}{' '}
              <Link
                href="/love-languages/test"
                className="underline font-semibold tracking-[-0.03em]"
                onClick={() => resetTestState()} // Call the reset function
              >
                {t('main_section.retake_link')}
              </Link>
            </p>
            <p className="font-raleway font-bold text-[#0E2432] text-[24px] md:text-[44px] leading-[34px] md:leading-[54px] tracking-[-0.03em] z-20">
              {t('main_section.main_love_style_heading')}
            </p>
            {results && <MainLoveStyle title={results[0]?.category} description={mainlovetext[results[0]?.category]} />}
          </div>
        </div>
        <div className="relative flex items-center justify-center md:hidden top-[-60px]">
          <Image
            src="/images/love-languages/result-photo-mobile.png"
            alt={t('alt_text.mobile_photos')}
            width={341}
            height={349}
            className="absolute w-full h-auto pl-[23px] pr-[33px] z-10"
          />
          <Image
            src="/images/love-languages/result-gradient-mobile.png"
            alt={t('alt_text.mobile_gradient')}
            width={361}
            height={474}
            className="w-full pl-[23px] h-auto z-0 mt-[-10px]"
          />
        </div>
        <div className="max-w-[1440px] mx-auto md:px-[82px] mt-[-60px] md:mt-0">
          <div className="px-[16px] py-[40px] flex flex-col-reverse md:flex-row justify-center items-center gap-[32px] md:gap-[95px] md:px-[96px] md:py-[63px] bg-[#FF5D5D0D]">
            <Image
              src="/images/love-languages/love-message.png"
              alt={t('alt_text.desktop_gradient')}
              width={240}
              height={240}
              className="md:w-[240px] h-auto"
            />
            <div>
              <h2 className="font-raleway font-bold text-[#0E2432] text-[20px] md:text-[40px] leading-[24px] md:leading-[50px] tracking-[-0.03em] mb-[6px] md:mb-[4px]">
                {t('main_section.secondary_love_style_heading')}
              </h2>
              <SecondaryLoveStyle
                title={results?.[1]?.category}
                description={secondarylovetext[results?.[1]?.category]}
              />
            </div>
          </div>
        </div>
        <div className="max-w-[1440px] mx-auto flex flex-col justify-center items-center px-4 md:px-[82px] py-[60px] md:py-[80px]">
          <h2 className="font-raleway font-bold text-[32px] md:text-[40px] leading-[36px] md:leading-[50px] tracking-[-0.03em] text-center text-[#0E2432] mb-3 md:mb-4">
            {t.rich('breakdown_section.heading', {
              br: () => <br />,
            })}
          </h2>
          <p className="text-center md:max-w-[629px] text-[#828E98] font-raleway font-medium text-[16px] md:text-[18px] leading-[24px] md:leading-[27px] mb-[32px] md:mb-[44px]">
            {t('breakdown_section.description')}
          </p>
          <PieChart results={formattedResults} />
        </div>
        <div className="w-full bg-[#F1FAFF]">
          <div className="max-w-[1024px] mx-auto px-[16px] md:px-[93px] py-[60px] md:py-[80px] flex flex-col justify-center">
            <h2 className="font-raleway font-bold text-[#0E2432] text-[32px] md:text-[40px] leading-[36px] md:leading-[50px] tracking-[-0.03em] text-center mb-[26px] md:mb-[40px]">
              {t('personal_growth_challenge.heading')}
            </h2>
            <div className="bg-white px-4 py-[26px] mb-5 md:mb-8 md:p-10 border-[#5DC4FF] border-[3px] rounded-[12px]">
              <h3 className="font-raleway font-bold text-[24px] md:text-[36px] leading-[28px] md:leading-[44px] tracking-[-0.03em] text-center text-[#0E2432] mb-[24px] md:mb-[36px]">
                {t('personal_growth_challenge.primary_focus')}
              </h3>
              {results && <PersonalGrowthChallenge category={results[0]?.category} />}
            </div>
            <div className="bg-white px-4 py-[26px] md:p-10 rounded-[12px]">
              <h3 className="font-raleway font-bold text-[24px] md:text-[36px] leading-[28px] md:leading-[44px] tracking-[-0.03em] text-center text-[#0E2432] mb-[24px] md:mb-[27px]">
                {t('personal_growth_challenge.secondary_focus')}
              </h3>
              <PersonalGrowthChallengeSecondaryFocus category={results?.[1]?.category} />
            </div>
          </div>
        </div>
        <div className="md:max-w-[1440px] md:mx-auto py-[60px] md:px-[82px] md:py-[80px] bg-[#FDFEFF]">
          <p className="font-raleway font-bold text-[#0E2432] text-[32px] md:text-[40px] leading-[36px] md:leading-[50px] tracking-[-0.03em] text-center mb-[26px] md:mb-[40px]">
            {t('additional_insights.heading')}
          </p>
          <div className="relative flex flex-col mx-4 md:px-[108px] gap-3 md:gap-4">
            <Image
              src="/images/love-languages/result-gradient-insight-desktop.png"
              alt={t('alt_text.insight_gradient_desktop')}
              width={903}
              height={477}
              className="absolute w-[930px] inset-0 m-auto h-auto hidden md:block z-10"
            />
            <Image
              src="/images/love-languages/result-gradient-insight-mobile.png"
              alt={t('alt_text.insight_gradient_mobile')}
              width={375}
              height={777}
              className="absolute bottom-[-120px] w-full h-[calc(100%+196px)] block md:hidden z-10"
            />
            {results && (
              <>
                <BoxWithInsights
                  color={additionalinsights[results[0]?.category]?.color}
                  source={additionalinsights[results[0]?.category]?.source}
                  alt={additionalinsights[results[0]?.category]?.alt}
                  heading={additionalinsights[results[0]?.category]?.heading}
                  subheading={additionalinsights[results[0]?.category]?.subheading}
                  description={additionalinsights[results[0]?.category]?.description}
                />
                <BoxWithInsights
                  color={additionalinsights[results[1]?.category]?.color}
                  source={additionalinsights[results[1]?.category]?.source}
                  alt={additionalinsights[results[1]?.category]?.alt}
                  heading={additionalinsights[results[1]?.category]?.heading}
                  subheading={additionalinsights[results[1]?.category]?.subheading}
                  description={additionalinsights[results[1]?.category]?.description}
                />
              </>
            )}
          </div>
        </div>
        <Footer />
      </main>
    </div>
  );
};

export default Results;
