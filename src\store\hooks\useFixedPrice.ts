import { useState } from 'react';
import lscache from 'lscache';
import { FixedPrice } from '@/types/plan-types';
import { ALLOWED_FIXED_PRICES } from '@/utils/constants';

/**
 * React hook to manage the current fixed price for payments.
 *
 * Initializes the fixed price state from local storage (key: 'se-cp_fp') if available,
 * otherwise falls back to the default price (ALLOWED_FIXED_PRICES.USD.default_price).
 *
 * @example
 * const { fixedPrice, setFixedPrice } = useFixedPrice();
 * setFixedPrice(1999);
 */
export function useFixedPrice() {
  const default_price = ALLOWED_FIXED_PRICES.USD.default_price;
  const [fixedPrice, setFixedPrice] = useState<FixedPrice>(lscache.get('se-cp_fp') || default_price);
  return { fixedPrice, setFixedPrice };
}
