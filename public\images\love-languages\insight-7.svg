<svg width="224" height="92" viewBox="0 0 224 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_3580_688)">
<rect x="19.8066" y="24.4175" width="48.2815" height="48.2815" rx="7.10023" transform="rotate(-5.11 19.8066 24.4175)" fill="white"/>
<g clip-path="url(#clip0_3580_688)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M49.5584 51.023C50.1961 50.4865 50.5614 49.8096 50.5005 49.1281L50.5017 49.128C50.3087 47.6518 49.6022 46.8328 48.6058 45.6774C47.8992 44.8572 47.0553 43.8797 46.1396 42.4133C45.0016 44.1501 45.3152 45.5422 46.0263 47.5454C46.1857 47.9765 45.7604 48.3378 45.3796 48.1718C44.3573 47.7239 43.8406 47.2943 43.0004 46.3994C42.5986 47.2406 42.5139 48.2035 42.5899 49.0533C42.879 52.3009 47.2318 52.9772 49.5584 51.023ZM48.4279 56.9648C47.2622 56.0173 45.4619 56.9116 45.5947 58.3968C45.7274 59.8808 47.6579 60.4427 48.6371 59.3034C49.2427 58.5989 49.149 57.5507 48.4279 56.9648ZM48.5578 52.8095L48.8494 56.0694C47.7806 55.3475 46.3378 55.4765 45.4141 56.3766L45.1258 53.1533C46.2593 53.1532 47.4487 53.0551 48.5578 52.8095ZM40.8723 55.5393C40.7196 54.051 38.8144 53.4026 37.8567 54.5498C36.8967 55.6984 37.8762 57.4556 39.3656 57.3432C40.2915 57.2726 40.9673 56.4654 40.8723 55.5393ZM43.5793 53.079L41.8015 55.2061C41.5563 53.9383 40.446 53.0103 39.1549 52.994L39.9165 52.0828C40.969 52.6231 42.2143 52.9449 43.5793 53.079ZM53.5466 53.1882C52.5579 54.3711 53.4558 56.1027 54.9417 55.9503C56.4301 55.7977 57.0784 53.8924 55.9311 52.9335C55.2125 52.334 54.1506 52.4656 53.5466 53.1882ZM52.8634 49.6963C52.2851 50.1885 51.663 50.5992 50.9198 50.9658C51.3263 50.3816 51.5296 49.7216 51.4688 49.0413L51.4676 49.0414C51.1514 46.0645 48.5214 44.8161 46.6072 41.3134C46.425 40.9807 46.002 40.971 45.7947 41.2323C44.2815 43.1392 44.1482 44.6374 44.7365 46.6991C44.329 46.3853 43.9219 45.9813 43.2766 45.2593L43.2754 45.2594C43.251 45.2323 43.2232 45.208 43.1932 45.1863C42.4119 44.6363 41.8372 46.6692 41.7476 47.0591C41.3087 48.959 41.6925 50.8635 43.211 52.0577C40.3001 51.6652 37.8436 50.256 37.5487 46.9588C37.2328 43.4257 44.859 38.75 44.5371 32.0938C49.1181 34.7625 46.5544 37.9799 49.4202 41.0058C49.7045 41.3062 50.2022 41.1274 50.2537 40.7348C50.3663 39.8646 50.5329 38.9309 50.891 38.2559C51.7953 41.6147 54.6538 42.4279 54.92 45.4055C55.057 46.9367 54.292 48.4388 52.936 49.6338C52.9095 49.6521 52.8858 49.6735 52.8634 49.6963ZM53.2147 50.6642L54.3739 51.633C53.1061 51.8781 52.178 52.9885 52.1617 54.2796L49.9384 52.4213C51.1886 51.9935 52.3011 51.3889 53.2147 50.6642Z" fill="#5E5BFC"/>
</g>
</g>
<g filter="url(#filter1_d_3580_688)">
<rect width="48.28" height="48.28" rx="10" transform="matrix(0.999041 0.0437938 0.0437938 -0.999041 65.1963 69.3721)" fill="white"/>
<g clip-path="url(#clip1_3580_688)">
<path d="M101.297 35.6518L100.214 35.6044L100.309 33.4397L98.1449 33.3448L97.9076 38.7564L95.7429 38.6615L95.8853 35.4146L85.0621 34.9402L85.157 32.7755L82.9923 32.6806L82.7551 38.0921L80.5907 37.9973L80.733 34.7504L79.6509 34.7029C78.4596 34.6507 77.4435 35.5824 77.3913 36.7728L76.5373 56.254C76.4851 57.444 77.4158 58.4611 78.6071 58.5133L100.253 59.4622C101.443 59.5143 102.46 58.5827 102.512 57.3924L103.366 37.9111C103.419 36.7211 102.487 35.704 101.297 35.6518ZM95.4861 49.285L89.6197 54.6587L84.2464 48.7923C82.8706 47.2941 82.9742 44.9603 84.4746 43.5849C85.976 42.2106 88.3086 42.3129 89.6833 43.8133L90.0763 44.2436L90.5057 43.8493C92.0074 42.475 94.3377 42.5771 95.7144 44.0776C97.0884 45.579 96.9861 47.9129 95.4861 49.285Z" fill="#605DFF"/>
</g>
</g>
<g filter="url(#filter2_d_3580_688)">
<rect x="108.545" y="23.2524" width="48.28" height="48.28" rx="10" transform="rotate(-2.51 108.545 23.2524)" fill="white"/>
<g clip-path="url(#clip2_3580_688)">
<path d="M138.333 58.5482C138.654 59.095 139.014 59.5639 139.405 59.9306L143.233 59.7628C144.605 58.2253 145.394 55.2555 145.256 52.1163C145.17 50.1457 144.035 47.894 143.124 46.0851C142.831 45.5062 142.559 44.9609 142.359 44.4998C142.292 44.3453 142.235 44.194 142.185 44.0449L139.076 44.1811C139.039 44.3341 138.995 44.4898 138.943 44.6495C138.784 45.1252 138.557 45.696 138.319 46.2957C138.279 46.3946 138.239 46.4948 138.199 46.5974C138.887 48.1418 139.464 49.7944 139.533 51.3634C139.651 54.057 139.207 56.5982 138.336 58.5479L138.333 58.5482Z" fill="#605DFF"/>
<path d="M139.017 41.2261L141.981 41.0962C142.006 40.934 142.03 40.7755 142.055 40.6169C142.147 40.052 142.233 39.5193 142.208 38.9533C142.082 36.0779 141.159 35.6965 140.262 35.7257C139.366 35.7748 138.48 36.2356 138.606 39.1112C138.631 39.6796 138.764 40.2027 138.904 40.7551C138.943 40.9071 138.981 41.0629 139.02 41.226L139.017 41.2261Z" fill="#605DFF"/>
<path d="M141.95 43.1105C141.893 42.7353 141.878 42.383 141.891 42.0453L139.195 42.1635C139.234 42.4988 139.253 42.8511 139.229 43.2297L141.955 43.1103L141.95 43.1105Z" fill="#605DFF"/>
<path d="M127.369 43.7492C127.312 43.374 127.296 43.0216 127.31 42.684L124.614 42.8022C124.653 43.1375 124.672 43.4897 124.648 43.8684L127.374 43.7489L127.369 43.7492Z" fill="#605DFF"/>
<path d="M128.65 60.406C129.037 59.9733 129.376 59.4197 129.663 58.7834C128.669 56.9338 128.033 54.5003 127.918 51.8718C127.852 50.3604 128.249 48.7303 128.77 47.1797C128.695 47.0256 128.615 46.8753 128.542 46.7235C128.249 46.1447 127.977 45.5994 127.777 45.1382C127.71 44.9837 127.653 44.8325 127.603 44.6833L124.494 44.8196C124.457 44.9725 124.413 45.1282 124.361 45.288C124.202 45.7636 123.975 46.3345 123.737 46.9341C122.988 48.8158 122.055 51.1584 122.141 53.1288C122.279 56.2681 123.326 59.1574 124.826 60.5689L128.654 60.4011L128.65 60.406Z" fill="#605DFF"/>
<path d="M124.436 41.8658L127.4 41.7359C127.425 41.5736 127.449 41.4151 127.474 41.2566C127.566 40.6916 127.652 40.1589 127.627 39.5929C127.501 36.7175 126.578 36.3362 125.681 36.3654C124.785 36.4145 123.899 36.8753 124.025 39.7508C124.05 40.3193 124.183 40.8424 124.323 41.3947C124.362 41.5468 124.399 41.7026 124.439 41.8657L124.436 41.8658Z" fill="#605DFF"/>
<path d="M134.79 40.4098L131.681 40.5461C131.704 40.8415 131.707 41.1465 131.681 41.4687L134.874 41.3287C134.82 41.01 134.797 40.706 134.793 40.4096L134.79 40.4098Z" fill="#605DFF"/>
<path d="M131.548 39.6075L134.842 39.4631C134.878 39.1282 134.932 38.8011 134.984 38.4777C135.089 37.8396 135.185 37.2376 135.156 36.593C135.012 33.289 133.885 32.8736 132.904 32.9066C131.923 32.9618 130.837 33.4732 130.982 36.776C131.01 37.4206 131.159 38.0119 131.318 38.6384C131.399 38.9559 131.479 39.2772 131.547 39.6076L131.548 39.6075Z" fill="#605DFF"/>
<path d="M131.922 60.2617L136.278 60.0707C137.839 58.3367 138.743 54.9647 138.587 51.4068C138.49 49.1747 137.21 46.6365 136.182 44.5966C135.854 43.9466 135.543 43.3305 135.32 42.8138C135.239 42.6254 135.168 42.4428 135.11 42.2621L131.528 42.4191C131.482 42.6032 131.431 42.7924 131.367 42.9871C131.188 43.5214 130.934 44.1624 130.664 44.8384C129.819 46.9605 128.766 49.6009 128.864 51.8331C129.02 55.3935 130.215 58.6697 131.922 60.2617Z" fill="#605DFF"/>
</g>
</g>
<g filter="url(#filter3_d_3580_688)">
<rect width="48.28" height="48.28" rx="10" transform="matrix(0.996195 0.0871557 0.0871557 -0.996195 151.893 68.2563)" fill="white"/>
<g clip-path="url(#clip3_3580_688)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M165.896 40.1037C165.441 40.7114 165.367 41.5586 165.219 43.2532L164.488 51.6038C164.315 53.5761 164.229 54.5623 164.663 55.2969C164.804 55.5341 164.98 55.7473 165.185 55.9288C165.822 56.4906 166.794 56.5756 168.736 56.7456L185.313 58.1958C187.255 58.3658 188.226 58.4507 188.951 58.0081C189.185 57.865 189.396 57.6857 189.575 57.4764C190.13 56.8283 190.216 55.8422 190.389 53.8698L191.12 45.5193C191.268 43.8247 191.342 42.9775 190.999 42.3C190.888 42.0798 190.747 41.8767 190.58 41.6967C190.065 41.1428 189.254 40.9315 187.632 40.5089L183.815 39.5142L183.693 39.0653C183.322 37.7067 183.136 37.0274 182.696 36.5532C182.551 36.3981 182.389 36.2613 182.213 36.146C181.672 35.7933 180.98 35.7328 179.596 35.6117L178.287 35.4972C176.903 35.3761 176.211 35.3155 175.618 35.569C175.424 35.6519 175.24 35.7584 175.071 35.8862C174.555 36.2767 174.255 36.9134 173.653 38.1869L173.455 38.6078L169.523 38.9246C167.852 39.0591 167.017 39.1263 166.414 39.5825C166.218 39.7307 166.044 39.9062 165.896 40.1037ZM189.514 43.9879C189.45 44.7141 188.819 45.2521 188.104 45.1896C187.388 45.127 186.86 44.4875 186.924 43.7613C186.987 43.0351 187.618 42.497 188.334 42.5596C189.049 42.6222 189.577 43.2617 189.514 43.9879ZM180.427 51.9426C182.355 50.9632 184.2 50.0261 184.42 47.5146C184.765 43.5697 180.055 42.7183 178.098 45.1977C176.601 42.4168 171.815 42.4366 171.47 46.3816C171.25 48.8931 172.904 50.1364 174.633 51.4357C175.62 52.1773 176.631 52.937 177.331 53.9613C178.199 53.0743 179.327 52.5015 180.427 51.9426Z" fill="#605DFF"/>
<path d="M169.486 36.4762C169.326 36.6601 169.301 36.9403 169.252 37.5006L169.197 38.1357L172.608 37.8695L172.615 37.7948C172.664 37.2345 172.688 36.9543 172.563 36.7454C172.522 36.6779 172.471 36.6173 172.412 36.5657C172.228 36.4058 171.948 36.3813 171.387 36.3323L170.715 36.2734C170.155 36.2244 169.875 36.1999 169.666 36.3254C169.598 36.3659 169.537 36.4168 169.486 36.4762Z" fill="#605DFF"/>
</g>
</g>
<defs>
<filter id="filter0_d_3580_688" x="0.410156" y="0.720703" width="91.1826" height="91.1826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0725667 0 0 0 0 0 0 0 0 0 0.518333 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3580_688"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3580_688" result="shape"/>
</filter>
<filter id="filter1_d_3580_688" x="45.624" y="1.56641" width="89.4922" height="89.4917" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0725667 0 0 0 0 0 0 0 0 0 0.518333 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3580_688"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3580_688" result="shape"/>
</filter>
<filter id="filter2_d_3580_688" x="88.9727" y="1.56641" width="89.4922" height="89.4917" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0725667 0 0 0 0 0 0 0 0 0 0.518333 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3580_688"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3580_688" result="shape"/>
</filter>
<filter id="filter3_d_3580_688" x="132.726" y="0.993164" width="90.6387" height="90.6382" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0725667 0 0 0 0 0 0 0 0 0 0.518333 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3580_688"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3580_688" result="shape"/>
</filter>
<clipPath id="clip0_3580_688">
<rect width="26" height="28" fill="white" transform="translate(31.8047 33.2129) rotate(-5.11)"/>
</clipPath>
<clipPath id="clip1_3580_688">
<rect width="28" height="28" fill="white" transform="translate(76.6201 31.4004) rotate(2.51)"/>
</clipPath>
<clipPath id="clip2_3580_688">
<rect width="27.0562" height="28" fill="white" transform="translate(119.292 32.918) rotate(-2.51)"/>
</clipPath>
<clipPath id="clip3_3580_688">
<rect width="28" height="28" fill="white" transform="translate(165.166 31.145) rotate(5)"/>
</clipPath>
</defs>
</svg>
