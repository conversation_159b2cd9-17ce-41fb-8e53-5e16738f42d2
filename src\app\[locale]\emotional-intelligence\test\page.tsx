'use client';
import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from '@/lib/i18n/routing';
import ProgressBar from '@/components/EmotionalIntelligence/Test/ProgressBar';
import Logo from '@/components/EmotionalIntelligence/Landing/Logo';
import ButtonWithAnswer from '@/components/EmotionalIntelligence/Test/ButtonWithAnswer';
import categoriesAndQuestions from './categoriesAndQuestions';
import SessionContext from '@/store/SessionContext';
import { UserContext } from '@/store/UserContext';

const Test: React.FC = () => {
  const { user } = useContext(UserContext);
  const router = useRouter();
  const {
    emotionalStage: contextStage,
    emotionalClickedButton: contextClickedButton,
    emotionalTestAnswers: contextTestAnswers,
    updateEmotionalStage,
    updateEmotionalClickedButton,
    updateEmotionalTestAnswers,
    updateEmotionalScores,
  } = useContext(SessionContext);

  const categories = Object.keys(categoriesAndQuestions);
  const questions = categories.flatMap(category => categoriesAndQuestions[category]);
  const totalStages = questions.length;

  const [stage, setStage] = useState<number>(contextStage || 1);
  const [clickedButton, setClickedButton] = useState<string | null>(contextClickedButton || null);
  const [answers, setAnswers] = useState<number[]>(contextTestAnswers || new Array(totalStages).fill(0));

  const progress = (stage / totalStages) * 100;

  const handleNext = (answer: number) => {
    const updatedAnswers = [...answers];
    updatedAnswers[stage - 1] = answer;
    setAnswers(updatedAnswers);
    setClickedButton(null);

    if (stage < totalStages) {
      setStage(stage + 1);
    }
  };

  const handleBack = () => {
    if (stage > 1) {
      const previousAnswer = answers[stage - 2];
      setClickedButton(previousAnswer.toString() || null);
      setStage(stage - 1);
    }
  };

  const currentQuestion = questions[stage - 1];
  const isLastAnswerSelected = answers.every(answer => answer > 0);

  useEffect(() => {
    if (answers[stage - 1] > 0) {
      setClickedButton(answers[stage - 1].toString());
    }
  }, [stage, answers]);

  useEffect(() => {
    updateEmotionalStage(stage);
    updateEmotionalClickedButton(clickedButton);
    updateEmotionalTestAnswers(answers);
  }, [stage, clickedButton, answers, updateEmotionalStage, updateEmotionalClickedButton, updateEmotionalTestAnswers]);

  const calculateCategoryTotals = () => {
    const emotionalScores: { [key: string]: number } = {};
    let questionIndex = 0;

    categories.forEach(category => {
      emotionalScores[category] = categoriesAndQuestions[category].reduce((sum, _) => {
        const score = answers[questionIndex] || 0;
        questionIndex++;
        return sum + score;
      }, 0);
    });

    updateEmotionalScores(emotionalScores);
  };

  return (
    <div className="m-[auto]">
      <header className="w-full h-[60px] md:h-[110px] flex justify-center items-center pt-[4px] md:pt-[12px]">
        <Logo />
      </header>
      <ProgressBar progress={progress} />

      <main className="max-w-[343px] md:max-w-[619px] mx-auto mt-[32px] md:mt-[60px]">
        <span className="font-ppmori text-[20px] font-semibold leading-[26px] md:leading-[30px]">
          {currentQuestion}
        </span>
        <div className="mt-[20px] md:mt-[24px] flex flex-col gap-[12px] md:gap-4">
          <ButtonWithAnswer
            label="Strongly Agree"
            src="/images/emotional-intelligence/test/thumb-up.svg"
            alt="Strongly Agree"
            onClick={() => handleNext(5)}
            isClicked={clickedButton === '5'}
          />
          <ButtonWithAnswer
            label="Agree"
            src="/images/emotional-intelligence/test/thumb-up.svg"
            alt="Agree"
            onClick={() => handleNext(4)}
            isClicked={clickedButton === '4'}
          />
          <ButtonWithAnswer
            label="Neutral"
            src="/images/emotional-intelligence/test/face.svg"
            alt="Neutral"
            onClick={() => handleNext(3)}
            isClicked={clickedButton === '3'}
          />
          <ButtonWithAnswer
            label="Disagree"
            src="/images/emotional-intelligence/test/thumb-up.svg"
            alt="Disagree"
            onClick={() => handleNext(2)}
            isClicked={clickedButton === '2'}
          />
          <ButtonWithAnswer
            label="Strongly Disagree"
            src="/images/emotional-intelligence/test/thumb-up.svg"
            alt="Strongly Disagree"
            onClick={() => handleNext(1)}
            isClicked={clickedButton === '1'}
          />
        </div>

        <div className="mt-[32px] md:mt-[40px] flex justify-between">
          {stage > 1 && (
            <button
              className="p-auto w-[140px] md:w-[150px] h-[48px] md:h-[58px] shadow-[0px_2px_14px_0px_#4100511A] rounded-[10px] font-ppmori font-bold text-[16px] md:text-xl tracking-[-0.03em] text-[#8C36D0]"
              onClick={handleBack}>
              Back
            </button>
          )}
          {stage === totalStages && isLastAnswerSelected && (
            <button
              className="p-auto w-[140px] md:w-[150px] h-[48px] md:h-[58px] bg-[#8C36D0] border border-[#8C36D0] shadow-[0px_1px_1px_0px_#C900EB] rounded-[10px] font-ppmori font-semibold text-[16px] md:text-[20px] text-white"
              onClick={() => {
                calculateCategoryTotals();
                user ? router.push('/emotional-intelligence/results') : router.push('/emotional-intelligence/checkout');
              }}>
              Submit
            </button>
          )}
        </div>
      </main>
    </div>
  );
};

export default Test;
