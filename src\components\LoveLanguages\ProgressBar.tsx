import React from "react";

interface ProgressBarProps {
  progress: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
  return (
    <div className="w-full bg-blue-50 h-[4px] md:h-[12px] relative">
      <div
        className="transition-all ease-in-out border-[2px] md:border-[6px] border-[#5DC4FF]"
        style={{
          width: `${progress}%`,
        }}
      ></div>
    </div>
  );
};

export default ProgressBar;
