'use client';

import { useEffect, useContext, useMemo } from 'react';
import { useStopwatch } from 'react-timer-hook';
import UiContext from '@/store/UiContext';
import { formatDate } from '@/utils/date';

const CompletedByNumberTimer = () => {
  const { completedByNumber, updateCompletedByNumber, today, updateToday } = useContext(UiContext);

  const { seconds } = useStopwatch({ autoStart: true });

  const realToday = useMemo(() => formatDate(new Date()), []);

  useEffect(() => {
    if (today !== realToday) updateToday(realToday);
  }, [today, realToday, updateToday]);

  useEffect(() => {
    if (completedByNumber === 0 || today !== realToday) {
      updateCompletedByNumber(Math.floor(Math.random() * (10000 - 5000 + 1) + 5000));
    } else if (seconds % 60 === 0) {
      updateCompletedByNumber(completedByNumber + Math.floor(Math.random() * (10 - 5 + 1) + 5));
    }
    // eslint-disable-next-line
  }, [seconds]);

  return '';
};

export default CompletedByNumberTimer;
