'use client';

import { useState, useContext, useMemo } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';

import CompliantTrialText from '@/components/PaymentProviders/CheckoutForms/CompliantTrialText';
import PriceTotalText from '@/components/PaymentProviders/CheckoutForms/PriceTotalText';
import { usePostHogEvents } from '@/hooks/usePostHogEvents';
import { useClaimOffer } from '@/store/hooks';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';
import { PaymentContextProps } from '@/types/payment';
import { getHostUrl } from '@/utils/getHostUrl';
import { COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';

import '@/sass/form.scss';
import '@/sass/spinner.scss';

export default function CheckoutFormStripe({ context }: PaymentContextProps) {
  const { isTrial, isOneTime, time } = context;
  const t = useTranslations('checkout.payment_details.form');
  const locale = useLocale();
  const stripe = useStripe();
  const elements = useElements();
  const { trackPostHogPaymentFailedEvent, trackPostHogCheckoutConfirmedEvent } = usePostHogEvents();
  const { checkoutVersion, prices, plan } = useContext(SessionContext);
  const { claimOffer } = useClaimOffer();
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const compliantVersion = useMemo(() => time < COMPLIANCE_TIME_THRESHOLD, [time]);

  /**
   * Handles the checkout form submission:
   *  1. Tracks the checkout-confirmed event in PostHog.
   *  2. Prevents default form behavior.
   *  3. Confirms the Stripe PaymentIntent.
   *  4. Redirects on success or displays errors on failure.
   *
   * @param e The form event.
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsLoading(true);

    // Track the checkout confirmed event in PostHog analytics
    trackPostHogCheckoutConfirmedEvent({
      paymentSystem: PaymentProvider.STRIPE,
      checkoutVersion,
      testType: 'iq',
      pageType: 'IQ',
    });

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Redirect to offers if claimOffer is true, otherwise to payment-status
        return_url: claimOffer
          ? `${getHostUrl()}/${locale}/offers`
          : `${getHostUrl()}/${locale}/checkout/payment-status`,
      },
    });

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.
    if (error.type === 'card_error' || error.type === 'validation_error') {
      setMessage(error.message ?? null);

      // Track the payment failed event in PostHog analytics
      trackPostHogPaymentFailedEvent({
        paymentSystem: PaymentProvider.STRIPE,
        checkoutVersion,
        testType: 'iq',
        pageType: 'IQ',
        reason: error.message,
      });
    } else {
      setMessage(t('unexpected_error'));

      // Track the payment failed event in PostHog analytics
      trackPostHogPaymentFailedEvent({
        paymentSystem: PaymentProvider.STRIPE,
        checkoutVersion,
        testType: 'iq',
        pageType: 'IQ',
        reason: error.message ?? 'Unexpected error',
      });
    }

    setIsLoading(false);
  };

  /**
   * Spinner component for loading indication.
   */
  const Spinner = () => <div className="spinner" id="spinner"></div>;

  /**
   * Renders the checkout submit button.
   *
   * Disables itself while loading or until Stripe.js and Elements are ready.
   * Shows a spinner when loading, or localized button text for trial vs. one-time.
   *
   * @returns The submit `<button />` element.
   */
  const SubmitButton = () => (
    <button
      disabled={isLoading || !stripe || !elements}
      id="submit"
      className="flex w-full button primary text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px] leading-"
      type="submit">
      <span id="button-text">
        {isLoading ? <Spinner /> : compliantVersion && isTrial ? t('start_trial_button') : t('get_results_button')}
      </span>
    </button>
  );

  return (
    <form onSubmit={handleSubmit}>
      {message && <div className="mb-5 mt-2 text-red-500 font-bold text-lg">{message}</div>}

      {/* Total Section */}
      <PriceTotalText isTrial={isTrial} isOneTime={isOneTime} prices={prices} plan={plan} />

      {/* Trial Text */}
      <CompliantTrialText compliantVersion={compliantVersion} isTrial={isTrial} prices={prices} plan={plan} />

      {/* Payment Element */}
      <PaymentElement />

      {/* Submit button */}
      <SubmitButton />
    </form>
  );
}
