'use client';

import React, { useContext, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from '@/lib/i18n/routing';
import { useTranslations } from 'next-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import SessionContext from '@/store/SessionContext';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import BoxWithIconPremium from '@/components/LoveLanguages/BoxWithIconPremium';
import IconWithIncluded from '@/components/LoveLanguages/IconWithIncluded';
import IconWithSupport from '@/components/LoveLanguages/IconWithSupport';
import Logo from '@/components/LoveLanguages/Logo';
import PaymentCards from '@/components/LoveLanguages/PaymentCards';
import ReviewSlider from '@/components/LoveLanguages/ReviewSlider';

const Checkout: React.FC = () => {
  const router = useRouter(); // Initialize useRouter
  const { prices, results } = useContext(SessionContext);
  const t = useTranslations('love-languages.checkout');

  useEffect(() => {
    if (results.length === 0) {
      router.push('/love-languages/test');
    }

    const query = new URLSearchParams(window?.location?.search);
    const status = query.get('status');

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on Love Languages Checkout page: Event not sent');
      return;
    }

    status === 'canceled'
      ? sendGTMEvent({ event: 'payment_canceled' })
      : sendGTMEvent({
          event: 'begin_checkout',
          ecommerce: {
            currency: prices.currency.toUpperCase(),
            items: [
              {
                item_name: 'IQ test',
                item_id: 'iqtest',
                price: prices.trial.amount,
                quantity: 1,
              },
            ],
          },
        });
  }, [results, prices, router]);

  return (
    <div>
      <header className="w-full flex justify-center items-center py-[13px] md:py-[43px] mb-[27px] md:mb-[18px]">
        <Logo />
      </header>

      <main>
        <div className="flex justify-center mb-[32px] md:mb-[40px]">
          <span className="font-raleway font-bold text-[24px] md:text-[48px] leading-[32px] md:leading-[58px] text-center tracking-tight text-[#0E2432]">
            {t('main_heading')}
          </span>
        </div>
        <div className="max-w-[1440px] mx-auto px-[16px] md:px-[81px] flex-col flex md:flex-row md:gap-[40px] mb-[40px] md:mb-[60px]">
          <div className="w-full flex flex-col items-center">
            <div className="flex w-full justify-between items-center p-[16px] md:p-[24px] gap-[10px] bg-[#F2FAFF] rounded-[12px] mb-[24px]">
              <div className="flex items-center gap-[16px] md:gap-[24px]">
                <Image
                  src="/images/love-languages/check.svg"
                  alt={t('alt_text.check')}
                  width={32}
                  height={32}
                  className="w-[24px] h-[24px] md:w-[32px] md:h-[32px]"
                />
                <div className="flex flex-col items-start gap-[12px]">
                  <span className="font-raleway font-bold text-[18px] md:text-[24px] leading-[24px] md:leading-[32px] text-[#0E2432]">
                    {t.rich('product.title', {
                      br: () => <br className="block md:hidden" />,
                      break: () => <br className="hidden md:block" />,
                    })}
                  </span>
                  {/* <span
                    className="flex items-center gap-[10px] py-[6px] md:py-[8px] px-[10px] md:px-[12px] bg-white rounded-[8px] text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] font-raleway font-medium text-[#0E2432]"
                    style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                    {t('product.access_period')}
                  </span> */}
                </div>
              </div>
              <span
                className="font-raleway font-bold text-[18px] md:text-[24px] leading-[24px] md:leading-[32px] text-[#0E2432]"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                {prices.trial.formatted}
              </span>
            </div>
            <PaymentCards mb_desktop="32px" mb_mobile="24px" h_mobile="28px" h_desktop="40px" />
            <button
              className="flex justify-center w-full items-center px-[40px] py-[18.5px] md:py-[24px] bg-[#5DC4FF] rounded-[10px] font-raleway font-bold text-[16px] md:text-[20px] leading-[24px] text-white mb-[16px]"
              style={{ letterSpacing: '-0.03em' }}
              onClick={() => router.push('/love-languages/payment')} // Redirect to checkout route
            >
              {t('product.btn_get_results')}
            </button>
          </div>
          <div className="w-full">
            <div className="flex flex-col items-start p-[16px] md:p-[24px] gap-[12px] md:gap-[20px] bg-white shadow-[0px_4px_12px_rgba(0,_34,_45,_0.08)] rounded-[12px] mb-[16px] md:mb-[24px]">
              <span className="font-raleway font-bold text-[20px] md:text-[26px] leading-[30px] md:leading-[34px] text-[#0E2432]">
                {t('whats_included.heading')}
              </span>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
                <IconWithIncluded source="/images/love-languages/included-1.png" alt={t('alt_text.included-1')}>
                  {t.rich('whats_included.items.love_language_results', {
                    b1: chunks => <b>{chunks}</b>,
                    br: () => <br className="hidden md:block" />,
                    b2: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
                <IconWithIncluded source="/images/love-languages/included-4.png" alt={t('alt_text.included-4')}>
                  {t.rich('whats_included.items.premium_love_report', {
                    b: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
                <IconWithIncluded source="/images/love-languages/included-2.png" alt={t('alt_text.included-2')}>
                  {t.rich('whats_included.items.partner_compatibility', {
                    b: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
                <IconWithIncluded source="/images/love-languages/included-5.png" alt={t('alt_text.included-5')}>
                  {t.rich('whats_included.items.access_more_tests', {
                    b: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
                <IconWithIncluded source="/images/love-languages/included-3.png" alt={t('alt_text.included-3')}>
                  {t.rich('whats_included.items.personal_growth_challenge', {
                    b: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
                <IconWithIncluded source="/images/love-languages/included-6.png" alt={t('alt_text.included-6')}>
                  {t.rich('whats_included.items.relationship_roadmap', {
                    br: () => <br />,
                    b: chunks => <b>{chunks}</b>,
                  })}
                </IconWithIncluded>
              </div>
            </div>
            <div className="flex flex-col">
              <div className="w-full bg-[#F6F6F6] rounded-[12px] p-[12px] md:p-[16px] md:pr-[70px] md:mb-[24px] order-2 md:order-none">
                <div className="flex items-center gap-[12px] md:gap-[14px] ">
                  <Image
                    src="/images/love-languages/info.png"
                    alt={t('alt_text.info')}
                    width={48}
                    height={48}
                    className="w-[40px] h-[40px] md:w-[48px] md:h-[48px] bg-white rounded-[8px]"
                  />
                  <span
                    className="font-raleway font-medium text-[12px] md:text-[16px] leading-[18px] md:leading-[20px] text-[#aaa]"
                    style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                    {t('support_features.subscription_info', { price: prices.subscription.formatted })}
                  </span>
                </div>
              </div>
              <div className="flex gap-[14px] flex-col md:flex-row order-1 mb-[24px] md:order-none">
                <IconWithSupport source="/images/love-languages/check-dark.png" alt={t('alt_text.check-dark')}>
                  {t('support_features.refund')}
                </IconWithSupport>
                <IconWithSupport source="/images/love-languages/check-dark.png" alt={t('alt_text.check-dark')}>
                  {t('support_features.premium_support')}
                </IconWithSupport>
                <IconWithSupport source="/images/love-languages/check-dark.png" alt={t('alt_text.check-dark')}>
                  {t('support_features.satisfaction_guarantee')}
                </IconWithSupport>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-center p-[20px] md:py-[40px] md:px-[82px] bg-[#F4FAFF] mb-[40px] md:mb-[0px]">
          <div className="flex flex-wrap items-center py-[10px] gap-x-[24px] gap-y-[21px] md:gap-[86px]">
            <Image
              src="/images/love-languages/logo-1.png"
              alt={t('alt_text.logo-1')}
              width={111.92}
              height={30}
              className="w-[89.53px] h-[24px] md:w-[111.92px] md:h-[30px]"
            />
            <Image
              src="/images/love-languages/logo-2.png"
              alt={t('alt_text.logo-2')}
              width={156.92}
              height={30}
              className="w-[125.53px] h-[24px] md:w-[156.92px] md:h-[30px]"
            />
            <Image
              src="/images/love-languages/logo-3.png"
              alt={t('alt_text.logo-3')}
              width={305.9}
              height={23}
              className="w-[270.38px] h-[20.33px] md:w-[305.9px] md:h-[23px]"
            />
            <Image
              src="/images/love-languages/logo-4.png"
              alt={t('alt_text.logo-4')}
              width={213.87}
              height={30}
              className="w-[199.6px] h-[28px] md:w-[213.87px] md:h-[30px]"
              priority
            />
            <Image
              src="/images/love-languages/logo-5.png"
              alt={t('alt_text.logo-5')}
              width={143.23}
              height={28}
              className="w-[105.73px] h-[20.67px] md:w-[143.23px] md:h-[28px]"
            />
          </div>
        </div>
        <div className="w-full relative mb-[40px] md:mb-0 bg-white">
          <div className="mx-auto flex items-center justify-center">
            <Image
              src="/images/love-languages/white-blur-desktop.png"
              alt={t('alt_text.white_blur_desktop')}
              width={1192}
              height={524}
              className="absolute md:w-[1192px] md:h-auto hidden md:block"
              priority
            />
            <Image
              src="/images/love-languages/white-blur-mobile.png"
              alt={t('alt_text.white_blur_mobile')}
              width={375}
              height={343}
              className="absolute mx-auto w-full h-auto block md:hidden top-[90px]"
              priority
            />
            <div className="w-full md:w-auto flex flex-col md:my-[80px] z-10 justify-between md:justify-normal md:bg-white md:shadow-[0px_0px_100px_rgba(0,0,0,0.1)] items-center px-4 md:p-8 rounded-[12px] gap-8">
              <Image
                src="/images/love-languages/lock-solid.png"
                alt={t('alt_text.lock_solid')}
                width={135}
                height={149}
                className="w-[97px] h-[110px] md:w-[109.03px] md:h-[124px] filter drop-shadow-[0px_4.46037px_18.9566px_rgba(1,46,61,0.25)] md:drop-shadow-[0px_6.75136px_28.6933px_rgba(1,46,61,0.25)] hidden md:block"
                priority
              />
              <div className="flex flex-col md:justify-center items-center gap-2">
                <span className="font-raleway font-bold leading-[32px] md:leading-[34px] text-[24px] md:text-[26px] text-[#0E2432] md:text-[#353535] text-center">
                  {t('lock_section.heading')}
                </span>
                <span
                  className="font-raleway font-medium leading-[20px] md:leading-[24px] text-[14px] md:text-[16px] text-[#828E98] text-center"
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                  {t.rich('lock_section.description', {
                    price: prices.trial.formatted,
                    br: () => <br />,
                  })}
                </span>
              </div>
              <Image
                src="/images/love-languages/lock-solid.png"
                alt={t('alt_text.lock_solid')}
                width={135}
                height={149}
                className="relative w-[97px] h-[110px] filter drop-shadow-[0px_4.46037px_18.9566px_rgba(1,46,61,0.25)] md:drop-shadow-[0px_6.75136px_28.6933px_rgba(1,46,61,0.25)] mb-[130px] block md:hidden top-[70px]"
                priority
              />
              <button
                className="flex justify-center w-full items-center py-[18.5px] md:py-[24px] bg-[#5DC4FF] rounded-[10px] font-raleway font-bold text-[16px] md:text-[20px] md:leading-[24px] tracking-tight text-white"
                onClick={() => router.push('/love-languages/payment')} // Redirect to checkout route
              >
                {t('lock_section.btn_unlock')}
              </button>
            </div>
          </div>
        </div>
        <div className="w-full bg-[#F2FAFF]">
          <div className="max-w-[1440px] mx-auto premium-section flex flex-col items-center relative m-0 p-0 px-[16px] py-[40px] md:py-[80px] md:px-[76px] mb-[40px] md:mb-[0px]">
            <Image
              src="/images/love-languages/premium-effect-desktop-1.png"
              alt={t('alt_text.desktop_gradient')}
              width={657}
              height={407}
              className="absolute right-[175px] top-[0px] md:w-[178px] md:h-auto hidden md:block z-0"
              priority
            />
            <Image
              src="/images/love-languages/premium-effect-desktop-2.png"
              alt={t('alt_text.desktop_gradient')}
              width={657}
              height={476}
              className="absolute left-[175px] bottom-[0px] md:w-[178px] md:h-auto hidden md:block z-0"
              priority
            />
            <label className="font-raleway font-bold flex justify-center text-center text-[32px] leading-[36px] md:text-[40px] md:leading-[48px] md:mb-[12px] mb-[26px] text-[#292929] z-20">
              {t('premium_section.heading')}
            </label>
            <label className="font-raleway font-medium flex justify-center text-center text-[16px] leading-[22px] md:text-[18px] md:leading-[27px] md:mb-[40px] mb-[26px] text-[#828E98] z-20">
              {t.rich('premium_section.description', {
                br: () => <br className="hidden md:block" />,
              })}
            </label>
            <div className="relative grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5 z-20 mb-8 md:mb-10">
              <Image
                src="/images/love-languages/premium-effect-mobile-1.png"
                alt={t('alt_text.premium_mobile_1')}
                width={375}
                height={557}
                className="w-[98px] h-auto block md:hidden absolute right-[-16px] z-10"
                style={{ top: '-106px' }}
              />
              <BoxWithIconPremium
                source="/images/love-languages/premium-1.png"
                alt={t('alt_text.premium_hearts')}
                heading={t('premium_section.features.primary_secondary_styles.heading')}>
                {t('premium_section.features.primary_secondary_styles.description')}
              </BoxWithIconPremium>
              <BoxWithIconPremium
                source="/images/love-languages/premium-2.png"
                alt={t('alt_text.premium_hearts')}
                heading={t('premium_section.features.giving_receiving_styles.heading')}>
                {t('premium_section.features.giving_receiving_styles.description')}
              </BoxWithIconPremium>
              <BoxWithIconPremium
                source="/images/love-languages/premium-3.png"
                alt={t('alt_text.premium_partner')}
                heading={t('premium_section.features.partner_compatibility.heading')}>
                {t('premium_section.features.partner_compatibility.description')}
              </BoxWithIconPremium>
              <BoxWithIconPremium
                source="/images/love-languages/premium-4.png"
                alt={t('alt_text.premium_love_style')}
                heading={t('premium_section.features.strengthen_relationships.heading')}>
                {t('premium_section.features.strengthen_relationships.description')}
              </BoxWithIconPremium>
              <BoxWithIconPremium
                source="/images/love-languages/premium-5.png"
                alt={t('alt_text.premium_heart_box')}
                heading={t('premium_section.features.speak_love_language.heading')}>
                {t('premium_section.features.speak_love_language.description')}
              </BoxWithIconPremium>
              <Image
                src="/images/love-languages/premium-effect-mobile-2.png"
                alt={t('alt_text.premium_mobile_2')}
                width={375}
                height={733}
                className="w-[98px] h-auto block md:hidden absolute left-[-16px] z-10"
                style={{ top: 'calc(83% - 87px)' }}
              />
              <BoxWithIconPremium
                source="/images/love-languages/premium-6.png"
                alt={t('alt_text.premium_smile')}
                heading={t('premium_section.features.least_comfortable_styles.heading')}>
                {t('premium_section.features.least_comfortable_styles.description')}
              </BoxWithIconPremium>
            </div>
            <button
              className="flex justify-center w-full md:w-auto items-center py-[18.5px] md:px-[77px] md:py-[24px] bg-[#5DC4FF] rounded-[10px] font-raleway font-bold text-[16px] md:text-[20px] leading-[24px] text-white mb-[16px] md:mb-0 tracking-tight"
              onClick={() => router.push('/love-languages/payment')} // Redirect to checkout route
            >
              {t('btn_unlock')}
            </button>
          </div>
        </div>
        <div className="max-w-[1440px] mx-auto bg-white px-4 md:px-[82px] md:py-[80px] mb-[40px] md:mb-0 flex items-center">
          <ReviewSlider />
        </div>
      </main>
    </div>
  );
};

export default Checkout;
