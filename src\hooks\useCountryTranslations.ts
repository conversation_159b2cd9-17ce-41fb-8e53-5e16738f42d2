// React hooks for country name translations
import { useTranslations } from 'next-intl';

// Type for country data
export interface Country {
  id: string;
  iq: number;
  name: string;
}

// Hook to get translated country name
export const useCountryName = (countryId: string): string => {
  const t = useTranslations('countries');
  return t(countryId);
};

/**
 * Filters out US states from countries array, keeping only actual countries
 * Used in CountriesSection to display only countries, not individual US states
 * @param countries - Array of countries including US states
 * @returns Array of countries without US states (excludes us-ca, us-ny, etc.)
 */
export const getCountriesWithoutUsStates = (countries: Country[]): Country[] => {
  return countries.filter(country => !country.id.startsWith('us-'));
};

// Hook to get all countries with translated names
export const useTranslatedCountries = (countries: Country[]): Country[] => {
  const t = useTranslations();

  // Filter out US states from countries array
  const countriesWithoutUsStates = getCountriesWithoutUsStates(countries);

   return countriesWithoutUsStates.map(country => ({
    ...country,
    name: getCountryName(country.id, t)
  }));
};

// Helper function to get translated country name with fallback
const getCountryName = (countryId: string, t: any): string => {
  try {
    return t(`countries.${countryId}`);
  } catch (error) {
    return countryId.toUpperCase(); // Fallback to country code
  }
};
