'use client';

import { useTranslations } from 'next-intl';
import AccordionCheckout from '@/components/AccordionCheckout';

export default function FaqSection() {
  const t = useTranslations('checkout_v2.faq');

  return (
    <div className="max-w-[1276px] px-3 md:px-0 mx-auto py-10 md:pt-16 md:pb-16">
      <div className="md:flex">
        <h2 className="text-[38px] md:text-[52px] md:w-2/5 font-bold tracking-tight pb-5 md:pb-5 leading-[56px] text-[#191919]">
          {t('title')}
        </h2>
        <div className="md:w-3/5 text-[#8893AC] font-[400] md:pl-12 leading-[26px] text-[18px]">
          <AccordionCheckout
            title={t('questions.what_included.question')}
            content={t('questions.what_included.answer')}
          />
          <AccordionCheckout
            title={t('questions.not_satisfied.question')}
            content={t('questions.not_satisfied.answer')}
          />
          <AccordionCheckout
            title={t('questions.results_time.question')}
            content={t('questions.results_time.answer')}
          />
          <AccordionCheckout
            title={t('questions.scientifically_valid.question')}
            content={t('questions.scientifically_valid.answer')}
          />
          <AccordionCheckout
            title={t('questions.multiple_devices.question')}
            content={t('questions.multiple_devices.answer')}
          />
          <AccordionCheckout
            title={t('questions.additional_support.question')}
            content={t('questions.additional_support.answer')}
          />
        </div>
      </div>
    </div>
  );
} 