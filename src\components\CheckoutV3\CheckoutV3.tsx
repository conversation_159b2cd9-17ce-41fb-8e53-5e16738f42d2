import { useState, useEffect, useRef, useContext, useMemo } from 'react';
import Cookies from 'js-cookie';
import Image from 'next/image';
import Link from 'next/link';
import Head from 'next/head';
import IqCard from '@/components/IqCard';
import TrustItem from '@/components/TrustItem';
import SelectedItem from '@/components/SelectedItem';
import AccordionCheckout from '@/components/AccordionCheckout';
import PauseOnHover from '@/components/CarouselAutoplay';
import Navbartwo from '@/components/Navbartwo';
import HelpItem from '@/components/CheckoutV3/HelpItem';
import countries from '@/containers/home/<USER>';
import JustBoughtFunction from '@/components/Navbartwo/just_bought';
import PaymentProviders from '@/components/PaymentProviders';
import ReviewSlider from '@/components/CheckoutV3/ReviewSlider';
import SessionContext from '@/store/SessionContext';
import { SubscriptionPlanEnum } from '@/types/plan-types';

export default function CheckoutV3() {
  const { locale, updateLocale, formData, paymentSystem } = useContext(SessionContext);
  const { prices, plan } = useContext(SessionContext);
  const scroll = useRef<HTMLDivElement | null>(null);
  const [namesOnTable, setNamesOnTable] = useState<string[]>([]);
  const [peopleFromCountry, setPeopleFromCountry] = useState([
    { 'First name': '', 'Last name': '', 'IQ score': '', Country: '' },
  ]);
  const [peopleArray, setArray] = useState([{ name: '', image: '', IQ: 102 }]);
  const [creditCard, setCreditCard] = useState(false);
  const [expriedTime, setExpiredTime] = useState(300);

  const country = useMemo(() => {
    const locale = Cookies.get('locale')?.toLowerCase();
    return countries.find(c => c.id === locale) || countries.find(c => c.id === 'us');
  }, []);

  const scrollToElement = () => {
    if (scroll.current) {
      scroll.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setExpiredTime(prev => {
        if (prev <= 0) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1; // adjust increment as needed
      });
    }, 1000);

    return () => clearInterval(interval); // cleanup
  }, []);

  useEffect(() => {
    updateLocale(Cookies.get('locale') || '');
  }, [locale, updateLocale]);

  useEffect(() => {
    setPeopleFromCountry(JustBoughtFunction(country?.name));
  }, [country]);

  useEffect(() => {
    setNamesOnTable(peopleArray.slice(0, 8).map(person => person.name));
  }, [peopleArray]);

  const namesRef = useRef(new Set()); // Ref to track the names on the table without causing re-renders

  useEffect(() => {
    if (peopleFromCountry.length > 0) {
      updateContent(({ name, image, number }: { name: string; image: string; number: number }) => {
        if (name) {
          // Add name to the namesRef set without causing re-renders
          namesRef.current.add(name);
          const newResult = { name, image, IQ: number };
          setArray(prevArray => [newResult, ...prevArray]);
        } else {
          console.log('No valid name returned');
        }
      }, peopleArray);

      const interval = setInterval(() => {
        updateContent(({ name, image, number }: { name: string; image: string; number: number }) => {
          if (name) {
            // Check if the name is already in the set, if not add it
            if (!namesRef.current.has(name)) {
              namesRef.current.add(name);
              const newResult = { name, image, IQ: number };
              setArray(prevArray => [newResult, ...prevArray]);
            }
          } else {
            console.log('No valid name returned');
          }
        }, peopleArray);
      }, 15000);

      return () => clearInterval(interval);
    }
  }, [peopleFromCountry]);

  function updateContent(callback: any, currentArray: any) {
    const getRandomContent = () => {
      if (peopleFromCountry.length === 0) {
        console.log('No more people available');
        return;
      }

      var existingNames = new Set(namesOnTable);

      const availablePeople = peopleFromCountry.filter(person => {
        const fullName = `${person['First name']} ${person['Last name']}`.trim();
        return !existingNames.has(fullName) && fullName;
      });

      if (availablePeople.length === 0) {
        console.log('No unique people left to select');
        return;
      }

      const randomIndex = Math.floor(Math.random() * availablePeople.length);
      const randomPerson = availablePeople[randomIndex];
      const randomPeopleCountryImage = countries.find(c => c.name === randomPerson['Country']);

      const name = `${randomPerson['First name']} ${randomPerson['Last name']}`.trim();

      callback({
        name,
        image: `https://flagcdn.com/w80/${randomPeopleCountryImage?.id}.png`,
        number: randomPerson['IQ score'],
      });
    };

    getRandomContent();
  }

  return (
    <div className="overflow-hidden grow flex justify-center">
      <Head>
        <link rel="stylesheet" href="https://assets.reviews.io/css/widgets/carousel-widget.css?_t=2024100119" />
        <link rel="stylesheet" href="https://assets.reviews.io/iconfont/reviewsio-icons/style.css?_t=2024100119" />
      </Head>
      <div className="w-full">
        <div className="w-full bg-grey-96 pb-px border-grey-91 border shadow">
          <div className="w-full md:max-w-[1280px] mx-auto px-4 md:px-0">
            <div className="flex items-center justify-between h-[65px]">
              <div>
                <Link href="/">
                  <Image
                    src="/images/checkout-v3/Logo.svg"
                    alt="Check Logo"
                    className="px-0 h-[36px] w-auto hidden md:block"
                    width={300}
                    height={36}
                    priority // Load it early because it's part of header/logo
                  />
                </Link>
                <Link href="/">
                  <Image
                    src="/images/checkout-v3/Logo-mobile.svg"
                    width={162}
                    height={30}
                    className="flex-sharink-0 block h-[30px] md:hidden mx-0"
                    alt="Academy logo-mobile"
                    priority
                  />
                </Link>
              </div>
              <div className="h-6 w-[100px] bg-white flex items-center rounded-[12px]">
                <div className="w-[24px] h-[24px] rounded-full border-2 border-grey-84 bg-white p-[2px]">
                  <div className="flex flex items-center justify-center w-[16px] h-[16px] bg-grey-84 rounded-full">
                    <span className="text-black font-segoe text-xs">{formData.email[0]?.toUpperCase()}</span>
                  </div>
                </div>
                <div className="ml-[2px] font-segoe text-grey-26 font-normal text-xs overflow-hidden truncate">
                  {formData.email}
                </div>
              </div>
            </div>
          </div>
        </div>

        {peopleArray[0].name != '' && (
          <Navbartwo name={peopleArray[0].name} image={peopleArray[0].image} IQ={peopleArray[0].IQ} />
        )}
        <div className="bg-white w-full pb-px border-b boder-grey-91 shadow">
          <div className="mx-auto px-4 md:p-0 w-full md:max-w-[1280px]">
            <div className="flex items-center justify-between h-[65px]">
              <div className="pt-2">
                <p className="font-segoe p-0 m-0 font-semibold text-[14px] leading-[17px] text-[#000000]">
                  Your <span className="text-primary">IQ Score</span> Expires in
                </p>
                <p className="font-bold text-[18px] leading-[22px] text-[#000000]">{`${Math.floor(expriedTime / 60)}:${
                  expriedTime % 60 < 10 ? '0' : ''
                }${expriedTime % 60}`}</p>
              </div>
              <div className=" sm:ml-auto sm:block">
                <button
                  onClick={scrollToElement}
                  className="min-w-[96px] max-w-[101px] h-[44px] px-[17.5px] pt-[10px] pb-[10px] rounded-lg bg-primary">
                  <span className="font-inter font-bold pt-1 text-[16px] leading-[24px] text-white">Continue</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full bg-[#FDF9F1] md:bg-gradient-to-b from-[#FDF9F1] to-[#FFFFFF]">
          <div className="max-w-[1280px] mx-auto md:flex justify-between items-center flex-row-reverse px-4 md:px-0 md:py-[52px]">
            <div className="pt-[50px] pb-[38px] md:pl-[58px] md:py-[14px]">
              <div className="hidden md:block md:flex flex-rows gap-[21.5px] items-end">
                <IqCard imgSource="ice spice" name="Ice Spice" text="IQ 103" />
                <IqCard imgSource="You" name="You" text="???" />
                <IqCard imgSource="Jeff Bezos" name="Jeff Bezos" text="IQ 155" />
              </div>
              <div className="md:hidden flex justify-between w-[358px] max-w-[495px] mx-auto items-end">
                <IqCard imgSource="ice spice1" name="Ice Spice" text="IQ 103" />
                <IqCard imgSource="You1" name="You" text="???" />
                <IqCard imgSource="Jeff Bezos1" name="Jeff Bezos" text="IQ 155" />
              </div>
            </div>
            <div>
              <p className="text-center md:text-left p-0 m-0 font-inter md:font-segoe font-bold text-[25px] md:text-5xl leading-8 md:leading-[60px] text-[#3F425E]">
                Your <span className="text-primary">Results</span> Are In!
              </p>
              <p className="text-center hidden md:block mt-[5px] md:text-left p-0 m-0 font-inter font-semibold text-[18px] leading-[25px] text-[#2B2D42]">
                Discover Your IQ Score Now
              </p>
              <button
                onClick={scrollToElement}
                className="min-w-[80px] w-[320px] max-w-[400px] h-[56px] mt-5 mx-auto block md:mx-0 md:mt-8 px-4 pt-[14px] pb-[15px] rounded-xl bg-primary font-inter font-bold text-[18px] leading-[27px] text-white">
                Unlock My Results!
              </button>
            </div>
          </div>
          <div className="max-w-[1280px] mx-auto pb-[36px] pt-[44px] md:pt-[7px]">
            <PauseOnHover />
          </div>
        </div>

        <div ref={scroll} className="w-full bg-white">
          <div className="max-w-[1280px] mx-auto pt-10 pb-5 px-4 md:p-0 md:py-10 flex flex-col gap-4 md:gap-6">
            <div className="font-segoe font-semibold text-[28px] md:text-[39px] leading-7 md:leading-[39px] tracking-tight text-center text-grey-5">
              GET INSTANT ACCESS to
            </div>
            <div className="flex flex-col md:flex-row md:items-start gap-10 md:gap-x-[44px]">
              <div className="md:flex-1 p-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="flex flex-col gap-0">
                  <div className="rounded-[14px] bg-[#F5F5F5] flex justify-between items-center py-3 px-[18px]">
                    <div className="font-segoe font-normal text-[14px] leading-[21px] tracking-0">
                      Over <span className="font-semibold">2379</span> tests taken today
                    </div>
                    <div className="flex flex-row">
                      <Image
                        src={`/images/checkout-v3/Margin(0).svg`}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-9 md:h-9 rounded-full ring-1 ring-[#D4D4D8]"
                        alt="Margin0"
                      />
                      <Image
                        src={'/images/checkout-v3/Margin(1).svg'}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-9 md:h-9 -ml-3 rounded-full ring-1 ring-[#D4D4D8]"
                        alt="Margin1"
                      />
                      <Image
                        src={'/images/checkout-v3/Margin(2).svg'}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-9 md:h-9 -ml-3 rounded-full ring-1 ring-[#D4D4D8]"
                        alt="Margin2"
                      />
                      <Image
                        src={'/images/checkout-v3/Margin(3).svg'}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-9 md:h-9 -ml-3 rounded-full ring-1 ring-[#D4D4D8]"
                        alt="Margin3"
                      />
                      <Image
                        src={'/images/checkout-v3/Margin(4).svg'}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-9 md:h-9 -ml-3 rounded-full ring-1 ring-[#D4D4D8]"
                        alt="Margin4"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="pt-4 pb-3 font-segoe font-semibold text-[14px] leading-[21px] tracking-[-0.35px] text-[#001B36]">
                      WHY CAN YOU TRUST IQ INTERNATIONAL
                    </div>
                    <div className="flex flex-col gap-2">
                      <TrustItem
                        imageSource={'Graduation Cap Streamline Lucide Line'}
                        title={'Certified IQ Test'}
                        text={
                          'Our test is designed following the Stanford-Binet Intelligence Scale, recognized as the benchmark in IQ measurement since 1916.'
                        }
                      />
                      <TrustItem
                        imageSource={'Certificate Streamline Phosphor Regular'}
                        title={'Comprehensive Report'}
                        text={
                          'Receive a tailored report built on the renowned Cattell-Horn-Carroll (CHC) model, the most trusted framework for measuring cognitive abilities.'
                        }
                      />
                      <TrustItem
                        imageSource={'Brain Circuit Streamline Lucide Line'}
                        title={'Science-Based Training'}
                        text={
                          'Our training is built on modern brain science and is designed to strengthen your cognitive abilities and raise your IQ.'
                        }
                      />
                    </div>
                  </div>
                  <div className="flex flex-col pt-4 md:pt-0">
                    <div className="py-4 font-segoe font-semibold text-[14px] leading-[21px] tracking-[-0.35px] text-[#001B36]">
                      IQ International HAS BEEN FEATURED IN
                    </div>
                    <div className="flex justify-center flex-wrap gap-y-1">
                      <img src="/images/marque/marque3-grey.svg" className="mr-[17px] md:mr-5" />
                      <img src="/images/marque/marque-grey.svg" className="mr-[17px] md:mr-9" />
                      <img src="/images/marque/marque1-grey.svg" className="mr-[17px] md:mr-10" />
                      <img src="/images/marque/marque2-grey.svg" className="md:mr-9" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:flex-1 md:p-2 rounded-[14px] flex flex-col shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="p-[12px] flex flex-col">
                  <div className="pt-1 pb-3">
                    <div className="flex flex-col gap-4">
                      <SelectedItem text="Find out your precise IQ score with our scientifically-validated test" />
                      <SelectedItem text="Find out where you rank among the general population" />
                      <SelectedItem text="Discover your mental strengths and areas to improve" />
                    </div>
                  </div>
                  <div className="pt-2 pb-5">
                    <div className="flex flex-row items-center gap-3 px-3 bg-[#FDF9F1] h-[70px]">
                      <Image
                        src={'/images/checkout-v3/gift.svg'}
                        width={24}
                        height={24}
                        className="w-6 h-6"
                        alt="gift"
                      />
                      <div className="flex flex-col gap-0">
                        <div className="font-segoe font-semibold text-[14px] leading-[21px] tracking-[0px] text-[#121620]">
                          Promo Code IQINTERNATIONAL-85 Applied
                        </div>
                        <div className="font-segoe font-normal text-[14px] leading-[21px] tracking-[0px] text-[#545758]">
                          You save{' '}
                          {Math.round(
                            (1 -
                              prices.trial.amount /
                                (plan === SubscriptionPlanEnum.Weekly && prices.subscription?.weekly?.amount
                                  ? prices.subscription.weekly.amount
                                  : prices.subscription?.amount)) *
                              100
                          )}
                          %
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between pb-5">
                    <div className="font-segoe font-semibold text-base tracking-[0px] text-[#3F425E]">
                      Total due today:
                    </div>
                    <span className="font-segoe font-semibold text-[16px] leading-[21px] tracking-0 text-[#6B7280]">
                      <span className="line-through">
                        {plan === SubscriptionPlanEnum.Weekly
                          ? prices.subscription.weekly?.formatted
                          : prices.subscription.formatted}
                      </span>{' '}
                      <span className="font-bold text-[21px] leading-[27px] text-[#3F425E]">
                        {prices.trial.formatted}
                      </span>
                    </span>
                  </div>
                </div>
                <div className="md:pb-4">
                  <div className="p-[10px]">
                    <button
                      onClick={() => setCreditCard(!creditCard)}
                      className={`${
                        creditCard ? 'hidden' : 'block'
                      } w-full flex button primary justify-center items-center rounded-lg h-[45px]`}>
                      <Image
                        src={`/images/checkout-v3/credit card.svg`}
                        width={20}
                        height={27}
                        className="w-[20px] h-auto"
                        alt="creadit"
                      />
                      <span className="mx-[10px] font-kumbh font-semibold text-[18px] leading-[27px] text-white">
                        Credit or debit card
                      </span>
                    </button>
                    <div className={`${creditCard ? 'block' : 'hidden'} w-full`}>
                      <button onClick={() => setCreditCard(!creditCard)}>
                        <Image
                          src={`/images/checkout-v3/allow-left.svg`}
                          width={30}
                          height={30}
                          className="w-[30px] h-auto"
                          alt="creadit"
                        />
                      </button>
                      <div>{paymentSystem ? <PaymentProviders provider={paymentSystem} /> : null}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full bg-white">
          <div className="max-w-[1280px] mx-auto pb-5 md:pb-10 px-4 md:px-0">
            <div className="relative p-[30px] flex flex-col gap-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
              <div className="flex flex-row items-center gap-2">
                <Image
                  src={'/images/checkout-v3/Traced Image.svg'}
                  width={32}
                  height={32}
                  className="w-8 h-8"
                  alt="Trace"
                />
                <div className="font-segoe font-semibold text-[20px] leading-[20px] tracking-[0px] text-[#3F3F46]">
                  Your Personalized Report
                </div>
              </div>
              <div className="font-segoe font-normal text-[16px] leading-[21px] tracking-0 text-[#3F425E]">
                Your results reveal captivating insights into your cognitive strengths and hidden talents. With scores
                that place you as a top performer in key areas, it shows you have outstanding abilities in
                problem-solving and cognitive reasoning:
              </div>
              <div className="relative blur-[3px] font-segoe font-normal text-[16px] leading-[21px] tracking-0 text-[#3F425E]">
                You demonstrate an impressive ability to recognize patterns and make logical connections, skills that
                are crucial in both academic and professional settings. However, there are also areas where further
                development could greatly enhance your overall cognitive performance.
              </div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-y-2 mt-20 md:mt-14 w-[169px]">
                <Image
                  width={23}
                  height={27}
                  src={`/images/checkout-v3/lock.svg`}
                  alt="..."
                  className={`mx-auto max-w-full max-h-full mx-auto my-auto block shadow rounded`}
                />
                <span className="text-segoe font-bold text-[13px] leading-[15px] tracking-0 text-center text-[#3F425E]">
                  To read the full report, you need full access
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full bg-white">
          <div className="max-w-[1280px] mx-auto pb-5 md:pb-10 px-4 md:px-0">
            <div className="flex flex-col md:flex-row-reverse gap-5 md:gap-x-8">
              <div className="hidden md:block flex-1 pt-4 pb-5 px-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="flex flex-col gap-4">
                  <div className="font-segoe font-semibold text-[20px] leading-[20px] tracking-0 text-[#3F425E]">
                    Learn How To
                  </div>
                  <div className="flex flex-col gap-y-3">
                    <HelpItem imgSource="tickmark" text="Learn faster and retain information more effectively" />
                    <HelpItem imgSource="tickmark" text="Solve complex problems with more ease and confidence" />
                    <HelpItem imgSource="tickmark" text="Build superior analytical thinking strategies" />
                    <HelpItem imgSource="tickmark" text="Boost your memory for improved performance" />
                  </div>
                </div>
              </div>
              <div className="block md:hidden flex-1 pt-4 pb-5 px-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="flex flex-col gap-4">
                  <div className="font-segoe font-semibold text-[20px] leading-[20px] tracking-0 text-[#3F425E] text-center">
                    How you&apos; ll benefit
                  </div>
                  <div className="flex flex-col gap-y-3">
                    <HelpItem
                      imgSource="tickmark1"
                      text="Understand How You Show Affection to strengthen your connections"
                    />
                    <HelpItem imgSource="tickmark1" text="Improve Communication for more effective love expressions" />
                    <HelpItem imgSource="tickmark1" text="Resolve Relationships Issues promptly and efficiently" />
                    <HelpItem imgSource="tickmark1" text="Build Stronger Bonds by deeping emotional connections" />
                    <HelpItem imgSource="tickmark1" text="Boost Self-Awareness and enhance emotional intelligence" />
                  </div>
                </div>
              </div>
              <div className="hidden md:block flex-1 pt-4 pb-5 px-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="flex flex-col gap-4">
                  <div className="font-segoe font-semibold text-[20px] leading-[20px] tracking-0 text-[#3F425E] text-center">
                    How this will help you
                  </div>
                  <div className="flex flex-col gap-y-3">
                    <HelpItem
                      imgSource="tickmark"
                      text="Stay ahead of others and succeed in competitive environments"
                    />
                    <HelpItem imgSource="tickmark" text="Advance your career and meet your professional goals" />
                    <HelpItem imgSource="tickmark" text="Make smarter choices in work, life, and beyond" />
                    <HelpItem
                      imgSource="tickmark"
                      text="Boost your confidence and self-assurance to tackle new challenges"
                    />
                  </div>
                </div>
              </div>
              <div className="md:hidden flex-1 pt-4 pb-5 px-4 rounded-[14px] shadow-[0_0_1px_0_#0000004D,_0_2px_30px_0_#00000014]">
                <div className="flex flex-col gap-4">
                  <div className="font-segoe font-semibold text-[20px] leading-[20px] tracking-0 text-[#3F425E] text-center">
                    Learn How To
                  </div>
                  <div className="flex flex-col gap-y-3">
                    <HelpItem imgSource="tickmark1" text="Identify your Love Style and discover how you express love" />
                    <HelpItem imgSource="tickmark1" text="Manage Love Blockers and resolve the issues effectively" />
                    <HelpItem imgSource="tickmark1" text="Express Love clearly through mastered techniques" />
                    <HelpItem imgSource="tickmark1" text="Develop Emotional Skills for personal growth" />
                    <HelpItem imgSource="tickmark1" text="Build Deeper Connections for lasting relationships" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full bg-white">
          <div className="max-w-[1280px] mx-auto pb-5 px-4 md:px-0">
            <ReviewSlider page="checkout" />
          </div>
        </div>

        <div className="w-full bg-[#FDF9F1]">
          <div className="max-w-[1280px] mx-auto px-8 py-10 md:px-4 md:py-20">
            <div className="md:flex md:justify-between md:items-center">
              <div className="hidden md:block max-w-[256px] font-semibold font-segoe traking-0 text-[39px] leading-[50px] text-[#3F425E] pt-5 pb-8">
                Frequently Asked Questions
              </div>
              <div className="block md:hidden text-center font-segoe font-semibold traking-0 text-[28px] leading-[33px] text-[#3F425E] py-4">
                FAQs
              </div>
              <div className="md:w-[795px] pt-4">
                <AccordionCheckout
                  title="How does our IQ Test work?"
                  content="The journey begins with our certified IQ Test to assess your current strengths. Then, you receive a detailed report highlighting your performance and areas for growth, as well as a Certificate with your score. This will allow you to begin a customized training journey through the IQ Academy, designed to help you improve key cognitive abilities over time."
                />
                <AccordionCheckout
                  title="How many parts are there in this IQ test?"
                  content="Our IQ Test is divided into several key sections designed to evaluate different dimensions of intelligence. Typically, you'll encounter three parts, each focusing on a specific skill set. To make the experience more challenging, the questions are presented in a randomized order. Each part focuses on either visual reasoning, analytical thinking, or spatial reasoning. Together, these sections provide a well-rounded assessment of your cognitive abilities and serve as a solid foundation for further training through our IQ Academy."
                />
                <AccordionCheckout
                  title="Can I train with different devices?"
                  content="Yes, your training syncs automatically across all your devices, whether you use a phone, tablet, or computer. That means you can practice anytime, anywhere, without losing momentum."
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
