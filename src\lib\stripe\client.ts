import { Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementLocale, StripeElementsOptions } from '@stripe/stripe-js';
import stripeConfig from '@/config/stripe.config';

/**
 * A singleton Promise that resolves to a Stripe.js client instance, initialized with the publishable key
 * from the validated Stripe config. Use this to interact with Stripe Elements in your application.
 *
 * @see https://stripe.com/docs/js/initializing
 *
 * @example
 * import { stripe } from '@/lib/stripe/client';
 * // Use `stripe` as the `stripe` prop for <Elements> from @stripe/react-stripe-js
 * <Elements stripe={stripe} options={...}>...</Elements>
 */
export const stripe = loadStripe(stripeConfig.stripePublishableKey);

/**
 * Utility to create StripeElementsOptions for <Elements>.
 * @param clientSecret - The Stripe client secret.
 * @param locale - The locale to use for Stripe Elements.
 * @returns {StripeElementsOptions} The options object for Stripe Elements.
 */
export function createStripeOptions(clientSecret: string, locale: string): StripeElementsOptions {
  return {
    clientSecret,
    locale: locale as StripeElementLocale,
    appearance: {
      theme: 'stripe',
    },
  };
}

export { Elements };
