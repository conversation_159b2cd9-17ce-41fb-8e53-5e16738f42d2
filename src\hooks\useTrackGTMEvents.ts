import { useCallback, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import { sendGTMEvent } from '@next/third-parties/google';
import { Prices } from '@/app/prices';
import SessionContext from '@/store/SessionContext';
import { getPlanName } from '@/utils/getPlanName';
import { getPlanCurrency, getTrialAmount, getOneTimeAmount, getSubscriptionAmount } from '@/utils/getSubscriptionPlan';
import { isGTMInitialized } from '@/utils/isGtmInitialized';

/**
 * Custom React hook to track Google Tag Manager (GTM) checkout events for payment providers.
 *
 * @returns A function to track GTM events for the given Prices.
 */
export function useTrackGTMEvents() {
  const searchParams = useSearchParams();
  const { plan, planType, getIsTrial, getIsOneTime } = useContext(SessionContext);
  const isTrial = getIsTrial();
  const isOneTime = getIsOneTime();

  /**
   * Tracks a Google Tag Manager (GTM) checkout event for payment providers.
   *
   * @param prices - An object containing pricing details (expects `currency` and `trial.amount`).
   */
  const trackGTMBeingCheckoutEvent = useCallback(
    (prices: Prices) => {
      const status = searchParams.get('status');

      if (!isGTMInitialized()) {
        console.warn('GTM not initialized on Checkout page: Event not sent');
        return;
      }

      // Get the correct currency for the current plan and payment type
      const currency = getPlanCurrency(isOneTime, prices);

      // Determine the correct amount to track for analytics:
      // Use the trial price if this is a trial, the one-time price if it's a one-time payment,
      // otherwise use the subscription price based on the selected plan.
      const amount = isTrial
        ? getTrialAmount(prices)
        : isOneTime
          ? getOneTimeAmount(prices)
          : getSubscriptionAmount(prices, plan);

      if (status === 'canceled') {
        sendGTMEvent({ event: 'payment_canceled' });
      } else {
        sendGTMEvent({
          event: 'begin_checkout',
          ecommerce: {
            currency: currency,
            value: amount,
            plan_type: planType,
            plan: getPlanName(planType, plan),
            items: [
              {
                item_name: 'IQ test',
                item_id: 'iqtest',
                price: amount,
                quantity: 1,
              },
            ],
          },
        });
      }
    },
    [searchParams, isTrial, isOneTime, plan]
  );

  /**
   * Sends a Google Tag Manager (GTM) purchase event with eCommerce details.
   *
   * This function pushes a 'purchase' event to the GTM dataLayer,
   * including transaction details like currency, amount, transaction ID,
   * and purchased item information. It ensures GTM is initialized before sending.
   *
   * @param prices - An object containing pricing details (expects `currency` and `trial.amount`).
   * @param checkoutId - A unique identifier for the purchase transaction.
   * @param hasFiredGTM - A React ref object to ensure the event is only sent once.
   */
  const trackGTMPurchaseEvent = useCallback(
    (prices: Prices, checkoutId: string, hasFiredGTM: React.MutableRefObject<boolean>) => {
      if (hasFiredGTM.current) return;

      if (!isGTMInitialized()) {
        console.warn('GTM not initialized: purchase event not sent');
        return;
      }

      // Get the correct currency for the current plan and payment type
      const currency = getPlanCurrency(isOneTime, prices);

      // Determine the correct amount to track for analytics:
      // Use the trial price if this is a trial, the one-time price if it's a one-time payment,
      // otherwise use the subscription price based on the selected plan.
      const amount = isTrial
        ? getTrialAmount(prices)
        : isOneTime
          ? getOneTimeAmount(prices)
          : getSubscriptionAmount(prices, plan);

      sendGTMEvent({
        event: 'purchase',
        ecommerce: {
          currency: currency,
          value: amount,
          transaction_id: checkoutId,
          plan_type: planType,
          plan: getPlanName(planType, plan),
          items: [
            {
              item_name: 'IQ test',
              item_id: 'iqtest',
              price: amount,
              quantity: 1,
            },
          ],
        },
      });

      hasFiredGTM.current = true;
    },
    [isTrial, plan]
  );

  return { trackGTMBeingCheckoutEvent, trackGTMPurchaseEvent };
}
