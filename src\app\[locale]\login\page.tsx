'use client';

import '@/sass/form.scss';
import { throttle } from 'lodash';
import { useContext, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Loader2 } from 'lucide-react';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useRouter, Link } from '@/lib/i18n/routing';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';
import { auth } from '@/utils/firebase';

const Login = () => {
  const t = useTranslations('login');
  const router = useRouter();
  const { identifyUser } = usePostHogAnalytics();
  const { checkoutVersion, paymentSystem } = useContext(SessionContext);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const { user } = useContext(UserContext);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'email') setEmail(value);
    if (name === 'password') setPassword(value);
  };

  const throttledHandleChange = throttle(handleChange, 1000);

  useEffect(() => {
    if (user) router.push('/user/account');
  }, [router, user]);

  const handleSubmit = async (e: React.FormEvent<HTMLButtonElement>) => {
    e.preventDefault();

    setLoading(true);

    try {
      await signInWithEmailAndPassword(auth, email, password);
      identifyUser(email, { email, paymentSystem, checkoutVersion });
      router.push('/user/account');
    } catch (err: any) {
      const errorMessage = err.message;
      const errorCode = err.code;

      setError(true);

      switch (errorCode) {
        case 'auth/invalid-credential':
          setErrorMessage(t('invalid_credential'));
          break;
        case 'auth/invalid-email':
          setErrorMessage(t('invalid_email'));
          break;
        case 'auth/user-disabled':
          setErrorMessage(t('user_disabled'));
          break;
        case 'auth/user-not-found':
          setErrorMessage(t('user_not_found'));
          break;
        case 'auth/wrong-password':
          setErrorMessage(t('wrong_password'));
          break;
        default:
          setErrorMessage(errorMessage);
          break;
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    //@ts-ignore
    <form id="msform" className="flex justify-center flex-col" onSubmit={e => e.preventDefault()} noValidate>
      <h1
        className="fs-title mb-5"
        style={{
          fontSize: 32,
          lineHeight: '112.5%',
        }}>
        {t('title')}
      </h1>
      <fieldset>
        {error && <div className="mb-2">{errorMessage}</div>}
        <input type="email" name="email" placeholder={t('email_placeholder')} onChange={throttledHandleChange} required />
        <input
          type="password"
          name="password"
          placeholder={t('password_placeholder')}
          onChange={throttledHandleChange}
          required
          autoComplete="current-password"
        />
        <div className="flex">
          <p
            style={{
              color: '#191919',
              fontSize: 14,
              fontWeight: 400,
              lineHeight: '157.143%',
            }}
            className="justify-end">
            <Link href="/forgot-password" className="text-primary ml-2 mb-2 block">
              {t('forgot_password')}
            </Link>
          </p>
        </div>
        <button onClick={handleSubmit} className="action-button flex justify-center" style={{ marginTop: 4 }}>
          {loading ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2" /> : ''}
          {t('btn_log_in')}
        </button>
      </fieldset>
    </form>
  );
};

export default Login;
