'use client';

import { memo } from 'react';
import { usePathname } from '@/lib/i18n/routing';
import Copyright from '@/components/Footer/Copyright';
import PaymentMethods from '@/components/Footer/PaymentMethods';
import LegalMenu from '@/components/Footer/LegalMenu';
import MainMenu from '@/components/Footer/MainMenu';
import SupportSection from '@/components/Footer/SupportSection';
import FooterLogo from '@/components/Footer/FooterLogo';
import LanguageSwitcher from '@/components/LanguageSwitcher';

// Pages where footer should be hidden
const FOOTER_HIDDEN_PAGES: ReadonlyArray<string> = [
  '/form',
  '/form-v3',
  '/calculation',
  '/checkout-v2/upsell',
  '/offers',
  '/offers/2',
  '/offers/3',
  '/offers/4',
  '/calculation-v3',
  '/mobile-checkout',
  '/questions',
] as const;

const Footer = () => {
  const activePath = usePathname();
  const shouldShowFooter = !FOOTER_HIDDEN_PAGES.includes(activePath);

  if (!shouldShowFooter) return null;

  return (
    <div className="w-full font-segoe bg-[#F5F5F5]">
      <div className="flex flex-col max-w-[1280px] mx-[16px] py-[24px] md:mx-auto md:py-[48px]">
        <div className="flex flex-col md:flex-row w-full gap-[24px] md:justify-between border-b border-gray-500 pb-[24px]">
          <FooterLogo />
          <div className="flex flex-col md:flex-row gap-[16px] md:gap-[100px] ">
            <SupportSection />
            <div className="flex flex-col md:flex-row gap-[16px] md:gap-[80px] font-semibold tracking-[0]">
              <LegalMenu />
              <MainMenu />
            </div>
          </div>
        </div>

        <div className="flex md:justify-end pt-6">
          <LanguageSwitcher />
        </div>

        <div className="flex flex-col md:flex-row md:justfiy-between gap-[16px] mt-[16px]">
          <Copyright />
          <PaymentMethods />
        </div>
      </div>
    </div>
  );
};

export default memo(Footer);
