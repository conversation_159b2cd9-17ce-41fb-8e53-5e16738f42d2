import { useState } from 'react';
import lscache from 'lscache';
import { PaymentProvider } from '@/store/types';
import { getPaymentSystem } from '@/utils/getPaymentSystem';

/**
 * React hook to manage the current payment system (provider).
 *
 * Initializes the payment system state from local storage (key: 'se-cp_ps') if available,
 * otherwise falls back to the default or environment-configured provider via getPaymentSystem().
 *
 * @example
 * const { paymentSystem, setPaymentSystem } = usePaymentSystem();
 * setPaymentSystem(PaymentProvider.STRIPE);
 */
export function usePaymentSystem() {
  const [paymentSystem, setPaymentSystem] = useState<PaymentProvider>(lscache.get('se-cp_ps') ?? getPaymentSystem());
  return { paymentSystem, setPaymentSystem };
}
