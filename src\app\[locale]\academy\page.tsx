'use client';

import Academy from '@/components/Academy';
import requireAuth from '@/components/requireAuth';
import { PostHogEventEnum } from '@/store/types';
import { useEffect } from 'react';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';

function AcademyPage() {
  const { captureEvent } = usePostHogAnalytics();
  const { isSubscribed } = useSubscriptionPlan();

  useEffect(() => {
    captureEvent(PostHogEventEnum.ACADEMY_PAGE_VIEWED, {});
    // eslint-disable-next-line
  }, []);

  return (
    <div>
      <div className="max-w-7xl px-3 md:px-0 items-center pb-20 m-auto">{isSubscribed && <Academy />}</div>
    </div>
  );
}

export default requireAuth(AcademyPage, ['is_subscribed']);
