import { GoogleTagManager } from '@next/third-parties/google';
import HotJar from '@/components/Tracking/HotJar';

/**
 * AnalyticsProps interface
 */
interface AnalyticsProps {
  siteConfig: {
    gtmId: string;
  };
}

/**
 * AnalyticsTracker component
 *
 * @param param0 - Site configuration object
 * @returns - The Google Tag Manager and HotJar components
 */
export default async function AnalyticsTracker({ siteConfig }: AnalyticsProps) {
  const isTrackingEnabled = true;

  return (
    <>
      {isTrackingEnabled && (
        <>
          <GoogleTagManager gtmId={siteConfig.gtmId} />
          <HotJar />
        </>
      )}
    </>
  );
}
