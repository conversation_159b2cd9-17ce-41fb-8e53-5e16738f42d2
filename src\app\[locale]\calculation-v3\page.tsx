'use client';

import { memo, useEffect, useContext, useState, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { functions } from '@/utils/firebase';
import { httpsCallable } from 'firebase/functions';
import SessionContext from '@/store/SessionContext';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';
import ReviewSlider from '@/components/CheckoutV3/ReviewSlider';
import Swal from 'sweetalert2';
import ProgressBar from '@/components/ProgressBar';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';

const tags = ['Your Logic', 'Your Reaction', 'Your Concentration', 'Your Speed', 'Your Memory'];

const Calculation = () => {
  const router = useRouter();
  const { captureEvent } = usePostHogAnalytics();
  const { user, isSubscribed } = useSubscriptionPlan();
  const { answers, updateSessionId } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const showResults = searchParams.get('showResults');
  const origin = searchParams.get('origin');

  async function generateReportAndCertificate() {
    const generate = httpsCallable(functions, 'generateReportAndCertificate');
    const result = await generate({ answers, showResults });
    return result;
  }

  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startProgress = () => {
    if (intervalRef.current) return; // Already running

    intervalRef.current = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          const promises = [new Promise(res => setTimeout(res, 2 * 1000))];
          if (user && isSubscribed)
            promises.push(generateReportAndCertificate().then((x: any) => updateSessionId(x.data.sessionId)));
          Promise.all(promises).then(x => router.push(isSubscribed ? '/results' : '/form-v3?origin=calculation-v3'));

          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          return 100;
        }
        if (prev === 19) {
          pauseProgress();
          Swal.fire({
            customClass: {
              image: 'mt-[10px] md:mt-[30px] mx-auto mb-0 w-[86px] md:w-[114px] h-[79px] md:h-[106px]',
              htmlContainer:
                'text-center font-inter font-normal text-[14px] md:text-[24px] leading-[32px] text-[#3F425E] tracking-xs p-0 mx-0 md:my-[10px] mb-[5px] mt-[10px]',
              confirmButton:
                'mb-[10px] md:mb-[30px] bg-[#FDF9F1] rounded-[10px] mr-[10px] w-[154px] md:w-[200px] h-[48px] md:h-[60px] font-segoe text-[14px] leading-5 md:text-[20px] md:leading-8 font-semibold text-[#3F425E] tracking-tight',
              cancelButton:
                'mb-[10px] md:mb-[30px] bg-[#FDF9F1] rounded-[10px] ml-[10px] w-[154px] md:w-[200px] h-[48px] md:h-[60px] font-segoe text-[14px] leading-5 md:text-[20px] md:leading-8 font-semibold text-[#3F425E] tracking-tight',
              actions: 'w-full',
              popup: 'rounded-[15px] md:rounded-[20px]',
            },
            imageUrl: '/images/Diamonds.svg',
            imageAlt: 'Check',
            text: 'Are you a problem-solver?',
            showCancelButton: true,
            buttonsStyling: false,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
            showLoaderOnConfirm: true,
            width: 524,
            padding: 10,
            allowOutsideClick: () => !Swal.isLoading(),
            didOpen: () => {
              document.body.classList.add('swal-blur');
            },
            willClose: () => {
              document.body.classList.remove('swal-blur');
            },
          }).then(result => {
            startProgress();
          });
        }

        if (prev === 59) {
          pauseProgress();
          Swal.fire({
            customClass: {
              image: 'mt-[10px] md:mt-[30px] mx-auto mb-0',
              htmlContainer:
                'text-center font-inter font-normal text-[14px] md:text-[24px] leading-[32px] text-[#3F425E] tracking-xs p-0 mx-0 md:my-[10px] mb-[5px] mt-[10px]',
              confirmButton:
                'mb-[10px] md:mb-[30px] bg-[#FDF9F1] rounded-[10px] mr-[10px] w-[154px] md:w-[200px] h-[48px] md:h-[60px] font-segoe text-[14px] leading-5 md:text-[20px] md:leading-8 font-semibold text-[#3F425E] tracking-tight',
              cancelButton:
                'mb-[10px] md:mb-[30px] bg-[#FDF9F1] rounded-[10px] ml-[10px] w-[154px] md:w-[200px] h-[48px] md:h-[60px] font-segoe text-[14px] leading-5 md:text-[20px] md:leading-8 font-semibold text-[#3F425E] tracking-tight',
              actions: 'w-full',
              popup: 'rounded-[15px] md:rounded-[20px]',
            },
            imageUrl: '/images/Diamonds.svg',
            imageWidth: 114,
            imageHeight: 106,
            imageAlt: 'Check',
            text: 'Fast or Careful?',
            showCancelButton: true,
            buttonsStyling: false,
            confirmButtonText: 'Fast',
            cancelButtonText: 'Careful',
            showLoaderOnConfirm: true,
            width: 524,
            padding: 10,
            allowOutsideClick: () => !Swal.isLoading(),
            didOpen: () => {
              document.body.classList.add('swal-blur');
            },
            willClose: () => {
              document.body.classList.remove('swal-blur');
            },
          }).then(result => {
            startProgress();
          });
        }
        return prev + 1; // increment speed
      });
    }, 50); // every 50ms = total ~5s for 100 steps
  };

  const pauseProgress = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  useEffect(() => {
    if (user && !isSubscribed) return;

    setTimeout(() => {
      startProgress();
    }, 1000);
  }, []);

  useEffect(() => {
    if (origin === 'questions') {
      captureEvent(PostHogEventEnum.CALCULATION_PAGE_VIEWED, {});
    }

    // eslint-disable-next-line
  }, [origin]);

  if (user && !isSubscribed) {
    return (
      <div className="w-full items-center flex flex-col">
        <h5 className="mt-5">Please reactivate your subscription to calculate new test results.</h5>
        <button
          className="button primary text-base font-semibold mb-5 rounded-lg mt-5 text-center"
          onClick={() => router.push('/user/subscription')}>
          Reactivate subscription
        </button>
      </div>
    );
  }

  return (
    <div className="pt-[10px] px-4 md:px-0 absolute top-[65px] w-full flex flex-col gap-y-4 bg-[#FFFCFC]">
      <div className="max-w-[1280px] mx-auto md:px-0">
        <div className="mt-[16px] md:mt-[26px] flex flex-col gap-y-4 pb-4 md:pb-[35px]">
          <div className="font-inter font-bold text-[25px] leading-[32px] md:text-[36px] md:leading-[45px] tracking-0 text-[#3F425E]">
            Accessing your
          </div>
          <div className="font-inter font-bold text-[28px] md:text-[36px] leading-[33px] tracking-0 text-primary">
            IQ Score...
          </div>
          <div className="font-inter font-normal text-[18px] leading-[22px] tracking-lg text-[#3F425E]">
            Please hold on while our AI Engine evaluates your responses across the 5 core dimensions of intelligence
          </div>
          <ProgressBar progress={progress} />
          {tags.map((tag: string, i: number) => {
            return (
              <div key={i} className="flex">
                <label className="checkbox-container1 flex flex-wrap items-center">
                  <input className="h-4" checked={progress / 20 - 1 >= i} type="checkbox" id="consent" name="consent" />
                  <span className="checkmark1"></span>
                </label>
                <p className="font-inter font-semibold text-[18px] leading-[24px] text-[#3F425E]">{tag}</p>
              </div>
            );
          })}
        </div>
      </div>
      <div className="w-full">
        <div className="max-w-[1280px] mx-auto pb-[30px] md:px-0 md:-mt-[30px]">
          <ReviewSlider page="calculation" />
        </div>
      </div>
    </div>
  );
};
export default memo(Calculation);
