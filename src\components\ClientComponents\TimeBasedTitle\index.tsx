import { useContext, useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { calculateBestCategory } from '@/utils/results';
import UiContext from '@/store/UiContext';
import SessionContext from '@/store/SessionContext';

const TimeBasedTitle = () => {
  const t = useTranslations('checkout');
  const { answers } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const [minutes, setMinutes] = useState<number>(Math.floor(time / 60));
  const [seconds, setSeconds] = useState<number>(time - minutes * 60);
  const bestCategory = calculateBestCategory({ answers });

  useEffect(() => {
    setMinutes(Math.floor(time / 60));
  }, [time]);

  useEffect(() => {
    setSeconds(time - minutes * 60);
  }, [time, minutes]);

  return (
    <div>
      <h1
        className='fs-title mb-5 text-center'
        style={{
          fontSize: 32,
          lineHeight: '112.5%',
        }}
      >
        {t('get_results_now')}
      </h1>
      <p className='mb-8 text-center max-w-[335px]' style={{ margin: '0 auto 32px auto' }}>
        {t('completed_test_in')}
        <span className='text-primary'>{` ${minutes}:${seconds / 10 >= 1 ? '' : '0'}${seconds} ${t('minutes')}`}</span>,
        {t('showing_strong')}
        <span className='text-primary'> {` ${bestCategory || 'Analytical Thinking'} `} </span>
        {t('skills')}.
      </p>
    </div>
  );
};

export default TimeBasedTitle;
