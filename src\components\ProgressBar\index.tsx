'use client';

import React from 'react';
interface ProgressBarProps {
  progress: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
  return (
    <div className="relative w-full bg-gray-300 h-[22px] md:w-[798px] rounded-[20px] overflow-hidden">
      <div
        className="transition-all ease-in-out bg-primary border-[11px] md:border-[11px] border-primary text-white h-[22px]"
        style={{
          width: `${progress}%`,
        }}></div>
      <div className="absolute top-0 left-0 text-center w-full ">
        <span className="text-white font-segoe font-semibold text-[15px] leading-[20px]">{progress}%</span>
      </div>
    </div>
  );
};

export default ProgressBar;
