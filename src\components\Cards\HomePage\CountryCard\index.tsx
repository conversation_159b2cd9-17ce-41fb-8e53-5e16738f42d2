import { memo } from 'react';
import { useTranslations } from 'next-intl';
import { Country } from '@/hooks/useCountryTranslations';
import CountryFlag from '@/components/CountryFlag';

interface CountryCardProps {
  country: Country;
  i: number;
  isFullList: boolean;
  totalCount: number;
}

const CountryCard = ({
  country,
  i,
  isFullList,
  totalCount,
}: CountryCardProps) => {
  const t = useTranslations('home_page');
  const { id, iq, name } = country;

  return (
    i < (isFullList ? totalCount : 15) && (
      <li
        className='bg-grey-bg flex justify-between items-center font-semibold lg:min-w-[400px] max-w-[94%] p-[20px] pl-[15px] xs:p-[30px] text-md/6 xs:text-lg/7 sm:text-xl/7'
        style={{
          color: '#191919',
        }}
      >
        <div className='flex justify-between'>
          <div className='mr-1 lg:mr-10 content-center w-6'>{`${i + 1}.`}</div>
          <div className='flex items-center justify-start w-[160px] xxs:w-[220px] sm:w-[200px] md:w-[220px]'>
            <CountryFlag
              className='mr-6'
              countryId={id}
              countryName={name}
            />
            <div className='text-left'>{name}</div>
          </div>
        </div>
        <div>{iq.toFixed()}</div>
      </li>
    )
  );
};

export default memo(CountryCard);
