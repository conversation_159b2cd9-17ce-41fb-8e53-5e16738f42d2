export const categories = {
    "Caring Gestures": ["Lifts a burden when I’m struggling", "Quietly takes care of a task I hate", "Fixes something I’ve been avoiding", "Cooks dinner when I’m stressed", "Goes out of the way to do something thoughtful", "Handles an errand I’ve been dreading", "Surprises me with coffee to brighten my day", "Takes over when I’m overwhelmed", "Helps with a project I’m stuck on", "Jumps in to help with something I’m avoiding", "Steps in when I can’t manage alone", "Does something small to ease my day", "Completes a task to make me smile", "Offers help before I ask"],
    "Deep Understanding": ["Notices my mood and knows what I need", "Remembers all the little things I love", "Gets what I’m thinking without words", "Hands me a blanket before I even ask", "Brings my favorite drink without asking", "Anticipates what I need and does it", "Understands me during silent moments", "Remembers exactly how I like my coffee", "Senses when I need cheering up", "Senses when I’m reflective and listens", "Understands me with just a glance", "Makes me feel known just by being there", "Knows what I need before I do", "Knows when I need quiet time"],
    "Loving Words": ["Tells me I made a difference by being there", "Thanks me for something small I did", "Cheers me on during something boring", "Sends a sweet message to make me smile", "Tells me I’m amazing after a tough day", "Leaves a cute note to brighten my day", "Notices all my hard work this week", "Says “You’ve got this!” during stress", "Tells me I’m great when I’m stuck", "Thanks me for tidying up a little", "Says something sweet after a hard meeting", "Praises me for a quick errand", "Says something loving after a bad day", "Says something nice after a little fight"],
    "Meaningful Gifts": ["Gives a gift that reflects my taste", "Gives a gift tied to something I love", "Gives a custom gift that feels unique", "Gifts me something deeply meaningful", "Gives me something I’ll treasure forever", "Gives me a gift with personal significance", "Gives me something that reminds me of us", "Gives a carefully thought-out gift", "Surprises me with a thoughtful gift", "Makes a moment feel special with a thoughtful gift", "Gives a small gift that shows I’m valued", "Gives me something I’ve been hoping for", "Gives me a gift to celebrate a special occasion", "Surprises me with a gift that shows they know me"],
    "Nourishing Care": ["Surprises me with comfort food", "Cooks a warm meal after a hard day", "Makes a meal with my favorite ingredients", "Has my favorite food waiting to celebrate", "Hands me the snack I’ve been craving", "Cooks a dish that feels like home", "Serves me a meal when I’m exhausted", "Surprises me with breakfast in bed", "Whips up something special to celebrate", "Plans a dinner with all the foods I love", "Prepares a dish with extra care and love", "Makes a meal just to cheer me up", "Serves something warm when I’m feeling down", "Surprises me with a homemade treat"],
    "Shared Experiences": ["Plans a special day for just us", "Spends an afternoon with me, phone-free", "Suggests visiting a new place together", "Focuses on me during our quiet time", "Plans a surprise trip just for us", "Cooks dinner with me and enjoys it together", "Laughs with me while watching silly videos", "Gets excited about our weekend plans", "Watches our favorite shows together", "Spends a cozy night in, just us", "Tries a new hobby or activity with me", "Surprises me with a lunch date", "Takes me on a weekend adventure", "Turns a simple walk into a special moment"],
    "Touch of Affection": ["Wraps their arms around me when I’m sad", "Rests their hand on my shoulder", "Hugs me after a stressful call", "Holds my hand when we cross the street", "Hugs me when I bring takeout", "Gives me a comforting touch when I’m upset", "Gives me a sweet hug after shopping", "Surprises me with a hug during laundry", "Cuddles with me during Netflix", "Touches my back while I’m cooking", "Lets me lean on them during a movie", "Playfully nudges me when I spill coffee", "Squeezes my hand while walking", "Snuggles close on a quiet night"],
}
