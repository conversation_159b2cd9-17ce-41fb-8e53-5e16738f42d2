'use client';

import { createContext, FC, ReactNode, Suspense, useEffect, useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import moment from 'moment';
import { useRouter } from '@/lib/i18n/routing';
import { PaymentProvider } from '@/store/types';
import { PlanType, PlanTypeEnum, SubscriptionPlan, SubscriptionPlanEnum } from '@/types/plan-types';
import { auth, functions } from '@/utils/firebase';

interface ToolkitsClaims {
  has_career_toolkit_access?: boolean;
  has_stress_toolkit_access?: boolean;
  has_brain_toolkit_access?: boolean;
  has_toolkits_bundle_access?: boolean;
}

interface SubscriptionClaims {
  active_plan: SubscriptionPlanEnum;
  active_plan_type: PlanTypeEnum;
  is_subscribed?: boolean;
  current_period_end?: string | number;
}

interface User extends ToolkitsClaims {
  uid: string;
  email: string | null;
  displayName: string | null;
  is_subscribed: boolean;
  has_toolkits_access?: boolean;
  current_period_end: any;
  scheduled_cancel_at: any;
  active_plan: SubscriptionPlan;
  active_plan_type: PlanType;
  activeSubscriptionType: PaymentProvider;
}

interface TrainingQuestion {
  id: number;
  originalQuestionId: number;
  correctAnswerId: number;
  category: string;
  explanation: string;
  numberOfAnswers: number;
}

type TrainingQuestionsMap = {
  [prop: string]: TrainingQuestion[];
};

interface UserContextProps {
  user: User | null | undefined;
  logout: () => void;
  trainingQuestions: TrainingQuestionsMap;
}

const UserContext = createContext<UserContextProps>({
  user: null,
  logout: () => {},
  trainingQuestions: {},
});

const UserProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null | undefined>(undefined);
  const [trainingQuestions, setTrainingQuestions] = useState({});
  const router = useRouter();

  async function getTrainingQuestions() {
    const getQuestions = httpsCallable(functions, 'getTrainingQuestions');
    const result: any = await getQuestions();
    setTrainingQuestions(result.data.questions);
  }

  /**
   * Determines whether the user currently has an active subscription or valid one-time access.
   *
   * For recurring or non–one-time plans, trusts the backend’s `is_subscribed` flag.
   * For one-time (“Fix” + “Onetime”) plans, checks that the current time is still
   * on or before the stored `current_period_end`.
   *
   * @param claims – The JWT claims containing subscription details.
   * @returns `true` if the user is subscribed or within their one-time access period; otherwise `false`.
   */
  const computeIsSubscribed = (claims: SubscriptionClaims): boolean => {
    const { active_plan, active_plan_type, is_subscribed, current_period_end } = claims;

    // Non–one-time plans: use the backend flag
    if (active_plan !== SubscriptionPlanEnum.Fix || active_plan_type !== PlanTypeEnum.Onetime) {
      return Boolean(is_subscribed);
    }

    // One-time plan: ensure we have an expiration timestamp
    if (!current_period_end) {
      return false;
    }

    // Access is valid if now is on or before period end
    const now = moment();
    const expiresAt = moment(current_period_end);
    return now.isAfter(expiresAt);
  };

  /**
   * Determines toolkit access for each individual toolkit.
   *
   * @param claims – The JWT claims containing subscription details.
   * @param toolkitType – Which toolkit to check access for
   * @returns `true` if the user has access to the specified toolkit; otherwise `false`.
   */
  const computeToolkitAccess = (claims: ToolkitsClaims, toolkitType: 'career' | 'stress' | 'brain'): boolean => {
    const { has_career_toolkit_access, has_stress_toolkit_access, has_brain_toolkit_access } = claims;

    switch (toolkitType) {
      case 'career':
        return Boolean(has_career_toolkit_access);
      case 'stress':
        return Boolean(has_stress_toolkit_access);
      case 'brain':
        return Boolean(has_brain_toolkit_access);
      default:
        return false;
    }
  };

  /**
   * Parses a date string or number into a Date object.
   * @param date - The date string or number to parse.
   * @param debug - Whether to log the parsed date for debugging purposes.
   * @returns A Date object if the date is valid, otherwise null.
   */
  const parseDateFormat = (date: string | number, debug?: boolean) => {
    if (date === null) {
      return null;
    }

    const date1 = new Date((date as number) * 1000);
    const date2 = new Date(date);

    const finalDate = isNaN(date1.getTime()) ? date2 : date1;

    return !isNaN(finalDate.getTime()) ? finalDate : null;
  };

  /**
   * Syncs the current user’s profile and subscription info.
   * @param fb_user The Firebase Auth user object (or null/undefined).
   */
  const updateUser = async (fb_user: any) => {
    // Early exit if there's no authenticated user
    if (!fb_user) {
      setUser(null);
      localStorage.removeItem('user');
      return;
    }

    // Destructure the bits we need from fb_user
    const { uid, email, displayName } = fb_user;
    const claims = (await fb_user.getIdTokenResult()).claims;

    // Destructure the relevant claims
    const {
      current_period_end,
      scheduled_cancel_at,
      active_plan = SubscriptionPlanEnum.Monthly,
      active_plan_type = PlanTypeEnum.Subscription,
      activeSubscriptionType,
    } = claims;

    // Subscription status
    const is_subscribed = computeIsSubscribed(claims);

    // Toolkit access
    const has_toolkits_access = Boolean(claims.has_toolkits_access);
    const has_toolkits_bundle_access = Boolean(claims.has_toolkits_bundle_access);
    const has_career_toolkit_access = computeToolkitAccess(claims, 'career');
    const has_stress_toolkit_access = computeToolkitAccess(claims, 'stress');
    const has_brain_toolkit_access = computeToolkitAccess(claims, 'brain');

    // Build the user toolkits access payload
    const toolkits = {
      has_toolkits_access,
      has_career_toolkit_access,
      has_stress_toolkit_access,
      has_brain_toolkit_access,
      has_toolkits_bundle_access,
    };

    // Build the user payload
    const user = {
      uid,
      email,
      displayName,
      current_period_end: parseDateFormat(current_period_end),
      scheduled_cancel_at: parseDateFormat(scheduled_cancel_at, true),
      is_subscribed,
      active_plan,
      active_plan_type,
      activeSubscriptionType,
      ...toolkits,
    };

    // Fetch extra data only if subscribed
    if (is_subscribed) {
      getTrainingQuestions();
    }

    // Persist to localStorage and update state
    const serialized = JSON.stringify(user);
    localStorage.setItem('user', serialized);
    setUser(user);
  };

  /**
   * Logs out the user and clears the localStorage.
   */
  const logout = async () => {
    await auth.signOut();
    localStorage.clear();
    router.push('/');
  };

  useEffect(() => {
    const userLocalStorage = localStorage.getItem('user');
    if (userLocalStorage) setUser(JSON.parse(userLocalStorage));

    const unsubscribeIdTokenChange = auth.onIdTokenChanged(updateUser);
    const unsubscribeAuthState = auth.onAuthStateChanged(updateUser);

    return () => {
      unsubscribeIdTokenChange();
      unsubscribeAuthState();
    };
  }, []);

  return (
    <UserContext.Provider value={{ user, logout, trainingQuestions }}>
      <Suspense>{children}</Suspense>
    </UserContext.Provider>
  );
};

export { UserContext, UserProvider };
