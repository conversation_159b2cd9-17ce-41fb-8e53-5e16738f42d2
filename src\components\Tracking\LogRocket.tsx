'use client';
import { useContext, useEffect, memo } from 'react';
import * as LogRocketClient from 'logrocket';
import { usePathname } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

const LogRocket = () => {
  const pathname = usePathname();
  const { sessionId, formData: { email }, } = useContext(SessionContext);
  const { siteConfig } = useContext(SessionContext);
  
  useEffect(() => {
    if (!pathname.includes('results')) {
      LogRocketClient.init(`ujqmjd/${siteConfig.domain}-app`);
      LogRocketClient.identify(sessionId, { email, });
    }
  }, [email, sessionId, pathname, siteConfig]);

  return null;
};

export default memo(LogRocket);
