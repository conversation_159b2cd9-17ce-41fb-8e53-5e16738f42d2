import { FixedCurrency, FixedCurrencyEnum } from '@/types/plan-types';

// Compliance time threshold in seconds
export const COMPLIANCE_TIME_THRESHOLD = 330;

// Compliance checkout version
export const COMPLIANCE_CHECKOUT_VERSION = 'v1';

// Allowed fixed prices for each currency
export const ALLOWED_FIXED_PRICES: Record<FixedCurrency, { prices: readonly number[]; default_price: number }> = {
  [FixedCurrencyEnum.USD]: {
    prices: [4.9, 9.9, 14.9],
    default_price: 9.9,
  },
};
