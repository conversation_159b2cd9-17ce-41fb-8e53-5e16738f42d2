'use client';

import { useState, useContext, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { z } from 'zod';
import { Loader2 } from 'lucide-react';
import SessionContext from '@/store/SessionContext';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import { useSaveSession } from '@/hooks/useSaveSession';
import UiContext from '@/store/UiContext';

const emailCheck = httpsCallable(functions, 'checkIfEmailExists');

const OrderForm = () => {
  const t = useTranslations('form');
  const router = useRouter();
  const searchParams = useSearchParams();
  const origin = searchParams.get('origin');
  const { captureEvent, identifyUser } = usePostHogAnalytics();
  const { checkoutVersion, formData, updateFormData, siteConfig, paymentSystem } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const [messages, setMessages] = useState<{ email: string; name: string }>({ email: '', name: '' });
  const [optedIn, setOptedIn] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { saveSession } = useSaveSession();

  useEffect(() => {
    if (origin === 'questions') {
      captureEvent(PostHogEventEnum.ACCOUNT_CREATION_PAGE_VIEWED, {});
    }
    // eslint-disable-next-line
  }, [origin]);

  const onSubmit = async () => {
    setLoading(true);
    captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED, { email: formData.email, name: formData.name });
    const isValidEmail = z.string().email().safeParse(formData.email).success;
    if (!isValidEmail) setMessages(prev => ({ ...prev, email: t('inputs.email.error.invalid') }));

    if (!formData.name) setMessages(prev => ({ ...prev, name: t('inputs.name.error') }));

    if (!isValidEmail || !formData.name) {
      setLoading(false);
      captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_ERROR, { email: formData.email, name: formData.name });
      return;
    }

    const emailCheckResponse: any = await emailCheck({ email: formData.email });

    if (emailCheckResponse.data.exists) {
      setMessages({
        email: t('inputs.email.error.in_use'),
        name: messages.name,
      });
      setLoading(false);
      captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_ERROR, { email: formData.email, name: formData.name });
      return;
    }

    captureEvent(PostHogEventEnum.ACCOUNT_FORM_SUBMITTED_SUCCESS, { email: formData.email, name: formData.name });
    identifyUser(formData.email, { email: formData.email, name: formData.name, paymentSystem, checkoutVersion });

    // Save session data
    await saveSession('iq', time);
    router.push('/checkout');
  };

  return (
    //@ts-ignore
    <form id="msform" onSubmit={e => e.preventDefault()} noValidate>
      <h1 className="fs-title mb-5" style={{ fontSize: 32, lineHeight: '112.5%' }}>
        {t('title')}
      </h1>
      <p className="mx-auto mt-0 mb-8 h:mb-4 max-w-[275px]">{t('subtitle')}</p>

      <fieldset>
        <input
          type="text"
          name="name"
          placeholder={t('inputs.name.placeholder')}
          value={formData.name ?? ''}
          required
          onChange={e => {
            updateFormData({ ...formData, [e.target.name]: e.target.value });
            setMessages({ email: messages.email, name: '' });
          }}
        />
        {messages.name && <span className="block mb-2 text-left -mt-2 pl-[10px]">{messages.name}</span>}
        <input
          type="email"
          name="email"
          placeholder={t('inputs.email.placeholder')}
          value={formData.email ?? ''}
          required
          onChange={e => {
            updateFormData({ ...formData, [e.target.name]: e.target.value });
            setMessages({ email: '', name: messages.name });
          }}
        />
        {messages.email && (
          <span className="block mb-2 text-left pl-[10px] text-red-500 font-bold">{messages.email}</span>
        )}
        <div className="flex">
          <label className="checkbox-container flex flex-wrap items-center">
            <input className="h-4" type="checkbox" id="consent" name="consent" onClick={() => setOptedIn(!optedIn)} />
            <span className="checkmark"></span>
          </label>
          <p
            style={{
              color: '#191919',
              textAlign: 'left',
              fontSize: 14,
              fontWeight: 400,
              lineHeight: '157.143%',
              marginTop: 14,
            }}>
            {t.rich('consent.text', {
              siteName: siteConfig.siteName,
              terms: chunks => (
                <a href="/terms-and-conditions" className="text-primary" target="_blank">
                  {chunks}
                </a>
              ),
              privacy: chunks => (
                <a target="_blank" href="/privacy" className="text-primary">
                  {chunks}
                </a>
              ),
            })}
          </p>
        </div>
        <button disabled={loading || !optedIn} onClick={onSubmit} className="action-button flex justify-center">
          {loading ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2" /> : ''}
          {t('buttons.proceed')}
        </button>
      </fieldset>
    </form>
  );
};

export default OrderForm;
