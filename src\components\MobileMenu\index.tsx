'use client';

import { FC, useContext, useMemo } from 'react';
import { menuItems } from '@/app/config';
import MenuItem from '@/components/Menu/MenuItem';
import { usePathname } from '@/lib/i18n/routing';
import useLayoutStore from '@/store/useLayoutStore';
import { UserContext } from '@/store/UserContext';

interface MobileMenuProps {}

const MobileMenu: FC<MobileMenuProps> = () => {
  const activePath = usePathname();
  const { user } = useContext(UserContext);
  const { mobileOpen } = useLayoutStore();
  
  // Optimize filtering logic with useMemo and clearer conditions
  const visibleMenuItems = useMemo(() => {
    return menuItems.filter((item) => {
      // Don't show pricing on results page
      const isPricingOnResults = activePath === '/results' && item.id === 'pricing';
      if (isPricingOnResults) return false;
      
      // Filter based on user authentication status
      if (!user) {
        // User not logged in: hide member-only items
        return !item.memberOnly;
      } else {
        // User logged in: hide items that should be hidden when logged in
        return !item.hideIfLoggedIn;
      }
    });
  }, [activePath, user]);
  
  return (
    <div
      className={`transition-transform duration-500 ease-in-out ${
        mobileOpen ? "translate-x-0" : "translate-x-full"
      } w-screen h-screen bg-white z-10 fixed top-0 left-0 flex flex-col justify-center items-center`}
    >
      <div className="flex flex-col px-2">
        <nav className="flex flex-col flex-wrap gap-10 text-center lg:flex mobile-menu">
          {visibleMenuItems.map((item) => (
            <MenuItem key={item.id} item={item} />
          ))}
        </nav>
      </div>
    </div>
  );
};

export default MobileMenu;
