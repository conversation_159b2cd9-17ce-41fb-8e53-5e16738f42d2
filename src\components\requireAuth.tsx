'use client';

import { useEffect, useContext } from 'react';
import { useRouter } from '@/lib/i18n/routing';
import { UserContext } from '@/store/UserContext';

export default function requireAuth(WrappedComponent: any, allowedClaims: string[] = []) {
  return function WithAuth(props: any) {
    const { user } = useContext(UserContext);
    const router = useRouter();

    useEffect(() => {
      if (user === undefined) return;
      if (!user) router.push('/login');
      //@ts-ignore
      else if (allowedClaims.length > 0 && !allowedClaims.some(claim => user[claim])) {
        if (user) router.push('/user/subscription');
      }
    }, [user, router]);

    return !!user && <WrappedComponent {...props} />;
  };
}
