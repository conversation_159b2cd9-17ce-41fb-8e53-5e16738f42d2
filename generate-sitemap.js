const fs = require("fs");
const path = require("path");
const siteConfig = require("./site.config.js").getSiteConfig();

const sitemap = `
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://${siteConfig.domain}</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/insights</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/about-us</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/faq</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/questions</loc>   <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/checkout</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/calculation</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>https://${siteConfig.domain}/form</loc>
    <lastmod>2024-04-10T15:02:24.021Z</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
</urlset>
`;

fs.writeFileSync(path.join(__dirname, "./src/app/sitemap.xml"), sitemap);

console.log("✅ Generated sitemap.xml");
