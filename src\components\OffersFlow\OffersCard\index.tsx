'use client';

interface OffersCardProps {
  icon: string;
  header: string;
  text: string;
}

function OffersCard({ icon, header, text }: OffersCardProps) {
  return (
    <div className="max-w-[412px] flex flex-col md:p-[24px] p-[16px] md:gap-[24px] gap-[10px] rounded-[12px] bg-white border border-[#C1CFE9]/45 shadow-[2px_8px_14px_0px_#6881B114] shadow-[1px_1px_1px_1px_#8DA0BC14]">
      <img src={icon} className="w-[54px] h-auto"></img>
      <div className="flex flex-col gap-[10px] font-ppmori">
        <span className="font-semibold text-[20px] leading-[26px] text-[#191919]">{header}</span>
        <span className="font-normal text-[16px] leading-[22px] text-[#8893AC]">{text}</span>
      </div>
    </div>
  );
}

export default OffersCard;
