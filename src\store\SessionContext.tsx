'use client';

import { createContext, useCallback, useEffect, useState, ReactNode } from 'react';
import Cookies from 'js-cookie';
import lscache from 'lscache';
import { v4 as uuid } from 'uuid';
import { Offers, Prices } from '@/app/prices';
import {
  usePaymentSystem,
  useCheckoutVersion,
  usePlanType,
  useSubscriptionPlan,
  usePaymentStatus,
  useFixedCurrency,
  useFixedPrice,
} from '@/store/hooks';
import {
  Answer,
  CheckoutVersion,
  FormData,
  MerchantDataType,
  PaymentProvider,
  SessionContextProps,
  SessionData,
} from '@/store/types';
import { update } from '@/store/utils';
import {
  FixedCurrency,
  FixedPrice,
  PlanType,
  PlanTypeEnum,
  SubscriptionPlan,
  SubscriptionPlanEnum,
} from '@/types/plan-types';
import { getPaymentSystem } from '@/utils/getPaymentSystem';
import { getSiteConfig } from '../../site.config';

const SessionContext = createContext<SessionContextProps>({
  sessionId: '',
  locale: '',
  questionId: 1,
  answers: [],
  formData: { name: '', email: '' },
  checkoutId: '',
  paymentStatus: '',
  getIsTrial: () => true,
  getIsOneTime: () => false,
  prices: {
    country: '',
    currency: '',
    symbol: '',
    subscription: { amount: 0, formatted: '' },
    trial: { amount: 0, formatted: '' },
    oneTime: { amount: 0, formatted: '' },
    vatIncluded: false,
  },
  stripeInvoices: { data: [] },
  solidgateInvoices: { data: [] },
  siteConfig: getSiteConfig(),
  merchantData: '',
  results: [],
  emotionalScores: {},
  stage: 1, // Added for test progress
  clickedButton: null, // Added for selected answer
  testAnswers: [], // Added for test answers
  emotionalStage: 1, // Added for emotional intelligence test progress
  emotionalClickedButton: null, // Added for selected answer in emotional intelligence test
  emotionalTestAnswers: [], // Added for emotional intelligence test answers
  updateSessionId: async (_: string) => {},
  updateLocale: async (_: string) => {},
  updateQuestionId: async (_: number) => {},
  updateAnswer: async (_: { questionId: number; answerId: number }) => {},
  updateAllAnswers: async (_: Answer[]) => {},
  updateFormData: async (_: FormData) => {},
  updateCheckoutId: async (_: string) => {},
  updatePaymentStatus: async (_: string) => {},
  planType: PlanTypeEnum.Subscription,
  plan: SubscriptionPlanEnum.Monthly,
  paymentSystem: getPaymentSystem(),
  checkoutVersion: 'v1',
  fetchAllSessionData: () => ({
    sessionId: '',
    locale: '',
    questionId: 1,
    answers: [],
    formData: { email: '', name: '' },
    checkoutId: '',
    paymentStatus: '',
    stage: 1, // Added for test progress
    clickedButton: null, // Added for selected answer
    testAnswers: [], // Added for test answers
    emotionalStage: 1, // Added for emotional intelligence test progress
    emotionalClickedButton: null, // Added for selected answer in emotional intelligence test
    emotionalTestAnswers: [], // Added for emotional intelligence test answers
  }),
  updateAllSessionData: async (_: SessionData) => {},
  resetAnswers: async () => {},
  updateStripeInvoices: async () => {},
  updateSolidgateInvoices: async () => {},
  updateResults: async () => {},
  updatePlanType: async (_: PlanType) => {},
  updateFixedPrice: async (_: FixedPrice) => {},
  updateFixedCurrency: async (_: FixedCurrency) => {},
  updatePlan: async (_: SubscriptionPlan) => {},
  updateCheckoutVersion: async (_: CheckoutVersion) => {},
  updatePaymentSystem: (_: PaymentProvider) => {},
  updateEmotionalScores: async () => {},
  updateStage: async (_: number) => {}, // Added for test progress
  updateClickedButton: async (_: string | null) => {}, // Added for selected answer
  updateTestAnswers: async (_: string[]) => {}, // Added for test answers
  resetTestState: async () => {}, // Added to reset test-related state
  updateEmotionalStage: async () => {}, // Added for emotional intelligence test progress
  updateEmotionalClickedButton: async () => {}, // Added for selected answer in emotional intelligence test
  updateEmotionalTestAnswers: async () => {}, // Added for emotional intelligence test answers
  resetEmotionalTestState: async () => {}, // Added to reset emotional intelligence test state,
  updateMerchantData: async (_: MerchantDataType) => {},
});

interface SessionProviderProps {
  children: ReactNode;
  prices: Prices;
  offers: Offers;
  siteConfig: ReturnType<typeof getSiteConfig>;
}

export const SessionProvider: any = (props: SessionProviderProps) => {
  const [prices] = useState<Prices>(props.prices);
  const [offers] = useState<Offers>(props.offers);
  const [siteConfig] = useState(props.siteConfig);
  const [sessionId, setSessionId] = useState<string>(lscache.get('se-sessionId') || '');
  const [locale, setLocale] = useState<string>(lscache.get('se-locale') || '');
  const [questionId, setQuestionId] = useState<number>(1);
  const [answers, setAnswers] = useState<Answer[]>(lscache.get('se-answers') || []);
  const [formData, setFormData] = useState<FormData>(lscache.get('se-formData') || { email: '', name: '' });
  const [checkoutId, setCheckoutId] = useState<string>(lscache.get('se-checkoutId') || '');
  const { paymentStatus, setPaymentStatus } = usePaymentStatus();
  const { planType, setPlanType } = usePlanType();
  const { plan, setPlan } = useSubscriptionPlan();
  const { fixedPrice, setFixedPrice } = useFixedPrice();
  const { fixedCurrency, setFixedCurrency } = useFixedCurrency();
  const { checkoutVersion, setCheckoutVersion } = useCheckoutVersion();
  const { paymentSystem, setPaymentSystem } = usePaymentSystem();
  const [results, setResults] = useState<any>(lscache.get('se-results') || []);
  const [emotionalScores, setEmotionalScores] = useState<any>(lscache.get('se-emotionalScores') || {});
  const [stage, setStage] = useState<number>(lscache.get('se-stage') || 1); // Added for test progress
  const [clickedButton, setClickedButton] = useState<string | null>(lscache.get('se-clickedButton') || null); // Added for selected answer
  const [testAnswers, setTestAnswers] = useState<string[]>(lscache.get('se-testAnswers') || []); // Added for test answers
  const [emotionalStage, setEmotionalStage] = useState<number>(lscache.get('se-emotionalStage') || 1);
  const [stripeInvoices, setStripeInvoices] = useState<{ data: any[] }>(
    lscache.get('se-stripeInvoices') || { data: [] }
  );
  const [solidgateInvoices, setSolidgateInvoices] = useState<{ data: any[] }>(
    lscache.get('se-solidgateInvoices') || { data: [] }
  );
  const [emotionalClickedButton, setEmotionalClickedButton] = useState<string | null>(
    lscache.get('se-emotionalClickedButton') || null
  );
  const [merchantData, setMerchantData] = useState<MerchantDataType>(lscache.get('se-merchantData') || '');
  const [emotionalTestAnswers, setEmotionalTestAnswers] = useState<number[]>(
    lscache.get('se-emotionalTestAnswers') || []
  );

  /**
   * Determines whether the trial experience is enabled for the current user.
   *
   * - For Stripe: disables trial for one-time fixed plans.
   * - For all providers: disables trial for certain countries if needed.
   * - On server: always enables trial.
   */
  const getIsTrial = useCallback((): boolean => {
    const isStripe = paymentSystem === PaymentProvider.STRIPE;
    // Only Stripe disables trial for one-time fixed plans
    if (isStripe && plan === SubscriptionPlanEnum.Fix && planType === PlanTypeEnum.Onetime) {
      return false;
    }

    if (typeof window === 'undefined') return true;

    const disabledCountries: string[] = []; // Add e.g., ['AE', 'US'] to disable trials
    const locale = Cookies.get('locale');

    return !disabledCountries.includes(locale ?? '');
  }, [plan, planType, paymentSystem]);

  /**
   * Determines if the current plan is a fixed one-time payment (Stripe only).
   *
   * @returns {boolean} True if the user is on a one-time fixed plan with Stripe.
   */
  const getIsOneTime = useCallback((): boolean => {
    const isStripe = paymentSystem === PaymentProvider.STRIPE;
    return isStripe && plan === SubscriptionPlanEnum.Fix && planType === PlanTypeEnum.Onetime;
  }, [plan, planType, paymentSystem]);

  const createSession = useCallback((customSessionId?: string) => {
    const newSessionId = customSessionId || uuid();
    setSessionId(newSessionId);
    lscache.set('se-sessionId', newSessionId, 525601);
  }, []);

  useEffect(() => {
    const sessionIdFromLocalStorage = lscache.get('se-sessionId');
    if (sessionIdFromLocalStorage !== null) {
      hydrateFromLocalStorage();
    } else {
      createSession();
    }
  }, [createSession]);

  const hydrateFromLocalStorage = () => {
    const sessionIdFromLocalStorage = lscache.get('se-sessionId');
    const questionIdFromLocalStorage = lscache.get('se-questionId');
    const answersFromLocalStorage = lscache.get('se-answers');
    const formDataFromLocalStorage = lscache.get('se-formData');
    const checkoutIdFromLocalStorage = lscache.get('se-checkoutId');
    const paymentStatusFromLocalStorage = paymentSystem;
    const stageFromLocalStorage = lscache.get('se-stage');
    const clickedButtonFromLocalStorage = lscache.get('se-clickedButton');
    const testAnswersFromLocalStorage = lscache.get('se-testAnswers');

    sessionIdFromLocalStorage && setSessionId(sessionIdFromLocalStorage);
    questionIdFromLocalStorage && setQuestionId(questionIdFromLocalStorage);
    answersFromLocalStorage && setAnswers(answersFromLocalStorage);
    formDataFromLocalStorage && setFormData(formDataFromLocalStorage);
    checkoutIdFromLocalStorage && setCheckoutId(checkoutIdFromLocalStorage);
    paymentStatusFromLocalStorage && setPaymentStatus(paymentStatusFromLocalStorage);
    stageFromLocalStorage && setStage(stageFromLocalStorage);
    clickedButtonFromLocalStorage && setClickedButton(clickedButtonFromLocalStorage);
    testAnswersFromLocalStorage && setTestAnswers(testAnswersFromLocalStorage);
  };

  const updateSessionId = useCallback(
    async (newSessionId: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'sessionId',
        newVariable: newSessionId,
        oldVariable: sessionId,
        setter: setSessionId,
        getter: () => sessionId,
      });
    },
    [sessionId]
  );

  // Update plan type in localStorage
  const updatePlanType = useCallback(
    async (newPlanType: PlanType) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_pt',
        newVariable: newPlanType,
        oldVariable: planType,
        setter: setPlanType,
        getter: () => newPlanType,
      });
    },
    [planType]
  );

  // Update fixed price in localStorage
  const updateFixedPrice = useCallback(
    async (newPrice: FixedPrice) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_fp',
        newVariable: newPrice,
        oldVariable: fixedPrice,
        setter: setFixedPrice,
        getter: () => newPrice,
      });
    },
    [fixedPrice]
  );

  // Update fixed currency in localStorage
  const updateFixedCurrency = useCallback(
    async (newCurrency: FixedCurrency) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_fpc',
        newVariable: newCurrency,
        oldVariable: fixedCurrency,
        setter: setFixedCurrency,
        getter: () => newCurrency,
      });
    },
    [fixedCurrency]
  );

  // Update price plan in localStorage
  const updatePlan = useCallback(
    async (newPlan: SubscriptionPlan) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_sp',
        newVariable: newPlan,
        oldVariable: plan,
        setter: setPlan,
        getter: () => newPlan,
      });
    },
    [plan]
  );

  // Update checkout version in localStorage
  const updateCheckoutVersion = useCallback(
    async (newCheckoutVersion: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_cv',
        newVariable: newCheckoutVersion,
        setter: setCheckoutVersion,
        getter: () => checkoutVersion,
      });
    },
    [checkoutVersion]
  );

  // Update payment provider in localStorage
  const updatePaymentSystem = useCallback(
    async (newPaymentProvider: PaymentProvider) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_ps',
        newVariable: newPaymentProvider,
        oldVariable: paymentSystem,
        setter: setPaymentSystem,
        getter: () => paymentSystem,
      });
    },
    [paymentSystem]
  );

  const updateLocale = useCallback(
    async (newLocale: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'locale',
        newVariable: newLocale,
        oldVariable: locale,
        setter: setLocale,
        getter: () => locale,
      });
    },
    [locale]
  );

  const updateQuestionId = useCallback(
    async (newQuestionId: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'questionId',
        newVariable: newQuestionId,
        oldVariable: questionId,
        setter: setQuestionId,
        getter: () => questionId,
      });
    },
    [questionId]
  );

  const updateAnswer = useCallback(
    async ({ questionId, answerId }: Answer) => {
      const otherAnswers = answers.filter(item => item.questionId !== questionId);
      const newAnswers = [...otherAnswers, { questionId, answerId }];
      update({
        contextPrefix: 'se',
        variableName: 'answers',
        newVariable: newAnswers,
        oldVariable: answers,
        setter: setAnswers,
        getter: () => answers,
      });
    },
    [answers]
  );

  const updateAllAnswers = useCallback(
    async (newAnswers: Answer[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'answers',
        newVariable: newAnswers,
        oldVariable: answers,
        setter: setAnswers,
        getter: () => answers,
      });
    },
    [answers]
  );

  const updateFormData = useCallback(async (formData: FormData) => {
    update({
      contextPrefix: 'se',
      variableName: 'formData',
      newVariable: formData,
      oldVariable: formData,
      setter: setFormData,
      getter: () => formData,
    });
  }, []);

  const updateCheckoutId = useCallback(
    async (newCheckoutId: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'checkoutId',
        newVariable: newCheckoutId,
        oldVariable: checkoutId,
        setter: setCheckoutId,
        getter: () => checkoutId,
      });
    },
    [checkoutId]
  );

  const updatePaymentStatus = useCallback(
    async (newPaymentStatus: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'paymentStatus',
        newVariable: newPaymentStatus,
        oldVariable: paymentStatus,
        setter: setPaymentStatus,
        getter: () => paymentStatus,
      });
    },
    [paymentStatus]
  );

  const updateStage = useCallback(
    async (newStage: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'stage',
        newVariable: newStage,
        oldVariable: stage,
        setter: setStage,
        getter: () => stage,
      });
    },
    [stage]
  );

  const updateClickedButton = useCallback(
    async (newClickedButton: string | null) => {
      update({
        contextPrefix: 'se',
        variableName: 'clickedButton',
        newVariable: newClickedButton,
        oldVariable: clickedButton,
        setter: setClickedButton,
        getter: () => clickedButton,
      });
    },
    [clickedButton]
  );

  const updateTestAnswers = useCallback(
    async (newTestAnswers: string[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'testAnswers',
        newVariable: newTestAnswers,
        oldVariable: testAnswers,
        setter: setTestAnswers,
        getter: () => testAnswers,
      });
    },
    [testAnswers]
  );

  const resetTestState = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'stage',
      newVariable: 1,
      oldVariable: stage,
      setter: setStage,
      getter: () => stage,
    });
    update({
      contextPrefix: 'se',
      variableName: 'clickedButton',
      newVariable: null,
      oldVariable: clickedButton,
      setter: setClickedButton,
      getter: () => clickedButton,
    });
    update({
      contextPrefix: 'se',
      variableName: 'testAnswers',
      newVariable: [],
      oldVariable: testAnswers,
      setter: setTestAnswers,
      getter: () => testAnswers,
    });
  }, [stage, clickedButton, testAnswers]);

  // Add new methods for emotional intelligence test
  const updateEmotionalStage = useCallback(
    async (newStage: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalStage',
        newVariable: newStage,
        oldVariable: emotionalStage,
        setter: setEmotionalStage,
        getter: () => emotionalStage,
      });
    },
    [emotionalStage]
  );

  const updateEmotionalClickedButton = useCallback(
    async (newClickedButton: string | null) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalClickedButton',
        newVariable: newClickedButton,
        oldVariable: emotionalClickedButton,
        setter: setEmotionalClickedButton,
        getter: () => emotionalClickedButton,
      });
    },
    [emotionalClickedButton]
  );

  const updateEmotionalTestAnswers = useCallback(
    async (newTestAnswers: number[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalTestAnswers',
        newVariable: newTestAnswers,
        oldVariable: emotionalTestAnswers,
        setter: setEmotionalTestAnswers,
        getter: () => emotionalTestAnswers,
      });
    },
    [emotionalTestAnswers]
  );

  const resetEmotionalTestState = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'emotionalStage',
      newVariable: 1,
      oldVariable: emotionalStage,
      setter: setEmotionalStage,
      getter: () => emotionalStage,
    });
    update({
      contextPrefix: 'se',
      variableName: 'emotionalClickedButton',
      newVariable: null,
      oldVariable: emotionalClickedButton,
      setter: setEmotionalClickedButton,
      getter: () => emotionalClickedButton,
    });
    update({
      contextPrefix: 'se',
      variableName: 'emotionalTestAnswers',
      newVariable: [],
      oldVariable: emotionalTestAnswers,
      setter: setEmotionalTestAnswers,
      getter: () => emotionalTestAnswers,
    });
  }, [emotionalStage, emotionalClickedButton, emotionalTestAnswers]);

  const fetchAllSessionData = () => ({
    sessionId,
    locale,
    questionId,
    answers,
    formData,
    checkoutId,
    paymentStatus,
    stage,
    clickedButton,
    testAnswers,
    emotionalStage,
    emotionalClickedButton,
    emotionalTestAnswers,
  });

  const updateAllSessionData = async (data: SessionData) => {
    const {
      sessionId,
      locale,
      questionId,
      answers,
      formData,
      checkoutId,
      paymentStatus,
      stage,
      clickedButton,
      testAnswers,
      emotionalStage,
      emotionalClickedButton,
      emotionalTestAnswers,
    } = data;

    updateSessionId(sessionId);
    updateLocale(locale);
    updateQuestionId(questionId);
    updateAllAnswers(answers);
    updateFormData(formData);
    updateCheckoutId(checkoutId);
    updatePaymentStatus(paymentStatus);
    updateStage(stage || 1);
    updateClickedButton(clickedButton || null);
    updateTestAnswers(testAnswers || []);
    // Update emotional intelligence test fields
    updateEmotionalStage(emotionalStage || 1);
    updateEmotionalClickedButton(emotionalClickedButton || null);
    updateEmotionalTestAnswers(emotionalTestAnswers || []);
  };

  const resetAnswers = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'answers',
      newVariable: [],
      oldVariable: answers,
      setter: setAnswers,
      getter: () => answers,
    });
  }, [answers]);

  const updateResults = useCallback(
    async (newResults: any) => {
      update({
        contextPrefix: 'se',
        variableName: 'results',
        newVariable: newResults,
        oldVariable: results,
        setter: setResults,
        getter: () => results,
      });
    },
    [results]
  );

  const updateStripeInvoices = useCallback(
    async (newResults: { data: any[] }) => {
      update({
        contextPrefix: 'se',
        variableName: 'stripeInvoices',
        newVariable: newResults,
        oldVariable: stripeInvoices,
        setter: setStripeInvoices,
        getter: () => stripeInvoices,
      });
    },
    [stripeInvoices]
  );

  const updateSolidgateInvoices = useCallback(
    async (newResults: { data: any[] }) => {
      update({
        contextPrefix: 'se',
        variableName: 'solidgateInvoices',
        newVariable: newResults,
        oldVariable: solidgateInvoices,
        setter: setSolidgateInvoices,
        getter: () => solidgateInvoices,
      });
    },
    [solidgateInvoices]
  );

  const updateEmotionalScores = useCallback(
    async (newEmotionalScores: any) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalScores',
        newVariable: newEmotionalScores,
        oldVariable: emotionalScores,
        setter: setEmotionalScores,
        getter: () => emotionalScores,
      });
    },
    [emotionalScores]
  );

  const updateMerchantData = useCallback(
    async (newMerchantData: MerchantDataType) => {
      update({
        contextPrefix: 'se',
        variableName: 'merchantData',
        newVariable: newMerchantData,
        oldVariable: merchantData,
        setter: setMerchantData,
        getter: () => merchantData,
      });
    },
    [merchantData]
  );

  return (
    <SessionContext.Provider
      value={{
        sessionId,
        merchantData,
        locale,
        questionId,
        answers,
        planType,
        plan,
        paymentSystem,
        checkoutVersion,
        getIsTrial,
        getIsOneTime,
        formData,
        checkoutId,
        paymentStatus,
        prices,
        offers,
        siteConfig,
        results,
        emotionalScores,
        stage,
        clickedButton,
        testAnswers,
        stripeInvoices,
        solidgateInvoices,
        emotionalStage,
        emotionalClickedButton,
        emotionalTestAnswers,
        updateSessionId,
        updateLocale,
        updateQuestionId,
        updateAnswer,
        updatePlanType,
        updateFixedPrice,
        updateFixedCurrency,
        updatePlan,
        updatePaymentSystem,
        updateCheckoutVersion,
        updateAllAnswers,
        updateFormData,
        updateCheckoutId,
        updatePaymentStatus,
        fetchAllSessionData,
        updateAllSessionData,
        resetAnswers,
        updateResults,
        updateEmotionalScores,
        updateMerchantData,
        updateStage,
        updateClickedButton,
        updateStripeInvoices,
        updateSolidgateInvoices,
        updateTestAnswers,
        resetTestState,
        updateEmotionalStage,
        updateEmotionalClickedButton,
        updateEmotionalTestAnswers,
        resetEmotionalTestState,
      }}>
      {props.children}
    </SessionContext.Provider>
  );
};

export default SessionContext;
