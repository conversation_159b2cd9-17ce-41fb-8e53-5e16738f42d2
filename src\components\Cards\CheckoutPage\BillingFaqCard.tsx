import { memo } from 'react';
import Image from 'next/image';

const BillingFaqCard = ({ faq: { img, title, text } }: { faq: { img: JSX.Element; title: string; text: string } }) => (
  <li className='flex flex-wrap bg-grey-bg p-5 lg:w-[calc(33%-12px)] xl:min-w-[315px]'>
    <div className='flex'>
      <div className='mr-3 mb-2'>
        {/* <Image
          src={`/checkout/billing-faq/${img}`}
          alt={`Icon of billing faq item for ${title}`}
          width={36}
          height={36}
        /> */}
        {img}
      </div>
      <h6 className='leading-loose'>{title}</h6>
    </div>
    <div>
      <p className=''>{text}</p>
    </div>
  </li>
);

export default memo(BillingFaqCard);
