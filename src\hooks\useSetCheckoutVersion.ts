import { useCallback, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import SessionContext from '@/store/SessionContext';
import { parseQueryParam } from '@/utils/parseQueryParam';
import { CheckoutVersion } from '@/store/types';

export function useSetCheckoutVersion() {
  const searchParams = useSearchParams();
  const { updateCheckoutVersion } = useContext(SessionContext);

  // Set checkout version using callback to prevent re-rendering
  const setCheckoutVersion = useCallback(() => {
    const fw2 = searchParams.get('fw2');

    // If fw2 is present, update checkout version in localStorage
    if (fw2) {
      const checkoutVersion = parseQueryParam(fw2, {
        defaultValue: 'v1' as CheckoutVersion,
        falsyValue: 'v1' as CheckoutVersion,
        truthyValue: (normalized: string) => {
          const numValue = parseFloat(normalized);
          const isNumber = !isNaN(numValue);

          if (isNumber && numValue <= 0) {
            return 'v1' as CheckoutVersion;
          }

          return 'v3' as CheckoutVersion;
        },
      });
      updateCheckoutVersion(checkoutVersion);
    }
    // If fw2 is not present, do not touch localStorage
  }, [searchParams, updateCheckoutVersion]);

  return { setCheckoutVersion };
}
