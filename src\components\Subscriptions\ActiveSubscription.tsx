'use client';

import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import CancelSubscription from './CancelSubscription';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';

interface ActiveSubscriptionProps {
  onProcessing: (loading: boolean) => void;
}

export default function ActiveSubscription({ onProcessing }: ActiveSubscriptionProps) {
  const t = useTranslations('members.subscription');
  const { user, isOneTimePayment, canCancelSubscription, isSubscribed } = useSubscriptionPlan();

  if (!isSubscribed) {
    return null;
  }

  return (
    <>
      {isOneTimePayment ? (
        <>
          <h5 className="mb-5">{t('no_active_subscription')}</h5>
          <p className="text-gray-800">
            {t('onetime_access_expiry', { date: dayjs(user?.current_period_end).format('LLL') })}
          </p>
        </>
      ) : (
        <>
          <h5 className="mb-5">{t('active_subscription')}</h5>
          <p className="text-gray-800">
            {t('next_billing_date', { date: dayjs(user?.current_period_end).format('LLL') })}
          </p>
        </>
      )}
      {canCancelSubscription && <CancelSubscription onProcessing={onProcessing} />}
    </>
  );
}
