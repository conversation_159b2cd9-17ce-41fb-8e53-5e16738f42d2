import { useTranslations } from 'next-intl';

export default function CourseCardPart({ header, time, src, available, setSource, week }) {
  const t = useTranslations('video_course');

  const handlePlayClick = () => {
    setSource({
      header: header,
      src: src,
      week: week,
    });
  };

  return (
    <div
      onClick={handlePlayClick}
      className="bg-white shadow-md p-5 flex border-b-2 cursor-pointer">
      {available == true ? (
        <div className="flex items-center justify-center w-[40px] h-[40px] bg-primary bg-opacity-10 rounded-md">
          <svg className="w-[23px] h-[23px] fill-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960"><path d="m380-300 280-180-280-180v360ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
        </div>
      ) : (
        <img width="40" height="40" alt={t('alt.play_unavailable')} src="/images/play-unavailable.png" />
      )}
      <div className="pl-4">
        <h4 className="text-[#191919] text-[16px] font-semibold">{header}</h4>
        <p className="flex text-[14px] text-[#8893AC]">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="size-5 pb-0.5 mr-1">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z"
            />
          </svg>
          {time}
        </p>
      </div>
      {available != true && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="size-6 text-[#8893AC] ml-auto mt-2">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
          />
        </svg>
      )}
    </div>
  );
}
