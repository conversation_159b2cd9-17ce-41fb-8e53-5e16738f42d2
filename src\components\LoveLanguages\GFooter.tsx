"use client";

import React, { useContext } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n/routing';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';

const Footer = () => {
  const t = useTranslations('love-languages.landing.footer');
  const { user } = useContext(UserContext);
  const { siteConfig } = useContext(SessionContext);
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="relative flex flex-wrap md:flex-nowrap justify-between items-center px-[16px] md:p-[34px_82px] text-sm md:text-base z-10">
      <div className="flex flex-wrap gap-x-[32px] gap-y-[20px]">
        <Link
          href="/"
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.home')}
        </Link>
        <Link
          href={user ? `/love-languages/results` : `/love-languages/test`}
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.results')}
        </Link>
        <Link
          href={`${siteConfig.websiteUrl}/insights/terms-and-conditions`}
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.terms_conditions')}
        </Link>
        <Link
          href="/privacy"
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.privacy_policy')}
        </Link>
      </div>
      <div className="font-raleway font-semibold text-[18px] text-[#828E98] leading-[24px] md:leading-[20px] mt-4 md:mt-0 md:ml-auto mb-[23px] md:mb-0">
        {t('copyright')} <span className="font-sans">{currentYear}</span> {siteConfig.siteName}
      </div>
    </footer>
  );
};

export default Footer;
