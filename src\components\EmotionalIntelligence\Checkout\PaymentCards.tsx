import Image from 'next/image';
import React from 'react';
import { useTranslations } from 'next-intl';

const PaymentCards: React.FC = () => {
  const t = useTranslations('emotional_intelligence.checkout.payment_cards');

  return (
    <div className={`flex items-center gap-[8px] md:gap-[12px] bg-[#FDFEFF] mb-[24px] md:mb-[32px]`}>
      <Image
        src="/images/emotional-intelligence/visa1.svg"
        alt={t('visa1')}
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa2.svg"
        alt={t('visa2')}
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa3.svg"
        alt={t('visa3')}
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa4.svg"
        alt={t('visa4')}
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa5.svg"
        alt={t('visa5')}
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
    </div>
  );
};

export default PaymentCards;
