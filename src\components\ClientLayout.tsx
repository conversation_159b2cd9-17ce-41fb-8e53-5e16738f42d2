'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SearchParamsManager from '@/components/SearchParamsManager';
import { usePostHogUserProperties } from '@/hooks/usePostHogUserProperties';
import { usePathname } from '@/lib/i18n/routing';

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isSpecialRoute = pathname.startsWith('/love-languages') || pathname.startsWith('/emotional-intelligence');

  // Monitor PostHog properties
  usePostHogUserProperties();

  return isSpecialRoute ? (
    <>
      <SearchParamsManager />
      {children}
    </>
  ) : (
    <div className="flex flex-col min-h-lvh justify-between">
      <Header />
      <SearchParamsManager />
      <div className="grow flex justify-center">{children}</div>
      <Footer />
    </div>
  );
}
