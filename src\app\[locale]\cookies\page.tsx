'use client';

import {
  CookieP<PERSON>yHeader,
  WhatAreCookies,
  HowWeUseCookies,
  TypesOfCookies,
  ManagePreferences,
  CookiePolicyFooter
} from '@/components/CookiePolicy';

const CookiePolicy = () => {
  // You can customize these dates as needed
  const effectiveDate = '09-Apr-2024';
  const lastUpdatedDate = '09-Apr-2024';

  return (
    <section className='cookie-policy max-w-[1440px] m-auto mt-16 lg:mt-20 xl:mt-28 pt-10'>
      <CookiePolicyHeader 
        effectiveDate={effectiveDate}
        lastUpdatedDate={lastUpdatedDate}
      />
      <WhatAreCookies />
      <HowWeUseCookies />
      <TypesOfCookies />
      <ManagePreferences />
      <CookiePolicyFooter />
    </section>
  );
};

export default CookiePolicy;
