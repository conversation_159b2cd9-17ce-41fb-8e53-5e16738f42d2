'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Prices } from '@/app/prices';

type TrialPricingProps = {
  prices: Prices;
};

const TrialPricing: React.FC<TrialPricingProps> = ({ prices }) => {
  const t = useTranslations('pricing');

  return (
    <div className="flex flex-col items-center gap-7 border border-primary pb-5 rounded-md w-[320px]">
      <span className="p-3 bg-primary text-white font-semibold rounded-sm w-full text-center">{t('trial_header')}</span>
      <div className="flex flex-col items-center">
        <div className="flex items-center gap-2">
          <span className="text-4xl font-bold">{prices.trial.formatted}</span>
          <span>{t('trial_period')}</span>
        </div>
        {prices.vatIncluded && <span>{t('vat_included')}</span>}
      </div>
    </div>
  );
};

export default TrialPricing;
