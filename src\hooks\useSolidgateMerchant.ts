import { sendGTMEvent } from '@next/third-parties/google';
import { InitConfig } from '@solidgate/react-sdk';
import { httpsCallable } from 'firebase/functions';
import { useContext } from 'react';

import { usePaymentIntentAttempts } from '@/hooks/usePaymentIntentAttempts';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';
import { PaymentIntentContext } from '@/types/payment';
import { PlanTypeEnum, SubscriptionPlanEnum } from '@/types/plan-types';
import { COMPLIANCE_CHECKOUT_VERSION, COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';
import { functions } from '@/utils/firebase';
import getLandingUrl from '@/utils/getLandingUrl';
import { isGTMInitialized } from '@/utils/isGtmInitialized';

export interface MerchantData {
  merchant: string;
  signature: string;
  paymentIntent: string;
}

// Helper to build the payment intent payload
const getPaymentIntentPayload = (
  formData: any,
  sessionId: string,
  prices: any,
  checkoutVersion: string,
  clickId: string | null,
  time: number,
  isTrial: boolean = true
) => ({
  email: formData.email,
  name: formData.name,
  sessionId,
  clickId,
  landingUrl: getLandingUrl(),
  pageType: 'IQ',
  country: prices.country,
  completionTime: time,
  checkoutVersion: time < COMPLIANCE_TIME_THRESHOLD ? COMPLIANCE_CHECKOUT_VERSION : checkoutVersion,
  isTrial,
});

/**
 * Use solidgate merchant hook
 *
 * @returns
 */
const useSolidgateMerchant = () => {
  const { formData, sessionId, prices, checkoutVersion, updateMerchantData, merchantData } = useContext(SessionContext);
  const { paymentSystem } = useContext(SessionContext);

  // Use the shared hook for payment intent attempts
  const { attempts, incrementAttempts, maxAttempts } = usePaymentIntentAttempts();

  // Shared function for creating payment intent and handling tracking/merchant data
  const handlePaymentIntent = async (payload: any, clickId?: string | null) => {
    const createPaymentIntentFn = httpsCallable(functions, 'createPaymentIntentSolidgate');
    const trackingPostback = httpsCallable(functions, 'trackingPostback');

    // If max attempts reached, use the existing merchantData (from context)
    if (paymentSystem === PaymentProvider.SOLIDGATE && attempts >= maxAttempts) {
      // If merchantData exists, return it; otherwise, proceed to create a new payment intent
      if (
        merchantData &&
        typeof merchantData === 'object' &&
        'merchant' in merchantData &&
        'signature' in merchantData
      ) {
        // Now TypeScript knows merchantData is the object type
        return merchantData;
      }
      // If no merchantData is available, fall through to try creating a new one (should be rare)
    }

    try {
      // Call the backend to create a new payment intent
      const paymentIntent: any = await createPaymentIntentFn(payload);
      const merchantData = paymentIntent.data.merchantData as InitConfig['merchantData'];

      if (merchantData) {
        // Track the event if clickId is present
        if (clickId) trackingPostback({ clickId, event: 'cho' });
        // Optionally send GTM event if initialized
        if (isGTMInitialized()) sendGTMEvent({ leadsUserData: { email: formData.email } });
        // Update merchant data in context and local storage
        updateMerchantData(merchantData);
        // Increment the attempts counter in state and local storage
        incrementAttempts();
        return merchantData;
      }

      // If no merchantData is returned, return null
      return null;
    } catch (error) {
      // Log and return null on error
      console.error('Error getting Solidgate merchant data:', error);
      return null;
    }
  };

  // For subscription plans
  const getSubscriptionMerchantData = async (context: PaymentIntentContext): Promise<MerchantData | null> => {
    const { clickId, time, isTrial } = context;
    return handlePaymentIntent(
      {
        ...getPaymentIntentPayload(formData, sessionId, prices, checkoutVersion, clickId, time, isTrial),
        planType: PlanTypeEnum.Subscription,
        plan: SubscriptionPlanEnum.Monthly,
      },
      clickId
    );
  };

  return { getSubscriptionMerchantData };
};

export default useSolidgateMerchant;
