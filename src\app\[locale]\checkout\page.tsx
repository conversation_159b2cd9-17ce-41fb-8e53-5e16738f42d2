'use client';

import { useContext, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import CheckoutV1 from '@/components/CheckoutV1/CheckoutV1';
import CheckoutV3 from '@/components/CheckoutV3/CheckoutV3';
import { IS_PRODUCTION } from '@/config/env';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { CheckoutVersion, PostHogEventEnum } from '@/store/types';
import { COMPLIANCE_CHECKOUT_VERSION, COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';

const Checkout = () => {
  const { checkoutVersion } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const { time } = useContext(UiContext);
  const { captureEvent, updateUserProperties } = usePostHogAnalytics();
  const [version, setVersion] = useState<CheckoutVersion>(checkoutVersion);

  // 1) read skipWaitTime from URL (treat `?swt=1` as "skip"), only if not production
  const skipWait = !IS_PRODUCTION && searchParams.get('swt') === 'true';

  // 2) recompute compliance based on skipWait + time
  const isStillInHoldPeriod = useMemo(() => !skipWait && time < COMPLIANCE_TIME_THRESHOLD, [skipWait, time]);

  // 3) Determine the checkout version, taking into account the compliance time threshold and the skip-wait flag
  useEffect(() => {
    setVersion(skipWait ? 'v3' : checkoutVersion);
  }, [checkoutVersion, skipWait]);

  // 4) fire analytics once
  useEffect(() => {
    captureEvent(PostHogEventEnum.CHECKOUT_PAGE_VIEWED, {});
    if (isStillInHoldPeriod) {
      updateUserProperties({ checkoutVersion: COMPLIANCE_CHECKOUT_VERSION });
    }
  }, []);

  // 5) render
  if (isStillInHoldPeriod || version === 'v1') {
    // still in hold OR explicitly v1 → show v1
    return <CheckoutV1 />;
  } else if (version === 'v3') {
    // If time >= 5 min and version is v3, show v3
    return <CheckoutV3 />;
  }

  return null;
};

export default Checkout;
