"use client";

import React, { useContext } from 'react';
import { Link } from '@/lib/i18n/routing';
import { useTranslations } from 'next-intl';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';

const Footer = () => {
  const t = useTranslations('emotional_intelligence.landing.footer');
  const { user } = useContext(UserContext);
  const { siteConfig } = useContext(SessionContext);
  
  return (
    <footer className="relative flex flex-wrap md:flex-nowrap justify-between items-center px-[16px] md:p-[34px_82px] text-sm md:text-base bg-white z-10">
      <div className="flex flex-wrap gap-x-[32px] gap-y-[20px]">
        <Link
          href="/"
          className="font-ppmori font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.home')}
        </Link>
        <Link
          href={user ? `/emotional-intelligence/results` : `/emotional-intelligence/test`}
          className="font-ppmori font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.results')}
        </Link>
        <Link
          href={`${siteConfig.websiteUrl}/insights/terms-and-conditions`}
          className="font-ppmori font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.terms_conditions')}
        </Link>
        <Link
          href="/privacy"
          className="font-ppmori font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          {t('links.privacy_policy')}
        </Link>
      </div>
      <div className="font-ppmori font-semibold text-[18px] text-[#8C8492] leading-[24px] md:leading-[20px] mt-4 md:mt-0 md:ml-auto mb-[32px] md:mb-0">
        {t('copyright')} <span className="font-sans">{new Date().getFullYear()}</span> {siteConfig.siteName}
      </div>
    </footer>
  );
};

export default Footer;
