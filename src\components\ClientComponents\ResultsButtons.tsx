import { useTranslations } from 'next-intl';
import { Loader2 } from 'lucide-react';
import { filesBucket } from '@/config/firebase.config';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { Link } from '@/lib/i18n/routing';
import { PostHogEventEnum } from '@/store/types';

const ResultsButtons = ({ sessionIdToUse, loading }: { sessionIdToUse: string; loading: boolean }) => {
  const t = useTranslations('iq_test_results.buttons');
  const { captureEvent } = usePostHogAnalytics();

  if (loading) {
    return (
      <div className="flex justify-center basis-full">
        <Loader2 className="text-primary h-16 w-16 animate-spin mr-2 md:mt-[3px]" />
      </div>
    );
  }

  return (
    <div className="flex flex-wrap justify-center max-w-96">
      <Link
        target="_blank"
        href={`${filesBucket}/${sessionIdToUse || 'example'}/report.pdf`}
        className={`button primary text-base md:text-xl font-semibold mb-5`}
        style={{
          display: 'inline-flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          borderRadius: 10,
          lineHeight: '120%',
        }}>
        <button onClick={() => captureEvent(PostHogEventEnum.DOWNLOAD_REPORT_CLICKED, { sessionId: sessionIdToUse })}>
          {sessionIdToUse === null || sessionIdToUse === '' ? t('download_example_report') : t('download_your_report')}
        </button>
      </Link>
      <Link
        target="_blank"
        href={`${filesBucket}/${sessionIdToUse || 'example'}/certificate.pdf`}
        className={`button secondary text-base md:text-xl font-semibold`}
        style={{
          display: 'inline-flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          borderRadius: 10,
          lineHeight: '120%',
        }}>
        <button onClick={() => captureEvent(PostHogEventEnum.DOWNLOAD_REPORT_CLICKED, { sessionId: sessionIdToUse })}>
          {sessionIdToUse === null || sessionIdToUse === ''
            ? t('download_example_certificate')
            : t('download_your_certificate')}
        </button>
      </Link>
    </div>
  );
};

export default ResultsButtons;
