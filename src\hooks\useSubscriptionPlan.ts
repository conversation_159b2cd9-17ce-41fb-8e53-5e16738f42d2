import { useContext } from 'react';
import { UserContext } from '@/store/UserContext';
import { PlanTypeEnum, SubscriptionPlanEnum } from '@/types/plan-types';

export const useSubscriptionPlan = () => {
  const { user } = useContext(UserContext);

  /**
   * Helper function to safely get user subscription status
   */
  const isSubscribed = (): boolean => {
    return !!user?.is_subscribed;
  };

  /**
   * Checks if the user has a one-time payment plan (Fix plan with Onetime type)
   */
  const isOneTimePayment = (): boolean => {
    return user?.active_plan === SubscriptionPlanEnum.Fix && user?.active_plan_type === PlanTypeEnum.Onetime;
  };

  /**
   * Checks if the user has a subscription-based plan (not one-time payment)
   */
  const isSubscriptionPayment = (): boolean => {
    return !isOneTimePayment();
  };

  /**
   * Checks if the user has a scheduled cancellation for their subscription
   */
  const hasScheduledCancellation = (): boolean => {
    return !!user?.scheduled_cancel_at;
  };

  /**
   * Checks if the user has an active subscription without scheduled cancellation
   */
  const hasActiveSubscription = (): boolean => {
    return isSubscribed() && !hasScheduledCancellation();
  };

  /**
   * Checks if the user can cancel their subscription
   */
  const canCancelSubscription = (): boolean => {
    return hasActiveSubscription() && isSubscriptionPayment();
  };

  /**
   * Checks if the user can resume their subscription
   */
  const canResumeSubscription = (): boolean => {
    return isSubscribed() && hasScheduledCancellation();
  };

  return {
    user,
    isSubscribed: isSubscribed(),
    isOneTimePayment: isOneTimePayment(),
    isSubscriptionPayment: isSubscriptionPayment(),
    hasActiveSubscription: hasActiveSubscription(),
    canCancelSubscription: canCancelSubscription(),
    canResumeSubscription: canResumeSubscription(),
  };
};
