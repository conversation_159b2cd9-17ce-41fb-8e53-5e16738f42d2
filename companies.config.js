const companies = [
    {
        id: 1,
        companyName: 'Mediavibe FZCO',
        companyAddress: 'Dubai Silicon Oasis, DDP, Building A1, Dubai, United Arab Emirates',
        companyRegistrationNumber: '01-09-320657',
        companyPhone: '+36702273727',
        euVat: 'HU26259572',
    },
    {
        id: 2,
        companyName: 'Robust Digital Kft.',
        companyAddress: 'Csomad, Hungary',
        companyRegistrationNumber: null,
        companyPhone: null,
        euVat: null,
    }
];

const countryCompanyMapping = {
    CY: 2,
    default: 1
}


function getCompanyInfo(country) {
    const companyId = countryCompanyMapping[country] ?? countryCompanyMapping.default;
    return companies.find(company => company.id === companyId);
}

module.exports = getCompanyInfo;