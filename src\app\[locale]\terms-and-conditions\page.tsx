'use client';

import { snakeCase } from 'lodash';
import { Link } from '@/lib/i18n/routing';
import { useContext, useMemo } from 'react';
import SessionContext from '@/store/SessionContext';
import { useTranslations } from 'next-intl';

const TermsAndConditions = () => {
  const { siteConfig } = useContext(SessionContext);
  const t = useTranslations('terms_and_conditions');
  
  const data = useMemo(
    () => [
      {
        title: t('sections.accounts_membership.title'),
        text: [t('sections.accounts_membership.content')],
      },
      {
        title: t('sections.billing_payments.title'),
        text: [t('sections.billing_payments.content')],
      },
      {
        title: t('sections.pricing_payment_terms.title'),
        text: [
          t('sections.pricing_payment_terms.subsections.pricing_structure.title'),
          t.raw('sections.pricing_payment_terms.subsections.pricing_structure.items').map((item: string) =>
            item.replace(/{siteName}/g, siteConfig.siteName)
          ),
          t('sections.pricing_payment_terms.subsections.payment_terms.title'),
          t.raw('sections.pricing_payment_terms.subsections.payment_terms.items').map((item: string) =>
            item.replace(/{siteName}/g, siteConfig.siteName)
          ),
          t('sections.pricing_payment_terms.subsections.price_adjustments.title'),
          t.raw('sections.pricing_payment_terms.subsections.price_adjustments.items').map((item: string) =>
            item.replace(/{siteName}/g, siteConfig.siteName)
          ),
          t('sections.pricing_payment_terms.subsections.cancellation_refund.title'),
          t.raw('sections.pricing_payment_terms.subsections.cancellation_refund.items').map((item: string) =>
            item.replace(/{siteName}/g, siteConfig.siteName)
          ),
          t('sections.pricing_payment_terms.subsections.disputed_charges.title'),
          t.raw('sections.pricing_payment_terms.subsections.disputed_charges.items').map((item: string) =>
            item.replace(/{siteName}/g, siteConfig.siteName)
          ),
          t('sections.pricing_payment_terms.subsections.account_termination.title'),
          t('sections.pricing_payment_terms.subsections.account_termination.content', { siteName: siteConfig.siteName }),
        ],
      },
      {
        title: t('sections.accuracy_information.title'),
        text: [t('sections.accuracy_information.content')],
      },
      {
        title: t('sections.prohibited_uses.title'),
        text: [t('sections.prohibited_uses.content')],
      },
      {
        title: t('sections.limitation_liability.title'),
        text: [t('sections.limitation_liability.content', { siteName: siteConfig.siteName })],
      },
      {
        title: t('sections.changes_amendments.title'),
        text: t.raw('sections.changes_amendments.content'),
      },
      {
        title: t('sections.acceptance_terms.title'),
        text: [t('sections.acceptance_terms.content')],
      },
      {
        title: t('sections.contacting_us.title'),
        text: t.raw('sections.contacting_us.content').map((item: string) =>
          item.replace(/{supportEmail}/g, siteConfig.supportEmail)
        ),
      },
    ],
    [t, siteConfig.siteName, siteConfig.supportEmail]
  );

  return (
    <div>
      <div className="w-full h-[100px] sm:h-[130px]"></div>
      <section id="privacy" className={`flex flex-wrap justify-between max-w-[1440px] m-auto mt-5 pt-0`}>
        <div className="flex flex-wrap flex-col max-w-[416px] w-full lg:w-[40%]">
          <div>
            <h1 className="mb-10 text-4xl" style={{ maxWidth: 353 }}>
              {t('title')}
            </h1>
          </div>
          <ol style={{ color: '#191919', listStyleType: 'decimal', paddingLeft: 20 }}>
            {data.map((item, i) => (
              <a key={i} href={`#${snakeCase(item.title)}`}>
                <li style={{ paddingLeft: 6, marginBottom: 12 }}>{`${item.title}`}</li>
              </a>
            ))}
          </ol>
        </div>
        <div className="max-w-[736px]  w-full lg:w-[60%]">
          <div>
            <h3 className="pb-[40px]">{t('introduction.title')}</h3>
            <p className="pb-[40px]">
              {t('introduction.content', { siteName: siteConfig.siteName })}
            </p>
          </div>
          {data.map((item, index) => (
            <div key={index}>
              <h3
                className="big"
                id={snakeCase(item.title)}
                style={{ marginBottom: 24, marginTop: index > 0 ? 48 : 0, scrollMarginTop: 150 }}>
                {item.title}
              </h3>
              {item.text.map((paragraph: any, i: number) =>
                paragraph.constructor === Array ? (
                  <div key={i}>
                    <ul style={{ listStyleType: 'disc', paddingLeft: 40 }}>
                      {paragraph.map((li: string, i: number) => (
                        <li key={i}>{li}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p key={i} style={{ marginBottom: 16, marginTop: index > 0 ? 16 : 0, whiteSpace: 'pre-line' }}>
                    {paragraph}
                    {item.title.includes('Cookies') && i === 1 && (
                      <span>
                        <Link className="hover:underline text-my-orange mb-4" target="_blank" href="/cookies">
                          {t('sections.cookies.title')}
                        </Link>
                        {'.'}
                      </span>
                    )}
                  </p>
                )
              )}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default TermsAndConditions;