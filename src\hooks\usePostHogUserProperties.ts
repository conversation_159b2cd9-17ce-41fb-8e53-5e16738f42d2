import { useEffect, useContext } from 'react';
import SessionContext from '@/store/SessionContext';
import { usePostHogAnalytics } from './useAnalytics';

/**
 * Hook that monitors changes in checkoutVersion and paymentSystem
 * and automatically updates the PostHog properties
 */
export const usePostHogUserProperties = () => {
  const { updateUserProperties } = usePostHogAnalytics();
  const { paymentSystem, checkoutVersion } = useContext(SessionContext);

  // Monitor changes in paymentSystem and checkoutVersion
  useEffect(() => {
    if (paymentSystem) {
      updateUserProperties({ paymentSystem, checkoutVersion });
    }
  }, [checkoutVersion, paymentSystem, updateUserProperties]);

  return { updateUserProperties };
};
