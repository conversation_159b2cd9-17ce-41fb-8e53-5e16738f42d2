'use client';

import { useState } from 'react';
import { useRouter, usePathname } from '@/lib/i18n/routing';
import { useLocale } from 'next-intl';
import { Locale, getLocalePrefix, isMultiLanguageEnabled } from '@/lib/i18n/locales';

interface LanguageMeta {
  name: string;
  flag: string;
}

interface Language {
  code: Locale;
  name: string;
  flag: string;
}

// Centralised metadata for each potential locale
const languageMeta: Record<Locale, LanguageMeta> = {
  [Locale.En]: { name: 'English', flag: '🇺🇸' },
  [Locale.Es]: { name: 'Español', flag: '🇪🇸' },
  // Uncomment as you re-enable locales
  [Locale.De]: { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  [Locale.Fr]: { name: 'Français', flag: '🇫🇷' },
  [Locale.Pt]: { name: 'Português', flag: '🇵🇹' },
  [Locale.El]: { name: 'Ελληνικά', flag: '🇬🇷' }
};

// Build languages array directly from active Locale enum
const languages: Language[] = Object.values(Locale).map((code) => ({
  code,
  name: languageMeta[code].name,
  flag: languageMeta[code].flag,
}));

function LanguageSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const currentLocale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  // Don't render if multi-language is disabled
  if (!isMultiLanguageEnabled) return null;

  // Get available languages based on environment configuration
  const availableLanguages = isMultiLanguageEnabled ? languages : languages.filter(lang => lang.code === currentLocale);
  const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

  // Set the language cookie
  const setLanguageCookie = (languageCode: Locale) => {
    document.cookie = `preferred-language=${languageCode}; path=/; max-age=31536000`; // 1 year
  };

  // Handle locale navigation based on the locale prefix strategy
  const handleLocaleNavigation = (languageCode: Locale) => {
    const localePrefix = getLocalePrefix();
    
    if (localePrefix === 'always' || localePrefix === 'as-needed') {
      router.push(pathname, { locale: languageCode });
    } else {
      router.refresh();
    }
  };

  // Handle language change
  const handleLanguageChange = (languageCode: Locale) => {
    setLanguageCookie(languageCode);
    handleLocaleNavigation(languageCode);
    setIsOpen(false);
  };

  return (
    <div className='relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between px-[18px] py-[10px] min-w-[128px] text-[14px] leading-[20px] font-segoe font-normal text-[#000000] border rounded-[12px] border-[#D4D4D8] hover:bg-gray-200"
      >
        <span>{currentLanguage.name}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 border border-[#D4D4D8] rounded-lg z-50 bg-white">
          <div className="w-full  min-w-[128px] rounded-lg bg-white">
            {availableLanguages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`w-full rounded-lg text-left px-3 py-[10px] text-[14px] leading-[20px] font-segoe hover:bg-gray-100 flex items-center space-x-2 ${
                  language.code === currentLocale ? 'bg-[#F5F5F5] text-primary' : 'text-[#000000]'
                }`}
              >
                <span>{language.flag}</span>
                <span>{language.name}</span>
                {language.code === currentLocale && (
                  <svg className="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default LanguageSwitcher;