'use client';
import React from 'react';

type BoxWithNumberProps = {
  number: string | number;
  title: string;
  description: string;
};

const BoxWithNumber: React.FC<BoxWithNumberProps> = ({ number, title, description }) => {
  return (
    <div className="flex flex-col gap-4 md:gap-6 p-5 md:p-7 bg-[#FBF6FF] rounded-[6px] md:rounded-[12px]">
      <span className="font-inter font-extrabold text-[44px] leading-[44px] md:text-[64px] md:leading-[54px] tracking-[-0.04em] text-[#8C36D0]">
        {number}
      </span>
      <div className="flex flex-col gap-3 font-ppmori">
        <h3 className="font-semibold text-[18px] leading-[22px] md:text-[24px] md:leading-[32px] text-[#0C0113]">
          {title}
        </h3>
        <span className="font-normal text-[14px] leading-[20px] md:text-[16px] md:leading-[24px] text-[#8C8492]">
          {description}
        </span>
      </div>
    </div>
  );
};

export default BoxWithNumber;
