import React from 'react';
import Image from 'next/image';

interface StarRatingProps {
  rating: number;
  size: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating, size }) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, i) => {
        if (i < rating && rating >= i + 1) {
          return (
            <Image
              key={i}
              width={22}
              height={22}
              className={`w-[${size}px] h-[${size}px] h-auto`}
              src="/images/checkout-v3/Star-Review.svg"
              alt="star rating"
            />
          );
        } else if (rating < i + 1 && rating > i) {
          const value = Math.floor((rating - i) * 24);
          return (
            <div key={i} className="relative">
              <Image src="/images/checkout-v3/Star-Review-Empty.svg" width="24" height="24" alt="Colored Star" />
              <div className={`overflow-hidden absolute top-0 left-0`} style={{ width: value }}>
                <div className="w-6 h-6">
                  <Image src="/images/checkout-v3/Star-Review.svg" alt="Gray Star" width={22} height={22} className="w-full object-contain" />
                </div>
              </div>
            </div>
          );
        } else {
          return (
            <Image
              key={i}
              width={22}
              height={22}
              className={`w-[${size}px] h-[${size}px]`}
              src="/images/checkout-v3/Star-Review-Empty.svg"
              alt="star rating"
            />
          );
        }
      })}
    </div>
  );
};

export default StarRating;
