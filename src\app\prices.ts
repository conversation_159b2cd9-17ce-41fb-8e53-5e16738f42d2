/**
 * Pricing and offers configuration for multiple countries and products.
 * Provides types, constants, and utility functions for price management.
 */
import { headers } from 'next/headers';

/**
 * Supported country codes for pricing and offers.
 */
export type CountryCode = 'us' | 'ca' | 'gb' | 'br' | 'eu';

/**
 * Supported product keys for offers.
 */
export type ProductKey = 'allInOneBundle' | 'careerAndEducation' | 'stressManagement' | 'brainBiohacks';

/**
 * Basic price details for a product or offer.
 */
export interface PriceDetails {
  amount: number;
  formatted: string;
  original?: string;
}

/**
 * Subscription price details, optionally including a weekly price.
 */
export interface SubscriptionDetails extends PriceDetails {
  weekly?: PriceDetails;
}

/**
 * Full price configuration for a country (used in priceList).
 */
export type Prices = {
  country: string;
  currency: string;
  symbol: string;
  subscription: SubscriptionDetails;
  trial: PriceDetails;
  oneTime: PriceDetails;
  vatIncluded: boolean;
};

/**
 * Offer details for a country, including all product prices.
 */
export type Offers = {
  country: CountryCode;
  currency: string;
  symbol: string;
  products: Record<ProductKey, PriceDetails>;
};

/**
 * Offers for all supported countries and products.
 */
export const offers: Record<CountryCode, Offers> = {
  us: {
    country: 'us',
    currency: 'usd',
    symbol: '$',
    products: {
      allInOneBundle: { amount: 24.9, formatted: '$24.90', original: '$99.97' },
      careerAndEducation: { amount: 18.9, formatted: '$18.90', original: '$28.99' },
      stressManagement: { amount: 14.9, formatted: '$14.90', original: '$29.99' },
      brainBiohacks: { amount: 19.9, formatted: '$19.90', original: '$28.99' },
    },
  },
  ca: {
    country: 'ca',
    currency: 'usd',
    symbol: '$',
    products: {
      allInOneBundle: { amount: 24.9, formatted: '$24.90', original: '$99.97' },
      careerAndEducation: { amount: 18.9, formatted: '$18.90', original: '$28.99' },
      stressManagement: { amount: 14.9, formatted: '$14.90', original: '$29.99' },
      brainBiohacks: { amount: 19.9, formatted: '$19.90', original: '$28.99' },
    },
  },
  gb: {
    country: 'gb',
    currency: 'gbp',
    symbol: '£',
    products: {
      allInOneBundle: { amount: 17.9, formatted: '£17.90', original: '£99.97' },
      careerAndEducation: { amount: 12.9, formatted: '£12.90', original: '£28.99' },
      stressManagement: { amount: 9.9, formatted: '£9.90', original: '£29.99' },
      brainBiohacks: { amount: 11.9, formatted: '£11.90', original: '£28.99' },
    },
  },
  br: {
    country: 'br',
    currency: 'brl',
    symbol: 'R$',
    products: {
      allInOneBundle: { amount: 129, formatted: 'R$129', original: 'R$99.97' },
      careerAndEducation: { amount: 89, formatted: 'R$89', original: 'R$28.99' },
      stressManagement: { amount: 69, formatted: 'R$69', original: 'R$29.99' },
      brainBiohacks: { amount: 79, formatted: 'R$79', original: 'R$28.99' },
    },
  },
  eu: {
    country: 'eu',
    currency: 'eur',
    symbol: '€',
    products: {
      allInOneBundle: { amount: 22.9, formatted: '€22.90', original: '€99.97' },
      careerAndEducation: { amount: 16.9, formatted: '€16.90', original: '€28.99' },
      stressManagement: { amount: 14.9, formatted: '€14.90', original: '€29.99' },
      brainBiohacks: { amount: 18.9, formatted: '€18.90', original: '€28.99' },
    },
  },
};

export const priceList: { [country: string]: Prices } = {
  us: {
    country: 'us',
    currency: 'usd',
    symbol: '$',
    subscription: {
      amount: 39.9,
      formatted: '$39.90',
      weekly: {
        amount: 14.9,
        formatted: '$14.90',
      },
    },
    trial: {
      amount: 0.99,
      formatted: '$0.99',
    },
    oneTime: {
      amount: 9.9,
      formatted: '$9.90',
    },
    vatIncluded: false,
  },
  ca: {
    country: 'ca',
    currency: 'usd',
    symbol: '$',
    subscription: {
      amount: 39.9,
      formatted: '$39.90',
      weekly: {
        amount: 14.9,
        formatted: '$14.90',
      },
    },
    trial: {
      amount: 0.99,
      formatted: '$0.99',
    },
    oneTime: {
      amount: 9.9,
      formatted: '$9.90',
    },
    vatIncluded: false,
  },
  gb: {
    country: 'gb',
    currency: 'gbp',
    symbol: '£',
    subscription: {
      amount: 35.9,
      formatted: '£35.90',
      weekly: {
        amount: 9.9,
        formatted: '£9.90',
      },
    },
    trial: {
      amount: 0.79,
      formatted: '£0.79',
    },
    oneTime: {
      amount: 9.9,
      formatted: '$9.90',
    },
    vatIncluded: true,
  },
  br: {
    country: 'br',
    currency: 'brl',
    symbol: 'R$',
    subscription: {
      amount: 199,
      formatted: 'R$199',
      weekly: {
        amount: 79.0,
        formatted: 'R$79.00',
      },
    },
    trial: {
      amount: 3.99,
      formatted: 'R$3.99',
    },
    oneTime: {
      amount: 9.9,
      formatted: '$9.90',
    },
    vatIncluded: false,
  },
  eu: {
    country: 'eu',
    currency: 'eur',
    symbol: '€',
    subscription: {
      amount: 44.9,
      formatted: '€44.90',
      weekly: {
        amount: 14.9,
        formatted: '€14.90',
      },
    },
    trial: {
      amount: 0.7,
      formatted: '€0.70',
    },
    oneTime: {
      amount: 9.9,
      formatted: '$9.90',
    },
    vatIncluded: true,
  },
};

/**
 * Retrieves the pricing configuration for a given country.
 * Defaults to 'eu' if no match is found.
 *
 * @returns {Promise<Prices>} The matched pricing object.
 */
export const getPrices = async (): Promise<Prices> => {
  const country = headers().get('x-vercel-ip-country')?.toLowerCase();
  return priceList[country as keyof typeof priceList] || priceList['eu'];
};

/**
 * Returns all offers for a given country code.
 * Defaults to 'eu' if no match is found.
 *
 * @returns The Offers object for the country, or undefined if not found.
 */
export const getOffers = async (): Promise<Offers | undefined> => {
  const country = headers().get('x-vercel-ip-country')?.toLowerCase() as CountryCode | undefined;
  return offers[country ?? 'eu'];
};
