import { useTranslations } from 'next-intl';

const AutomaticRenewalsAnswer = () => {
  const t = useTranslations('support_page.faq_details.automatic-renewals.answer');

  return (
    <>
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph1') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph2') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph3') }} 
      />
    </>
  );
};

export default AutomaticRenewalsAnswer; 