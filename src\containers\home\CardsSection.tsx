import Image from 'next/image';
import { useTranslations } from 'next-intl';

const CardsSection = () => {
  const t = useTranslations('home_page');

  return (
    <section
      className='zoom-06 flex flex-wrap justify-center h-[450px] mt-[-210px] pb-10 xl:pb-[80px] px-0'
      //style={{ background: 'url("/home/<USER>/cards.png") no-repeat center' }}
    >
      {
        <Image
          alt={t('alt.cards_section')}
          src={'/home/<USER>/cards-min.png'}
          width={1328}
          height={373}
          quality={100}
          sizes='(min-width: 808px) 50vw, 100vw'
          style={{
            objectFit: 'cover',
          }}
          priority
        />
      }
    </section>
  );
};

export default CardsSection;
