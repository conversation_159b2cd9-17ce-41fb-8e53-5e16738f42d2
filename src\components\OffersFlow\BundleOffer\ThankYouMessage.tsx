import { motion, AnimatePresence } from 'framer-motion';
import { useTranslations } from 'next-intl';

interface ThankYouMessageProps {
  showThankYou: boolean;
}

function ThankYouMessage({ showThankYou }: ThankYouMessageProps) {
  const t = useTranslations('offers_flow.one');

  return (
    <AnimatePresence>
      {showThankYou && (
        <motion.div
          initial={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className="mx-auto flex md:gap-4 gap-[5px] md:rounded-[16px] rounded-[10px] md:justify-start justify-center md:max-w-[412px] max-w-[251.6px] bg-[#4BCA7D] mt-5 md:mt-4 md:p-4 px-[14px] py-2">
          <img src="/images/offers/check.svg" className="md:w-[44px] w-[39.6px] h-auto" />
          <div className="flex flex-col md:gap-[2px] gap-[1.6px] font-ppmori">
            <h4 className="text-white md:text-[18px] text-[16.2px] font-semibold md:leading-[22px] leading-[19.8px]">
              {t('thank_you.title')}
            </h4>
            <p className="text-white/85 md:text-[16px] text-[14.4px] md:leading-[20px] leading-[18px]">
              {t('thank_you.message')}
            </p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default ThankYouMessage;
