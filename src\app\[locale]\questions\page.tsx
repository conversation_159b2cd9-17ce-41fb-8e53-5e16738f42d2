'use client';

import dynamic from 'next/dynamic';
import { useState, useContext, useEffect, useMemo } from 'react';
import { v4 as uuid } from 'uuid';
import { httpsCallable } from 'firebase/functions';
import { sendGTMEvent } from '@next/third-parties/google';
import { functions } from '@/utils/firebase';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import { questions } from './config';
import SessionContext from '@/store/SessionContext';
import { UserContext } from '@/store/UserContext';
import QuestionsImage from '@/components/ClientComponents/QuestionsImage';
import QuestionSteps from '@/components/ClientComponents/QuestionSteps';
import QuestionsTitle from '@/components/ClientComponents/QuestionsTitle';
import AnswerCard from '@/components/Cards/QuestionPage/AnswerCard';
import QuizButton from '@/components/Buttons/QuizButton';
import NotDonePopUp from '@/components/PopUps/QuestionsPage/NotDonePopUp';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useTranslations } from 'next-intl';
import { getClickIdFromStorage } from '@/utils/getClickIdFromStorage';

const DynamicQuestionSteps = dynamic(() => import('@/components/ClientComponents/QuestionSteps'), {
  ssr: false,
  loading: () => <QuestionSteps {...{ questionId: 1 }} />,
});

const DynamicQuestionsTitle = dynamic(() => import('@/components/ClientComponents/QuestionsTitle'), {
  ssr: false,
  loading: () => <QuestionsTitle {...{ questionId: 1 }} />,
});

const DynamicQuestionsImage = dynamic(() => import('@/components/ClientComponents/QuestionsImage'), {
  ssr: false,
  loading: () => <QuestionsImage {...{ questionId: 1 }} />,
});

const trackingPostback = httpsCallable(functions, 'trackingPostback');

export default function Question() {
  const t = useTranslations('questions');
  const { captureEvent } = usePostHogAnalytics();

  const [visible, setVisible] = useState<boolean>(false);
  const { questionId } = useContext(SessionContext);
  const { updateSessionId } = useContext(SessionContext);
  const { user } = useContext(UserContext);
  const clickId = useMemo(() => getClickIdFromStorage(), []);

  const fakeAnswerArray = [];
  for (let i = 1; i <= (questions.find(i => i.id === questionId) || { numberOfAnswers: 6 })?.numberOfAnswers; i++) {
    fakeAnswerArray.push(i);
  }

  useEffect(() => {
    if (!user) {
      updateSessionId(uuid());
    }
  }, [user]);

  useEffect(() => {
    if (clickId) trackingPostback({ clickId, event: 'start' });
  }, [clickId]);

  useEffect(() => {
    // Send GTM event for question progress
    if (isGTMInitialized()) {
      const formattedId = `question_${questionId.toString().padStart(2, '0')}`;
      sendGTMEvent({ event: formattedId });
    } else {
      console.warn(`GTM not initialized: question_${questionId} event not sent`);
    }

    // Send postback only when question 10 is reached and clickId is available
    if (questionId === 10 && clickId) {
      trackingPostback({ clickId, event: 'q10' });
    }
  }, [questionId, clickId]);

  return (
    <div style={{ padding: '0px 5.69444%' }} className="flex flex-wrap lg:px-[5.69444%] mt-[50px] 2xl:mt-[75px] mb-10">
      <div className="w-full">
        <DynamicQuestionSteps {...{ questionId }} />
      </div>
      <div className="flex flex-wrap justify-between m-auto mt-1 xs:mt-3 max-w-screen-xl">
        <div className="flex flex-wrap justify-center m-auto xl:w-[1280px]">
          <div className="flex flex-wrap justify-between w-full md:w-[calc(50%-20px)] mdh:w-[30%] xlh:w-[32%] xlgh:w-[31%] 2xl:w-[50%] md:mr-10 xl:mr-0">
            <div className="flex flex-wrap m-auto md:m-0">
              <DynamicQuestionsTitle {...{ questionId }} />
              <DynamicQuestionsImage {...{ questionId }} />
            </div>
          </div>
          <div className="flex flex-wrap justify-between w-full md:w-[calc(50%-20px)]">
            <h5 className="w-full my-2 xs:my-4" style={{ fontSize: 20, lineHeight: ' 130%', letterSpacing: 0 }}>
              {questionId !== 25 ? t('select_answer') : t('select_mirror')}
            </h5>
            <div className="flex flex-wrap ml-0">
              <div className="flex flex-wrap flex-row">
                <div
                  className={`flex flex-wrap flex-row basis-full flex-1 justify-between sm:justify-center ${
                    questions.find(i => i.id === questionId)?.numberOfAnswers === 6
                      ? 'md:justify-between'
                      : 'md:justify-end'
                  }  mdh:justify-end lg:justify-end`}>
                  {fakeAnswerArray.map(fakeId => (
                    <AnswerCard
                      key={fakeId}
                      {...{
                        questionId,
                        answerId: fakeId,
                        numberOfAnswers: questions.find(i => i.id === questionId)?.numberOfAnswers || 6,
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className={`w-full flex flex-wrap flex-row mt-4 xs:mt-8`}
          style={{ justifyContent: questionId === 1 ? 'end' : 'space-between' }}>
          {questionId > 1 && <QuizButton {...{ type: 'back', label: t('buttons.back') }} />}
          {questionId < 30 && <QuizButton {...{ type: 'skip', label: t('buttons.skip') }} />}
          {questionId === 30 && <QuizButton {...{ setVisible, type: 'done', label: t('buttons.get_results') }} />}
        </div>
      </div>
      {visible && (
        <div>
          <NotDonePopUp {...{ visible, setVisible }} />
        </div>
      )}
    </div>
  );
}
