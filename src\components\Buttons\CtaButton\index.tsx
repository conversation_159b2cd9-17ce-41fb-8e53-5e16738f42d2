'use client';

import type { FC } from 'react';
import { useTranslations } from 'next-intl';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { Link } from '@/lib/i18n/routing';
import { PostHogEventEnum } from '@/store/types';

interface CtaButtonProps {
  className?: string;
  type?: 'primary' | 'secondary';
  style?: any;
}

const CtaButton: FC<CtaButtonProps> = ({ className, type, style }) => {
  const t = useTranslations('home_page');
  const { captureEvent } = usePostHogAnalytics();

  const handleClick = () => {
    captureEvent(PostHogEventEnum.START_TEST, {});
  };

  return (
    <Link
      href={`/questions`}
      className={`text-center text-nowrap inline-flex justify-center w-full sm:w-${
        type === 'secondary' ? '40' : '52'
      } button ${type} ${className} text-${type === 'primary' ? 'base' : 'sm'} md:text-xl font-semibold`}
      onClick={handleClick}
      style={{
        borderRadius: 10,
        lineHeight: '120%',
        ...style,
      }}>
      {t('btn_start_iq_test')}
    </Link>
  );
};

export default CtaButton;
