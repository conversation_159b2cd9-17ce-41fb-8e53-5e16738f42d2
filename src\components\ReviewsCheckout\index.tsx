"use client";
import Script from 'next/script';
import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

const ReviewsCarouselCheckout = () => {
  const { siteConfig } = useContext(SessionContext);
  return (
    <>
      {/* Load external JavaScript */}
      <Script
        src="https://widget.reviews.io/carousel-inline-iframeless/dist.js?_t=2024100119"
        strategy="beforeInteractive" // Load the script after the page is interactive
      />

      {/* Reviews Carousel Widget */}
      <div id="reviewsio-carousel-widget"></div>

      {/* Initialize the Carousel Widget */}
      <Script
        id="init-carousel-widget"
        strategy="beforeInteractive" // Ensure the widget initializes after scripts are loaded
      >
        {`
          new carouselInlineWidget('reviewsio-carousel-widget', {
            store: '${siteConfig.domain}',
            sku: '',
            lang: 'en',
            carousel_type: 'topHeader',
            styles_carousel: 'CarouselWidget--topHeader--withcards',
            options: {
              general: {
                review_type: 'company, product',
                min_reviews: '1',
                max_reviews: '20',
                address_format: 'CITY, COUNTRY',
                enable_auto_scroll: 10000,
              },
              header: {
                enable_overall_stars: true,
                rating_decimal_places: 2,
              },
              reviews: {
                enable_customer_name: true,
                enable_customer_location: false,
                enable_verified_badge: true,
                enable_subscriber_badge: true,
                enable_recommends_badge: false,
                enable_photos: true,
                enable_videos: true,
                enable_review_date: true,
                disable_same_customer: true,
                min_review_percent: 4,
                third_party_source: true,
                hide_empty_reviews: true,
                enable_product_name: true,
                tags: "",
                branch: "",
                enable_branch_name: false,
              },
              popups: {
                enable_review_popups: true,
                enable_helpful_buttons: true,
                enable_helpful_count: true,
                enable_share_buttons: true,
              },
            },
            translations: {
              verified_customer: "Verified Customer",
            },
            styles: {
              '--base-font-size': '16px',
              '--base-maxwidth': '100%',
              '--reviewsio-logo-style': 'var(--logo-normal)',
              '--common-star-color': '#FFD700',
              '--common-star-disabled-color': 'rgba(0,0,0,0.25)',
              '--medium-star-size': '22px',
              '--small-star-size': '19px',
              '--x-small-star-size': '16px',
              '--x-small-star-display': 'inline-flex',
              '--header-order': '1',
              '--header-width': '160px',
              '--header-bg-start-color': 'transparent',
              '--header-bg-end-color': 'transparent',
              '--header-gradient-direction': '135deg',
              '--header-padding': '0.5em',
              '--header-border-width': '0px',
              '--header-border-color': 'rgba(0,0,0,0.1)',
              '--header-border-radius': '0px',
              '--header-shadow-size': '0px',
              '--header-shadow-color': 'rgba(0, 0, 0, 0.1)',
              '--header-star-color': 'inherit',
              '--header-disabled-star-color': 'inherit',
              '--header-heading-text-color': 'inherit',
              '--header-heading-font-size': 'inherit',
              '--header-heading-font-weight': 'inherit',
              '--header-heading-line-height': 'inherit',
              '--header-heading-text-transform': 'inherit',
              '--header-subheading-text-color': 'inherit',
              '--header-subheading-font-size': 'inherit',
              '--header-subheading-font-weight': 'inherit',
              '--header-subheading-line-height': 'inherit',
              '--header-subheading-text-transform': 'inherit',
              '--item-maximum-columns': '5',
              '--item-background-start-color': '#ffffff',
              '--item-background-end-color': '#ffffff',
              '--item-gradient-direction': '135deg',
              '--item-padding': '1.5em',
              '--item-border-width': '0px',
              '--item-border-color': 'rgba(0,0,0,0.1)',
              '--item-border-radius': '15px',
              '--item-shadow-size': '8px',
              '--item-shadow-color': '#00000059',
              '--heading-text-color': '#0E1311',
              '--heading-text-font-weight': '600',
              '--heading-text-font-family': 'inherit',
              '--heading-text-line-height': '1.4',
              '--heading-text-letter-spacing': '0',
              '--heading-text-transform': 'none',
              '--body-text-color': '#0E1311',
              '--body-text-font-weight': '400',
              '--body-text-font-family': 'inherit',
              '--body-text-line-height': '1.4',
              '--body-text-letter-spacing': '0',
              '--body-text-transform': 'none',
              '--scroll-button-icon-color': '#0E1311',
              '--scroll-button-icon-size': '24px',
              '--scroll-button-bg-color': 'transparent',
              '--scroll-button-border-width': '0px',
              '--scroll-button-border-color': 'rgba(0,0,0,0.1)',
              '--scroll-button-border-radius': '60px',
              '--scroll-button-shadow-size': '0px',
              '--scroll-button-shadow-color': 'rgba(0,0,0,0.1)',
              '--scroll-button-horizontal-position': '0px',
              '--scroll-button-vertical-position': '0px',
              '--badge-icon-color': '#01B134',
              '--badge-icon-font-size': '15px',
              '--badge-text-color': '#0E1311',
              '--badge-text-font-size': 'inherit',
              '--badge-text-letter-spacing': 'inherit',
              '--badge-text-transform': 'inherit',
              '--author-font-size': 'inherit',
              '--author-font-weight': 'inherit',
              '--author-text-transform': 'inherit',
              '--photo-video-thumbnail-size': '60px',
              '--photo-video-thumbnail-border-radius': '0px',
              '--popup-backdrop-color': 'rgba(0,0,0,0.75)',
              '--popup-color': '#ffffff',
              '--popup-star-color': 'inherit',
              '--popup-disabled-star-color': 'inherit',
              '--popup-heading-text-color': 'inherit',
              '--popup-body-text-color': 'inherit',
              '--popup-badge-icon-color': 'inherit',
              '--popup-badge-icon-font-size': '19px',
              '--popup-badge-text-color': 'inherit',
              '--popup-badge-text-font-size': '14px',
              '--popup-border-width': '0px',
              '--popup-border-color': 'rgba(0,0,0,0.1)',
              '--popup-border-radius': '0px',
              '--popup-shadow-size': '0px',
              '--popup-shadow-color': 'rgba(0,0,0,0.1)',
              '--popup-icon-color': '#0E1311',
              '--tooltip-bg-color': '#0E1311',
              '--tooltip-text-color': '#ffffff',
            },
          });
        `}
      </Script>
    </>
  );
};

export default ReviewsCarouselCheckout;
