import Image from "next/image";
import React, { ReactNode } from "react";

type BoxWithIconPremium = {
  source: string;
  alt: string;
  heading: string;
  children?: ReactNode;
};

const BoxWithIconPremium: React.FC<BoxWithIconPremium> = ({
  source,
  alt,
  heading,
  children,
}) => {
  return (
    <div className="box-content p-5 md:p-6 bg-white border-l-4 border-[#5DC4FF] shadow-[0px_4px_12px_rgba(0,_80,_105,_0.08)] rounded-[12px] z-20">
      <Image
        src={source}
        alt={alt}
        width={48}
        height={48}
        className="w-[48px] h-[48px] mb-[16px]"
      />
      <label
        className="block font-raleway font-bold text-[#0E2432] text-[20px] md:text-[24px] leading-[24px] md:leading-[32px] mb-3 md:mb-2"
        dangerouslySetInnerHTML={{ __html: heading }}
      ></label>
      <label className="block font-raleway font-medium text-[#828E98] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px]">
        {children}
      </label>
    </div>
  );
};

export default BoxWithIconPremium;
