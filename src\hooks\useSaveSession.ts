import { useCallback, useContext } from 'react';
import { addFormDataToSessionDb } from '@/services/session';
import SessionContext from '@/store/SessionContext';
import type { TestTypes } from '@/store/types';

/**
 * Custom React hook to provide a method for saving the current session data to the database.
 * The session data is gathered from the SessionContext and includes the current answers, time, and type.
 *
 * @returns {Object} An object containing the async saveSession function.
 * @returns {Function} saveSession - Call this with the test type and time to save the session.
 *
 * @example
 * const { saveSession } = useSaveSession();
 * await saveSession('iq', 123);
 */
export const useSaveSession = () => {
  // Get the current session data from the context
  const { fetchAllSessionData, answers } = useContext(SessionContext);

  /**
   * Saves the current session data to the database.
   * @param {TestTypes} type - The type of test/session (e.g., 'iq', 'love-languages', etc.)
   * @param {number} time - The time value to save with the session.
   * @returns {Promise<void>}
   */
  const saveSession = useCallback(
    async (type: TestTypes, time: number) => {
      await addFormDataToSessionDb({
        ...fetchAllSessionData(),
        time,
        answers,
        type,
      });
    },
    [fetchAllSessionData, answers]
  );

  return { saveSession };
};
