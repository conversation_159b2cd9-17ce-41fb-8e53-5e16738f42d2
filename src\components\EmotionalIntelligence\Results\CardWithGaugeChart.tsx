"use client";

import { Gauge<PERSON><PERSON> } from "./GaugeChart";
import React from "react";
import Image from "next/image";

type CardWithGaugeChartProps = {
  alt: string;
  id: string;
  bg_source: string;
  box_shadow: string;
  icon_1_source: string;
  icon_2_source: string;
  header_text: string;
  header_text_color: string;
  chart_color_from: string;
  chart_color_to: string;
  chart_drop_shadow: string;
  remaining_color: string;
  bar_bg: string;
  bar_border: string;
  value: number;
  maxValue: number;
  value_color: string;
  maxValue_color: string;
  title: string;
  title_color: string;
  description: string;
  description_color: string;
};

const CardWithGaugeChart: React.FC<CardWithGaugeChartProps> = ({
  alt,
  id,
  bg_source,
  box_shadow,
  icon_1_source,
  icon_2_source,
  header_text,
  header_text_color,
  chart_color_from,
  chart_color_to,
  chart_drop_shadow,
  remaining_color,
  bar_bg,
  bar_border,
  value,
  maxValue,
  value_color,
  maxValue_color,
  title,
  title_color,
  description,
  description_color,
}) => {
  return (
    <div
      className="relative h-full w-full rounded-[6px] md:rounded-[12px] bg-cover bg-center z-10"
      style={{
        backgroundImage: `url(${bg_source})`,
        boxShadow: `${box_shadow}`,
      }}
    >
      <Image
        src={icon_1_source}
        alt={alt}
        width={101}
        height={101}
        className={`absolute w-[57px] md:w-[101px] h-auto z-20 ${
          id === "card_1" || id === "card_3" || id === "card_5"
            ? `top-[72.18px] md:top-[130.93px] left-[24.5px] md:left-[43.37px]`
            : `top-[58.19px] md:top-[102.7px] left-[24.11px] md:left-[40.8px]`
        }`}
      />
      <Image
        src={icon_2_source}
        alt={alt}
        width={101}
        height={101}
        className={`absolute w-[57px] md:w-[101px] h-auto z-20 ${
          id === "card_1" || id === "card_3" || id === "card_5"
            ? `top-[58.34px] md:top-[102.69px] right-[24.8px] md:right-[34.69px]`
            : `top-[72.36px] md:top-[130.7px] right-[24.27px] md:right-[38.26px]`
        }`}
      />
      <div className="relative flex flex-col p-[24px] md:p-[40px] gap-[24px] md:gap-[40px] w-full z-30">
        <span
          className={`font-ppmori text-[18px] md:text-[28px] leading-[22px] md:leading-[36px] font-semibold text-center bg-gradient-to-r ${header_text_color} text-transparent bg-clip-text`}
        >
          {header_text}
        </span>

        {/* Gauge Chart */}
        <div className="relative mx-auto top-[-12px] md:top-[-11px]">
          <GaugeChart
            id={id}
            chart_color_from={chart_color_from}
            chart_color_to={chart_color_to}
            chart_drop_shadow={chart_drop_shadow}
            remaining_color={remaining_color}
            bar_bg={bar_bg}
            bar_border={bar_border}
            value={value}
            maxValue={maxValue}
            value_color={value_color}
            maxValue_color={maxValue_color}
          />
        </div>

        <div className="font-ppmori text-center flex flex-col gap-2">
          <span
            className="text-[16px] md:text-[26px] leading-[20px] md:leading-[36px] font-semibold"
            style={{ color: `${title_color}` }}
          >
            {title}
          </span>
          <span
            className="text-[12px] md:text-[18px] leading-[18px] md:leading-[27px]"
            style={{ color: `${description_color}` }}
          >
            {description}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CardWithGaugeChart;
