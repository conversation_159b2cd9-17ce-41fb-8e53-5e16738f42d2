'use client';

import { useTranslations } from 'next-intl';

export const emotionalSelfAwareness = [
  "I am aware of my emotions as I experience them.",
  "I can easily identify why I am feeling a certain way.",
  "I take time to reflect on my emotions and reactions after an event.",
  "I am aware when I am feeling overwhelmed and know how to manage it.",
  "I regularly practice self-care to maintain my emotional well-being.",
  "I can identify and challenge my negative self-talk.",
  "I understand how my actions affect others emotionally.",
  "I accept responsibility for my emotions rather than blaming others.",
  "I am mindful of how my emotional state affects my work performance.",
  "I feel comfortable expressing both positive and negative emotions.",
  "I am aware of how my emotions influence my behavior.",
  "I often consider how my words and actions will affect others emotionally.",
];

export const selfRegulation = [
  "I stay calm and composed under pressure.",
  "I can effectively manage my negative emotions.",
  "I can recover quickly from setbacks or disappointments.",
  "I maintain control over my emotions in challenging situations.",
  "I handle stress well without it affecting my performance.",
  "I can stay focused even when I am emotionally charged.",
  "I can separate my emotions from the facts in decision-making.",
  "I accept criticism without becoming defensive or upset.",
  "I adapt my emotional responses based on the situation and people involved.",
  "I can find constructive ways to express anger or frustration.",
  "I can cheer others up when they are feeling down.",
  "I manage conflicts constructively and stay composed.",
];

export const socialAwareness = [
  "I am good at recognizing the emotions of others.",
  "I can sense when someone is upset even if they don't say it.",
  "I am empathetic towards others' feelings and perspectives.",
  "I actively listen to others without interrupting.",
  "I can influence other people's emotions in a positive way.",
  "I recognize when someone needs emotional support.",
  "I understand and appreciate diverse perspectives.",
  "I often help others manage their emotions.",
  "I am good at reading social cues in group settings.",
  "I encourage others to express their feelings openly.",
  "I am considerate of others emotional needs.",
  "I strive to maintain harmony in my relationships.",
];

export const relationshipManagement = [
  "I find it easy to communicate my feelings to others.",
  "I am able to manage conflicts constructively.",
  "I often resolve misunderstandings quickly.",
  "I build and maintain strong relationships with different types of people.",
  "I can mediate when others are in conflict.",
  "I can influence others to resolve their conflicts positively.",
  "I express empathy without being overwhelmed by others emotions.",
  "I can express my needs and boundaries clearly in relationships.",
  "I communicate openly and avoid bottling up my feelings.",
  "I am comfortable discussing sensitive topics without getting defensive.",
  "I am proactive in finding solutions when I or others feel stuck emotionally.",
  "I remain patient and understanding when others are emotional.",
];

export const adaptability = [
  "I can adapt my approach based on the emotional tone of a situation.",
  "I handle change with flexibility and emotional resilience.",
  "I can maintain emotional balance during challenging tasks.",
  "I recover quickly from setbacks or disappointments.",
  "I can turn a negative situation into a learning experience.",
  "I handle criticism without taking it personally.",
  "I can let go of grudges and forgive easily.",
  "I stay positive even when faced with setbacks.",
  "I can maintain focus on my goals despite emotional distractions.",
  "I handle change well without becoming overwhelmed.",
  "I maintain control over my emotions during stressful changes.",
  "I am resilient in the face of emotional challenges.",
];

export const useTestResults = function (
  emotional,
  self,
  social,
  relationship,
  adaptable,
  total
) {
  const t = useTranslations('eq_results');
  
  let emotionalReturn = <div className="max-w-[800px] mx-auto"></div>;
  let selfReturn = <div className="max-w-[800px] mx-auto"></div>;
  let socialReturn = <div className="max-w-[800px] mx-auto"></div>;
  let relationshipReturn = <div className="max-w-[800px] mx-auto"></div>;
  let adaptableReturn = <div className="max-w-[800px] mx-auto"></div>;
  let totalReturn = <div className="max-w-[800px] mx-auto"></div>;

  // Emotional Self-Awareness Results
  if (emotional >= 12 && emotional < 24) {
    emotionalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('emotional_self_awareness.very_low.title', { score: emotional })}
        </h2>
        <p>{t('emotional_self_awareness.very_low.description')}</p>
      </div>
    );
  } else if (emotional >= 24 && emotional < 36) {
    emotionalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('emotional_self_awareness.low.title', { score: emotional })}
        </h2>
        <p>{t('emotional_self_awareness.low.description')}</p>
      </div>
    );
  } else if (emotional >= 36 && emotional < 48) {
    emotionalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('emotional_self_awareness.moderate.title', { score: emotional })}
        </h2>
        <p>{t('emotional_self_awareness.moderate.description')}</p>
      </div>
    );
  } else {
    emotionalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('emotional_self_awareness.high.title', { score: emotional })}
        </h2>
        <p>{t('emotional_self_awareness.high.description')}</p>
      </div>
    );
  }

  // Self-Regulation Results
  if (self >= 12 && self < 24) {
    selfReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('self_regulation.very_low.title', { score: self })}
        </h2>
        <p>{t('self_regulation.very_low.description')}</p>
      </div>
    );
  } else if (self >= 24 && self < 36) {
    selfReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('self_regulation.low.title', { score: self })}
        </h2>
        <p>{t('self_regulation.low.description')}</p>
      </div>
    );
  } else if (self >= 36 && self < 48) {
    selfReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('self_regulation.moderate.title', { score: self })}
        </h2>
        <p>{t('self_regulation.moderate.description')}</p>
      </div>
    );
  } else {
    selfReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('self_regulation.high.title', { score: self })}
        </h2>
        <p>{t('self_regulation.high.description')}</p>
      </div>
    );
  }

  // Social Awareness Results
  if (social >= 12 && social < 24) {
    socialReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('social_awareness.very_low.title', { score: social })}
        </h2>
        <p>{t('social_awareness.very_low.description')}</p>
      </div>
    );
  } else if (social >= 24 && social < 36) {
    socialReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('social_awareness.low.title', { score: social })}
        </h2>
        <p>{t('social_awareness.low.description')}</p>
      </div>
    );
  } else if (social >= 36 && social < 48) {
    socialReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('social_awareness.moderate.title', { score: social })}
        </h2>
        <p>{t('social_awareness.moderate.description')}</p>
      </div>
    );
  } else {
    socialReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('social_awareness.high.title', { score: social })}
        </h2>
        <p>{t('social_awareness.high.description')}</p>
      </div>
    );
  }

  // Relationship Management Results
  if (relationship >= 12 && relationship < 24) {
    relationshipReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('relationship_management.very_low.title', { score: relationship })}
        </h2>
        <p>{t('relationship_management.very_low.description')}</p>
      </div>
    );
  } else if (relationship >= 24 && relationship < 36) {
    relationshipReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('relationship_management.low.title', { score: relationship })}
        </h2>
        <p>{t('relationship_management.low.description')}</p>
      </div>
    );
  } else if (relationship >= 36 && relationship < 48) {
    relationshipReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('relationship_management.moderate.title', { score: relationship })}
        </h2>
        <p>{t('relationship_management.moderate.description')}</p>
      </div>
    );
  } else {
    relationshipReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('relationship_management.high.title', { score: relationship })}
        </h2>
        <p>{t('relationship_management.high.description')}</p>
      </div>
    );
  }

  // Adaptability and Resilience Results
  if (adaptable >= 12 && adaptable < 24) {
    adaptableReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('adaptability_resilience.very_low.title', { score: adaptable })}
        </h2>
        <p>{t('adaptability_resilience.very_low.description')}</p>
      </div>
    );
  } else if (adaptable >= 24 && adaptable < 36) {
    adaptableReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('adaptability_resilience.low.title', { score: adaptable })}
        </h2>
        <p>{t('adaptability_resilience.low.description')}</p>
      </div>
    );
  } else if (adaptable >= 36 && adaptable < 48) {
    adaptableReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('adaptability_resilience.moderate.title', { score: adaptable })}
        </h2>
        <p>{t('adaptability_resilience.moderate.description')}</p>
      </div>
    );
  } else {
    adaptableReturn = (
      <div className="max-w-[800px] mx-auto">
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">
          {t('adaptability_resilience.high.title', { score: adaptable })}
        </h2>
        <p>{t('adaptability_resilience.high.description')}</p>
      </div>
    );
  }

  // Total Results - Complex sections with lists
  const renderAwarenessItems = (items) => (
    <ul>
      {items.map((item, index) => (
        <li key={index} className="pt-4">- {item}</li>
      ))}
    </ul>
  );

  const renderGrowthItems = (items) => (
    <ul>
      {items.map((item, index) => (
        <li key={index} className="pt-4">- {item}</li>
      ))}
    </ul>
  );

  if (total >= 60 && total < 80) {
    const veryLowData = t.raw('total_results.very_low');
    totalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h1 className="text-center text-[26px] font-bold py-10">{t('total_results.result_title')}</h1>
        <p>{veryLowData.description}</p>
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryLowData.awareness_title}</h2>
        {renderAwarenessItems(veryLowData.awareness_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryLowData.growth_title}</h2>
        {renderGrowthItems(veryLowData.growth_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryLowData.conclusion_title}</h2>
        <p>{veryLowData.conclusion}</p>
      </div>
    );
  } else if (total >= 80 && total < 120) {
    const lowData = t.raw('total_results.low');
    totalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h1 className="text-center text-[26px] font-bold py-10">{t('total_results.result_title')}</h1>
        <p>{lowData.description}</p>
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{lowData.awareness_title}</h2>
        {renderAwarenessItems(lowData.awareness_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{lowData.growth_title}</h2>
        {renderGrowthItems(lowData.growth_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{lowData.conclusion_title}</h2>
        <p>{lowData.conclusion}</p>
      </div>
    );
  } else if (total >= 120 && total < 180) {
    const moderateData = t.raw('total_results.moderate');
    totalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h1 className="text-center text-[26px] font-bold py-10">{t('total_results.result_title')}</h1>
        <p>{moderateData.description}</p>
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{moderateData.awareness_title}</h2>
        {renderAwarenessItems(moderateData.awareness_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{moderateData.growth_title}</h2>
        {renderGrowthItems(moderateData.growth_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{moderateData.conclusion_title}</h2>
        <p>{moderateData.conclusion}</p>
      </div>
    );
  } else if (total >= 180 && total < 240) {
    const highData = t.raw('total_results.high');
    totalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h1 className="text-center text-[26px] font-bold py-10">{t('total_results.result_title')}</h1>
        <p>{highData.description}</p>
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{highData.awareness_title}</h2>
        {renderAwarenessItems(highData.awareness_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{highData.growth_title}</h2>
        {renderGrowthItems(highData.growth_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{highData.conclusion_title}</h2>
        <p>{highData.conclusion}</p>
      </div>
    );
  } else {
    const veryHighData = t.raw('total_results.very_high');
    totalReturn = (
      <div className="max-w-[800px] mx-auto">
        <h1 className="text-center text-[26px] font-bold py-10">{t('total_results.result_title')}</h1>
        <p>{veryHighData.description}</p>
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryHighData.awareness_title}</h2>
        {renderAwarenessItems(veryHighData.awareness_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryHighData.growth_title}</h2>
        {renderGrowthItems(veryHighData.growth_items)}
        <h2 className="text-[20px] font-bold py-6 pt-10 text-center">{veryHighData.conclusion_title}</h2>
        <p>{veryHighData.conclusion}</p>
      </div>
    );
  }

  return {
    emotionalReturn,
    selfReturn,
    socialReturn,
    relationshipReturn,
    adaptableReturn,
    totalReturn,
  };
};
