import React from 'react';

import AccountAreaHeader from '@/components/AccountArea/Header';
import MobileMenu from '@/components/MobileMenu';

export default function UserAccountLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <div className="flex flex-col w-full mt-5 2xl:mt-10 px-[5%] md:px-[5.69444%]">
        <AccountAreaHeader />
        {children}
      </div>
      <MobileMenu />
    </>
  );
}
