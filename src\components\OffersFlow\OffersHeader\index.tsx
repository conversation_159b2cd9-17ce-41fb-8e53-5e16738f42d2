import { useTranslations } from 'next-intl';
import Brand from '@/components/Brand';
import type { BaseBackHandlerProps } from '@/components/OffersFlow/types';

function OffersHeader({ handleBack }: BaseBackHandlerProps) {
  const t = useTranslations('offers_flow');
  return (
    <div className="relative flex w-full md:h-[122px] h-[62.5px]">
      <div className="hidden md:flex flex gap-1 justify-center items-center md:ml-[82px] z-10">
        <button className="flex flex-row" onClick={handleBack}>
          <img src="/images/offers/back.svg" className="w-[26px] h-auto" />
          <span className="font-ppmori font-semibold text-[18px] leading-[26px] text-[#191919] mt-[2.4px]">
            {t('buttons.back')}
          </span>
        </button>
      </div>
      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[320px] flex justify-center items-center">
        <Brand />
      </div>
    </div>
  );
}

export default OffersHeader;
