import { useTranslations } from 'next-intl';

const LoseAccessToResultsAnswer = () => {
  const t = useTranslations('support_page.faq_details.lose-access-to-results.answer');

  // Get the list items safely
  const listItems = t.raw('list_items') || [];

  return (
    <>
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph1') }} 
      />
      
      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {listItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>

      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph2') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph3') }} 
      />
    </>
  );
};

export default LoseAccessToResultsAnswer; 