import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

export interface OffersPathProps {
  section: number;
}

function OffersPath({ section }: OffersPathProps) {
  const t = useTranslations('offers_flow');
  const step = section;
  const [circleOffset, setCircleOffset] = useState(14); // default for desktop

  useEffect(() => {
    const handleResize = () => {
      setCircleOffset(window.innerWidth < 768 ? 8.4 : 14);
    };

    handleResize(); // run on mount
    window.addEventListener('resize', handleResize); // run on resize
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Renders the appropriate icon stack
  const renderStepCircle = (index: number) => {
    if (step > index) {
      return (
        <div className="relative w-full h-full flex items-center justify-center">
          <img src="/images/offers/progress.svg" alt="progress" className="absolute w-full h-full" />
          <img
            src="/images/offers/checked.svg"
            alt="checked"
            className="md:w-[12.11px] md:h-[8px] w-[7.27px] h-[4.8px] z-10"
          />
        </div>
      );
    } else if (step === index) {
      return (
        <div className="relative w-full h-full flex items-center justify-center">
          <img src="/images/offers/blank.svg" alt="blank" className="absolute w-full h-full" />
          <img
            src="/images/offers/progress.svg"
            alt="progress"
            className="md:w-[16px] md:h-[16px] w-[9.6px] h-[9.6px] z-10"
          />
        </div>
      );
    } else {
      return <img src="/images/offers/blank.svg" alt="blank" className="w-full h-full" />;
    }
  };

  return (
    <div className="md:w-[374px] w-[223.8px] relative mx-auto md:mt-[54px] mt-[28.4px]">
      <div className="relative border-t-2 border-orange-500">
        {/* Step positions: left 0%, 50%, 100% */}
        {[1, 2, 3].map(stepIndex => {
          const leftPos = stepIndex === 1 ? '0%' : stepIndex === 2 ? '50%' : '100%';

          const label =
            stepIndex === 1
              ? t('steps.all-in-one')
              : stepIndex === 2
                ? t('steps.add_packs')
                : t('steps.access_product');

          return (
            <div
              key={stepIndex}
              className="absolute md:top-[-14px] top-[-9px]"
              style={{ left: `calc(${leftPos} - ${circleOffset}px)` }}>
              <div className="relative md:w-[28px] md:h-[28px] w-[16.8px] h-[16.8px]">
                {renderStepCircle(stepIndex)}
              </div>
              <p className="absolute md:top-10 top-6 left-1/2 transform -translate-x-1/2 text-[12px] md:text-[16px] text-[#191919] text-center w-max">
                {label}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default OffersPath;
