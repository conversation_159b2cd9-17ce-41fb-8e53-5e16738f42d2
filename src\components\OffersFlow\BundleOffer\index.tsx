import { PaymentIntent } from '@stripe/stripe-js';
import { useContext, useRef, useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import OffersPath from '@/components/OffersFlow/OffersPath';
import OfferChargeNotice from '@/components/OffersFlow/OfferChargeNotice';
import ThankYouMessage from '@/components/OffersFlow/BundleOffer/ThankYouMessage';
import StripePaymentHandler from '@/components/PaymentProviders/StripePaymentHandler';
import type { BaseNextHandlerProps } from '@/components/OffersFlow/types';
import { useRouter } from '@/lib/i18n/routing';
import { Elements, stripe } from '@/lib/stripe/client';
import SessionContext from '@/store/SessionContext';

function BundleOffer({ handleNext }: BaseNextHandlerProps) {
  const t = useTranslations('offers_flow.one');
  const ct = useTranslations('offers_flow');
  const router = useRouter();
  const { offers } = useContext(SessionContext);
  const timeout = useRef<NodeJS.Timeout | null>(null);
  const [showThankYou, setShowThankYou] = useState(false);

  // New handler for the claim button
  function handleClaimClick() {
    router.push('/results?init=1');
  }

  /**
   * Handles successful Stripe payment completion:
   * - Shows a thank-you message immediately
   * - Clears any existing timer
   * - Hides the thank-you message after 2.5 seconds
   * - Redirects to the offers page after 3 seconds
   *
   * @param _paymentIntent – The Stripe PaymentIntent object, if available.
   */
  const handlePaymentSuccess = (_paymentIntent?: PaymentIntent): void => {
    // Show thank-you message
    setShowThankYou(true);

    // Clear any existing timeout
    if (timeout.current) clearTimeout(timeout.current);

    // Hide the message after 2.5 seconds
    timeout.current = setTimeout(() => {
      setShowThankYou(false);
      timeout.current = null;
    }, 2500);

    // Redirect to offers page
    router.push('/offers?init=1');
  };

  /**
   * Handles payment processing state
   * Shows a processing message to the user and logs the intent status.
   *
   * @param paymentIntent – The Stripe PaymentIntent object, if available.
   */
  const handlePaymentProcessing = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment is being processed...', 'status =', paymentIntent?.status);
    // Could show a processing indicator here if needed
  };

  /**
   * Handles payment failure
   * Shows an error message, allows user to retry, and logs the intent status.
   *
   * @param paymentIntent – The Stripe PaymentIntent object, if available.
   */
  const handlePaymentFailed = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment failed. Please try again.', 'status =', paymentIntent?.status);
    // Could show an error message here if needed
  };

  /**
   * Handles payment errors
   * Shows a generic error message and logs the intent status.
   *
   * @param paymentIntent – The Stripe PaymentIntent object, if available.
   */
  const handlePaymentError = (paymentIntent?: PaymentIntent): void => {
    console.log('An error occurred during payment processing.', 'status =', paymentIntent?.status);
    // Could show an error message here if needed
  };

  /**
   * Cleans up any active timeout to prevent memory leaks
   * Used primarily for component unmount cleanup
   */
  const cleanupTimeout = () => {
    if (timeout.current) {
      clearTimeout(timeout.current);
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return cleanupTimeout;
  }, []);

  return (
    <div className="md:mx-auto mx-[20px]">
      <Elements stripe={stripe}>
        <StripePaymentHandler
          onSuccess={handlePaymentSuccess}
          onProcessing={handlePaymentProcessing}
          onFailed={handlePaymentFailed}
          onError={handlePaymentError}
          autoTrackEvents={true}
        />
      </Elements>

      <ThankYouMessage showThankYou={showThankYou} />

      <OffersPath section={1} />
      <div className="text-center md:pt-[80px] pt-[55.6px] font-ppmori font-semibold md:text-[36px] md:leading-[52px] text-[#191919] text-[32px] leading-[37px]">
        {t('commit_section.title1')} <br className="md:hidden block" /> {t('commit_section.title2')}
      </div>
      <div className="w-fit mx-auto flex flex-row gap-[6px] md:rounded-[8px] rounded-[5px] md:mt-[22px] mt-[15px] md:py-[10px] md:px-[14px] py-[5px] px-[28px] bg-[#FFF4EA] border border-[0.5px] border-[#FF932F]/25">
        <img src="/images/offers/fire.svg" className="md:w-[16px] md:h-[16px] w-[14.4px] h-[14.4px]" />
        <span className="text-[#FF932F] font-ppmori font-semibold md:text-[16px] leading-[20px] text-[13px] leading-[18px]">
          {t('available_now')}
        </span>
      </div>
      <div className="text-center md:mt-[16px] mt-[11px] font-ppmori font-normal md:text-[18px] md:leading-[32px] text-[16px] leading-[27px] text-[#8893AC]">
        {t('commit_section.description')}
      </div>
      <div className="md:max-w-[560px] max-w-[375px] mx-auto flex md:flex-row flex-col mt-[20px] rounded-[12px] border border-[1.5px] border-[#FF932F] p-4">
        <div className="flex flex-col md:gap-[20px] gap-[10px]">
          <div className="flex justify-between">
            <span className="w-fit rounded-[8px] bg-[#4BCA7D] text-white font-ppmori font-semibold text-[16px] leading-[20px] p-[10px]">
              {t('all-in-one.save')}
            </span>
            <div className="md:hidden block flex items-center">
              <img src="/images/offers/selected.svg" className="w-[30px] h-auto float-right" />
            </div>
          </div>
          <div className="flex flex-col gap-[10px] font-ppmori">
            <span className="font-semibold text-[20px] leading-[26px] text-[#191919]">
              {t('all-in-one.title1')} <br className="md:block hidden" /> {t('all-in-one.title2')}
            </span>
            <span className="font-normal text-[16px] leading-[20px] text-[#8893AC]">
              {t('all-in-one.description1')} <br className="md:block hidden" /> {t('all-in-one.description2')}
            </span>
          </div>
          <div className="md:w-[169px] flex flex-row gap-[6px] font-ppmori font-semibold">
            <span className="text-[30px] leading-[34px] text-[#191919]">
              {offers?.products?.allInOneBundle?.formatted}
            </span>
            <span className="text-[18px] leading-[20px] text-[#8893AC] flex items-center line-through">
              {offers?.products?.allInOneBundle?.original}
            </span>
          </div>
        </div>
        <div className="md:ml-[50px] ml-0 md:mt-0 mt-[10px] md:w-[1px] w-auto md:h-auto h-[1px] bg-[#C1CFE973]"></div>
        <div className="w-full md:ml-[37px] ml-0 md:mt-0 mt-[10px] flex flex-col gap-[18px]">
          <div className="md:block hidden">
            <img src="/images/offers/selected.svg" className="w-[30px] h-auto float-right" />
          </div>
          <div className="flex flex-col md:gap-[6px] gap-[12px]">
            <span className="font-ppmori font-semibold md:text-[20px] md:leading-[26px] text-[16px] leading-[20px] text-[#191919]">
              {t('included.title')}
            </span>
            <div className="flex flex-col">
              <div className="flex flex-row md:gap-[5px] gap-[8px] mb-4">
                <img src="/images/offers/career.svg" className="w-[20px] h-auto" />
                <span className="font-ppmori font-normal md:text-[16px] md:leading-[20px] text-[#8893AC]">
                  {t('included.desc1')}
                </span>
              </div>
              <div className="flex flex-row md:gap-[5px] gap-[8px] mb-4">
                <img src="/images/offers/stress.svg" className="w-[20px] h-auto" />
                <span className="font-ppmori font-normal md:text-[16px] md:leading-[20px] text-[#8893AC]">
                  {t('included.desc2')}
                </span>
              </div>
              <div className="flex flex-row md:gap-[5px] gap-[8px]">
                <img src="/images/offers/brain.svg" className="w-[20px] h-auto" />
                <span className="font-ppmori font-normal md:text-[16px] md:leading-[20px] text-[#8893AC]">
                  {t('included.desc3')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="mx-auto md:block hidden md:mt-[32px] md:mb-[160px] font-ppmori font-normal md:text-[15px] md:leading-[27px] text-[#8893AC] text-center">
        <OfferChargeNotice />
      </div>
      <div className="fixed bottom-0 left-0 w-full z-50 bg-white border-t border-[#8F949F]/30 px-[20px] py-[15px] md:px-[82px] md:py-[20px] flex justify-between">
        <button
          onClick={() => {
            handleNext();
          }}
          className="rounded-[10px] bg-[#E8EDF8] py-[15px] px-[20px] text-[#191919] font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {ct('buttons.skip')}
        </button>
        <button
          onClick={handleClaimClick}
          className="rounded-[10px] bg-[#FF932F] py-[15px] px-[20px] text-white font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {ct('buttons.claim')}
        </button>
      </div>
      <div className="md:hidden block mt-[20px] mb-[120px] font-ppmori font-normal text-[#8893AC] text-center px-[20px]">
        <OfferChargeNotice />
      </div>
    </div>
  );
}

export default BundleOffer;
