'use client';

import { useContext, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { ListStyleCheck } from '@/components/Icons/ListStyleCheck';
import { Link } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';
import TrialPricing from '@/components/Pricing/TrialPricing';
import MonthlyPricing from '@/components/Pricing/MonthlyPricing';
// import WeeklyPricing from '@/components/Pricing/WeeklyPricing';

const Pricing = () => {
  const t = useTranslations('pricing');
  const { prices, getIsTrial } = useContext(SessionContext);
  const isTrial = getIsTrial();
  const features = useMemo(() => [t('feature_certificate'), t('feature_report'), t('feature_training')], [t]);

  return (
    <section className={`flex flex-wrap justify-between max-w-[1440px] m-auto`}>
      <div className="flex flex-col">
        <h1 className="text-5xl md:text-6xl">{t('title')}</h1>
        <ul className="mt-10 gap-2 flex flex-col">
          {features.map((text, i) => (
            <li key={i} className="flex items-center">
              <ListStyleCheck className="w-[20px] mr-2" />
              <span>{text}</span>
            </li>
          ))}
        </ul>
        <div className="flex gap-5 md:flex-row flex-col flex-wrap mt-10">
          {isTrial && <TrialPricing prices={prices} />}
          <MonthlyPricing prices={prices} showAfterTrial={isTrial} />
          {/* <WeeklyPricing prices={prices} showAfterTrial={isTrial} /> */}
        </div>
        <Link
          href={'/questions'}
          className="text-center hover:opacity-90 bg-primary text-2xl font-bold w-[320px] text-white p-3 rounded-md mt-10">
          {t('btn_start_iq_test')}
        </Link>
      </div>
    </section>
  );
};

export default Pricing;
