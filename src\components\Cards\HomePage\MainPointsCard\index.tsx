import { memo } from 'react';
import Image from 'next/image';

interface MainPointsCardProps {
  order: number;
  img: string;
  title: string;
  text: string;
  t: (key: string) => string;
}

const MainPointsCard = ({ order, img, title, text, t }: MainPointsCardProps) => (
  <div className='flex-initial gap-5 relative'>
    <Image
      src={img}
      alt={t('alt.main_points_illustration')}
      width={412}
      height={412}
      priority
    />
    <h4 style={{ marginTop: 30 }}>{title}</h4>
    <p style={{ maxWidth: 412, marginTop: 10 }}>{text}</p>
    {order === 0 ? (
      <Image
        style={{ position: 'absolute', top: '-35px', left: '-45px' }}
        src={`/home/<USER>/wave.svg`}
        alt={t('alt.main_points_wave')}
        width={177}
        height={154}
      />
    ) : (
      ''
    )}
  </div>
);

export default memo(MainPointsCard);
