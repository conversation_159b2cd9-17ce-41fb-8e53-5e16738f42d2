'use client';

import React, { useContext } from 'react';
import { formatDateString } from '@/app/utils';
import InvoiceItem from '@/components/Invoices/InvoiceItem';
import SessionContext from '@/store/SessionContext';

const SolidgateInvoice: React.FC = () => {
  const { solidgateInvoices } = useContext(SessionContext);

  if (!solidgateInvoices?.data?.length) {
    return null;
  }

  return (
    <>
      {solidgateInvoices.data.map(invoice => {
        const date = formatDateString(invoice.billing_period_started_at);
        return (
          <InvoiceItem key={invoice.id} amount={invoice.amount} currency="USD" status={invoice.status} date={date} />
        );
      })}
    </>
  );
};

export default SolidgateInvoice;
