import { memo } from 'react';
import { Link as I18nLink } from '@/lib/i18n/routing';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const SubMenu = () => {
  const t = useTranslations('footer.submenu');

  return (
    <div className="flex flex-wrap gap-2 justify-center">
      <Link
        className="font-normal md:font-semibold mr-4 whitespace-pre"
        style={{ fontSize: 16, lineHeight: '111.111%', color: '#8893AC' }}
        href={`https://blog.iqinternational.org/privacy-policy`}>
        {t('privacy_policy')}
      </Link>
      <Link
        className="font-normal md:font-semibold mr-4 whitespace-pre"
        style={{ fontSize: 16, lineHeight: '111.111%', color: '#8893AC' }}
        href={`https://blog.iqinternational.org/cookie-policy`}>
        {t('cookie_policy')}
      </Link>
      <Link
        className="font-normal md:font-semibold mr-4 whitespace-pre"
        style={{ fontSize: 16, lineHeight: '111.111%', color: '#8893AC' }}
        href={`https://blog.iqinternational.org/legal-notice`}
        prefetch={false}>
        {t('legal_notice')}
      </Link>
      <Link
        className="font-normal md:font-semibold whitespace-pre mr-4"
        style={{ fontSize: 16, lineHeight: '111.111%', color: '#8893AC' }}
        href={`https://blog.iqinternational.org/terms-and-conditions`}
        prefetch={false}>
        {t('terms_conditions')}
      </Link>
      <I18nLink
        className="font-normal md:font-semibold whitespace-pre"
        style={{ fontSize: 16, lineHeight: '111.111%', color: '#8893AC' }}
        href={`/support`}
        prefetch={false}>
        {t('support')}
      </I18nLink>
    </div>
  );
};

export default memo(SubMenu);
