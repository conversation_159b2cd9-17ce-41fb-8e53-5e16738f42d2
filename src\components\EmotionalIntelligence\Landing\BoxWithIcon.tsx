'use client';

import Image from 'next/image';
import React, { ReactNode, useState } from 'react';
import { useTranslations } from 'next-intl';

type BoxWithIcon = {
  source: string;
  alt: string;
  heading: string;
  children?: ReactNode;
  open: boolean;
};

const BoxWithIcon: React.FC<BoxWithIcon> = ({ source, alt, heading, children, open }) => {
  const [isOpen, setIsOpen] = useState<boolean>(open);
  const t = useTranslations('emotional_intelligence.landing');

  const toggleContent = () => setIsOpen(!isOpen);

  return (
    <div className="flex flex-col md:gap-[12px] box-content p-5 md:p-6 bg-white border-l-4 border-[#8C36D0] shadow-[0px_2px_14px_0px_#54006914] rounded-[12px] z-20">
      <div className="flex md:flex-col gap-[14px] md:gap-[20px]">
        <Image src={source} alt={alt} width={56} height={56} className="w-[48px] h-[48px] md:w-[56px] md:h-[56px]" />
        <label className="block font-ppmori font-bold text-[#0C0113] text-[22px] md:text-[26px] leading-[26px] my-auto md:leading-[30px]">
          {heading}
        </label>
        <Image
          src={
            isOpen
              ? `/images/emotional-intelligence/landing/hide.svg`
              : `/images/emotional-intelligence/landing/show.svg`
          }
          alt={t('alt_text.show_hide_button')}
          width={20}
          height={20}
          className="md:hidden w-[20px] h-[20px] my-auto justify-between ml-auto"
          onClick={toggleContent}
        />
      </div>
      <div className="md:hidden">
        {isOpen && (
          <label className="block font-ppmori font-medium text-[#8C8492] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px] mt-[16px]">
            {children}
          </label>
        )}
      </div>
      {/* For md and larger screens, always show the content */}
      <label className="hidden md:block font-ppmori font-medium text-[#8C8492] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px]">
        {children}
      </label>
    </div>
  );
};

export default BoxWithIcon;
