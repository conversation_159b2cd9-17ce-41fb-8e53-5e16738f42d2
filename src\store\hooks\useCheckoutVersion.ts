import { useState } from 'react';
import lscache from 'lscache';
import { CheckoutVersion } from '@/store/types';

/**
 * React hook to manage the current checkout version.
 *
 * Initializes the checkoutVersion state from local storage (key: 'se-cp_cv') if available,
 * otherwise defaults to 'v1'.
 *
 * @example
 * const { checkoutVersion, setCheckoutVersion } = useCheckoutVersion();
 * setCheckoutVersion('v3');
 */
export function useCheckoutVersion() {
  const [checkoutVersion, setCheckoutVersion] = useState<CheckoutVersion>(lscache.get('se-cp_cv') ?? 'v1');
  return { checkoutVersion, setCheckoutVersion };
}
