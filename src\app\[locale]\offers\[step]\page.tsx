'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import OffersHeader from '@/components/OffersFlow/OffersHeader';
import BundleOffer from '@/components/OffersFlow/BundleOffer';
import AreYouSure from '@/components/OffersFlow/AreYouSure';
import ToolkitsOffer from '@/components/OffersFlow/ToolkitsOffer';
import FinalAreYouSure from '@/components/OffersFlow/FinalAreYouSure';

const components = [BundleOffer, AreYouSure, ToolkitsOffer, FinalAreYouSure];

export default function OffersStep() {
  const router = useRouter();
  const { step } = useParams();

  // Handle redirect: /offers/1 → /offers
  useEffect(() => {
    if (step === '1') {
      router.replace('/offers');
    }
  }, [step, router]);

  if (step === '1') return null;

  const stepIndex = step ? parseInt(step as string, 10) - 1 : 0;
  const StepComponent = components[stepIndex] || components[0];

  const handleNext = () => {
    if (stepIndex < components.length - 1) {
      const nextStep = stepIndex + 2;
      router.push(nextStep === 1 ? '/offers' : `/offers/${nextStep}`);
    }
  };

  const handleBack = () => {
    if (stepIndex <= 0) {
      window.history.back();
    } else {
      const prevStep = stepIndex;
      router.push(prevStep === 1 ? '/offers' : `/offers/${prevStep}`);
    }
  };

  const handleReset = () => {
    router.push('/offers');
  };

  return (
    <div className="w-full max-w-[1440px]">
      <OffersHeader handleBack={handleBack} />
      <StepComponent handleNext={handleNext} handleReset={handleReset} />
    </div>
  );
}
