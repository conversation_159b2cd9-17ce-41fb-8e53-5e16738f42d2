import { useState } from 'react';
import lscache from 'lscache';
import { SubscriptionPlan, SubscriptionPlanEnum } from '@/types/plan-types';

/**
 * React hook to manage the current subscription plan.
 *
 * Initializes the plan state from local storage (key: 'se-cp_sp') if available,
 * otherwise defaults to SubscriptionPlanEnum.Monthly.
 *
 * @example
 * const { plan, setPlan } = usePlan();
 * setPlan(SubscriptionPlanEnum.Monthly);
 */
export function useSubscriptionPlan() {
  const [plan, setPlan] = useState<SubscriptionPlan>(lscache.get('se-cp_sp') || SubscriptionPlanEnum.Monthly);
  return { plan, setPlan };
}
