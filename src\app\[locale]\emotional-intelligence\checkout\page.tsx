'use client';

import React, { useContext, useEffect } from 'react';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import { sendGTMEvent } from '@next/third-parties/google';
import IconWithIncluded from '@/components/EmotionalIntelligence/Checkout/IconWithIncluded';
import IconWithSupport from '@/components/EmotionalIntelligence/Checkout/IconWithSupport';
import PaymentCards from '@/components/EmotionalIntelligence/Checkout/PaymentCards';
import ReviewSlider from '@/components/EmotionalIntelligence/Checkout/ReviewSlider';
import BoxWithIcon from '@/components/EmotionalIntelligence/Landing/BoxWithIcon';
import Logo from '@/components/EmotionalIntelligence/Landing/Logo';
import SessionContext from '@/store/SessionContext';
import { useRouter } from '@/lib/i18n/routing';
import { isGTMInitialized } from '@/utils/isGtmInitialized';

const Checkout: React.FC = () => {
  const router = useRouter(); // Initialize useRouter
  const { emotionalScores } = useContext(SessionContext);
  const { prices } = useContext(SessionContext);

  useEffect(() => {
    if (isEmpty(emotionalScores)) {
      router.push('/emotional-intelligence/test');
    }

    const query = new URLSearchParams(window?.location?.search);
    const status = query.get('status');

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on Emotional Intelligence Checkout page: Event not sent');
      return;
    }

    status === 'canceled'
      ? sendGTMEvent({ event: 'payment_canceled' })
      : sendGTMEvent({
          event: 'begin_checkout',
          ecommerce: {
            currency: prices.currency.toUpperCase(),
            items: [
              {
                item_name: 'IQ test',
                item_id: 'iqtest',
                price: prices.trial.amount,
                quantity: 1,
              },
            ],
          },
        });
  }, [emotionalScores, router, prices]);

  return (
    <div className="bg-white">
      <header className="w-full flex justify-center items-center py-[13px] md:py-[43px] mb-[27px] md:mb-[18px]">
        <Logo />
      </header>

      <main>
        <div className="flex justify-center my-[28px] mb-[32px] md:mb-[40px]">
          <span className="font-ppmori font-semibold text-[28px] md:text-[48px] leading-[32px] md:leading-[58px] text-center tracking-[-0.03em] text-[#0C0113]">
            Access Your <br className="md:hidden" /> Test Results Now
          </span>
        </div>
        <div className="max-w-[1440px] mx-auto px-[16px] md:px-[81px] flex-col flex md:flex-row md:gap-[40px] mb-[40px] md:mb-[60px]">
          <div className="w-full flex flex-col items-center">
            <div className="flex w-full justify-between items-center p-[16px] md:p-[24px] gap-[10px] bg-[#F6F6F6] rounded-[12px] mb-[24px]">
              <div className="flex items-center max-w-[325px] gap-[16px] md:gap-[24px]">
                <Image
                  src="/images/emotional-intelligence/checkout/check.svg"
                  alt="check"
                  width={32}
                  height={32}
                  className="w-[24px] h-[24px] md:w-[32px] md:h-[32px]"
                />
                <div className="flex flex-col items-start gap-[12px]">
                  <span className="font-ppmori font-semibold text-[18px] md:text-[24px] leading-[24px] md:leading-[32px] text-[#0E2432]">
                    Emotional Intelligence Test
                  </span>
                  {/* <span
                    className="flex items-center gap-[10px] py-[6px] md:py-[8px] px-[10px] md:px-[12px] bg-white rounded-[8px] text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] font-ppmori font-normal text-[#0E2432]"
                    style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                    7-Day Full Access
                  </span> */}
                </div>
              </div>
              <span
                className="font-ppmori font-semibold text-[18px] md:text-[24px] leading-[24px] md:leading-[32px] text-[#0E2432]"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                {prices.trial.formatted}
              </span>
            </div>
            <PaymentCards />
            <button
              className="flex justify-center w-full items-center py-[18.5px] md:py-[24px] bg-[#8C36D0] rounded-[10px] font-ppmori font-semibold text-[16px] md:text-[20px] leading-[24px] text-white mb-[16px]"
              style={{ letterSpacing: '-0.03em' }}
              onClick={() => router.push('/emotional-intelligence/payment')} // Redirect to checkout route
            >
              Get My Results
            </button>
          </div>
          <div className="w-full">
            <div className="flex flex-col items-start p-[16px] md:p-[24px] gap-[12px] md:gap-[20px] bg-white shadow-[0px_2px_12px_0px_#5400690F] rounded-[12px] mb-[16px] md:mb-[18px]">
              <span className="font-ppmori font-semibold text-[20px] md:text-[26px] leading-[30px] md:leading-[34px] text-[#0C0113]">
                What&apos;s Included?
              </span>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
                <IconWithIncluded source="/images/emotional-intelligence/checkout/included-1.svg" alt="included-1">
                  Your <span className="font-semibold">Emotional Intelligence Results</span>
                </IconWithIncluded>
                <IconWithIncluded source="/images/emotional-intelligence/checkout/included-3.svg" alt="included-3">
                  Your personalised <span className="font-semibold">EQ Assessment</span>
                </IconWithIncluded>
                <IconWithIncluded source="/images/emotional-intelligence/checkout/included-2.svg" alt="included-2">
                  Personal <span className="font-semibold">Growth Challenge</span>
                </IconWithIncluded>
                <IconWithIncluded source="/images/emotional-intelligence/checkout/included-4.svg" alt="included-4">
                  Unlimited Access to <span className="font-semibold">More Tests</span>
                  {/* <span className="md:hidden font-semibold">Growth Challenge</span> */}
                </IconWithIncluded>
              </div>
            </div>
            <div className="flex flex-col">
              <div className="w-full bg-[#F6F6F6] rounded-[12px] p-[12px] md:p-[16px] md:pr-[70px] md:mb-[18px] order-2 md:order-none">
                <div className="flex items-center gap-[12px] md:gap-[14px] ">
                  <Image
                    src="/images/emotional-intelligence/checkout/info.svg"
                    alt="info"
                    width={48}
                    height={48}
                    className="w-[40px] h-[40px] md:w-[48px] md:h-[48px] bg-white rounded-[8px]"
                  />
                  <span
                    className="font-ppmori font-normal text-[12px] md:text-[16px] leading-[18px] md:leading-[20px] text-[#aaa]"
                    style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                    After 2 days, auto renews at {prices.subscription.formatted} billed every 4 weeks and includes
                    unlimited access to all the materials. Cancel anytime.
                  </span>
                </div>
              </div>
              <div className="flex gap-[16px] flex-col md:flex-row mb-[18px] order-1 md:order-none">
                <IconWithSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  14 days refund
                </IconWithSupport>
                <IconWithSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  Premium Support
                </IconWithSupport>
                <IconWithSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  Satisfaction guarantee
                </IconWithSupport>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-center py-[50.23px] md:py-[40px] bg-[#FBF6FF] mb-[40px] md:mb-[0px]">
          <div className="flex flex-col md:flex-row items-center py-[10px] gap-[32.27px] md:gap-[89px]">
            <Image
              src="/images/emotional-intelligence/checkout/logo-1.svg"
              alt="logo-1"
              width={111.92}
              height={30}
              className="w-[89.53px] h-[24px] md:w-[111.92px] md:h-[30px]"
            />
            <Image
              src="/images/emotional-intelligence/checkout/logo-2.svg"
              alt="logo-2"
              width={156.92}
              height={30}
              className="w-[125.53px] h-[24px] md:w-[156.92px] md:h-[30px]"
            />
            <Image
              src="/images/emotional-intelligence/checkout/logo-3.svg"
              alt="logo-3"
              width={305.9}
              height={23}
              className="w-[270.38px] h-[20.33px] md:w-[305.9px] md:h-[23px]"
            />
            <Image
              src="/images/emotional-intelligence/checkout/logo-4.svg"
              alt="logo-4"
              width={213.87}
              height={30}
              className="w-[199.6px] h-[28px] md:w-[213.87px] md:h-[30px]"
              priority
            />
            <Image
              src="/images/emotional-intelligence/checkout/logo-5.svg"
              alt="logo-5"
              width={143.23}
              height={28}
              className="w-[105.73px] h-[20.67px] md:w-[143.23px] md:h-[28px]"
            />
          </div>
        </div>
        <div className="max-w-[1440px] mx-auto relative md:pl-[109px] md:pr-[139px] py-[20px] md:pt-[29px] md:pb-[35px] flex items-center justify-center mb-[40px] md:mb-0">
          <Image
            src="/images/emotional-intelligence/checkout/white-blur-desktop.png"
            alt="white blur desktop"
            width={1192}
            height={524}
            className="absolute md:w-auto md:h-full hidden md:block"
            priority
          />
          <Image
            src="/images/emotional-intelligence/checkout/white-blur-mobile.png"
            alt="white blur mobile"
            width={375}
            height={431}
            className="absolute mx-auto w-auto h-[465px] block md:hidden"
            priority
          />
          <div className="max-w-[327px] md:max-w-[424px] flex flex-col md:my-[80px] z-10 justify-between md:justify-normal bg-white shadow-[0px_0px_24px_0px_#5400691A] md:shadow-[0px_0px_100px_0px_#0000001A] items-center p-5 md:p-8 rounded-[12px] gap-8">
            <Image
              src="/images/emotional-intelligence/checkout/lock-solid.svg"
              alt="lock solid"
              width={109}
              height={124}
              className="w-[73px] h-auto md:w-[109px]"
              priority
            />
            <div className="flex flex-col md:justify-center items-center gap-2">
              <span className="font-ppmori font-semibold leading-[28px] md:leading-[34px] text-[20px] md:text-[26px] text-[#353535] text-center">
                Emotional Intelligence Result
              </span>
              <span
                className="font-ppmori font-medium leading-[20px] md:leading-[24px] text-[14px] md:text-[16px] text-[#8C8492] text-center"
                style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                Unlock the full analysis of your Emotional Intelligence, complete with actionable steps to enhance your
                emotional well-being and relationship-building skills.
              </span>
            </div>
            <button
              className="flex justify-center w-full items-center py-[18.5px] md:py-[24px] bg-[#8C36D0] rounded-[10px] font-ppmori font-bold text-[16px] md:text-[20px] md:leading-[24px] tracking-tight text-white"
              onClick={() => router.push('/emotional-intelligence/payment')} // Redirect to checkout route
            >
              Unlock My Results
            </button>
          </div>
        </div>
        <div className="w-full bg-[#FBF6FF]">
          <div className="max-w-[1440px] mx-auto premium-section flex flex-col items-center relative m-0 p-0 px-[16px] py-[50px] md:py-[80px] md:px-[76px] mb-[40px] md:mb-[0px]">
            {/* <Image
              src="/images/emotional-intelligence/checkout/premium-effect-desktop-1.png"
              alt="Desktop Gradient"
              width={657}
              height={407}
              className="absolute right-[175px] top-[0px] md:w-[178px] md:h-auto hidden md:block z-0"
              priority
            />
            <Image
              src="/images/emotional-intelligence/checkout/premium-effect-desktop-2.png"
              alt="Desktop Gradient"
              width={657}
              height={476}
              className="absolute left-[175px] bottom-[0px] md:w-[178px] md:h-auto hidden md:block z-0"
              priority
            /> */}
            <label className="font-ppmori font-semibold flex justify-center text-center text-[24px] leading-[32px] md:text-[40px] md:leading-[48px] md:mb-[12px] mb-[26px] text-[#292929] z-20">
              Your Premium
              <br /> Emotional Intelligence Report
            </label>
            <label className="font-ppmori font-medium flex justify-center text-center text-[16px] leading-[22px] md:text-[18px] md:leading-[27px] md:mb-[40px] mb-[26px] text-[#8C8492] z-20">
              In-depth analysis on your Emotional Intelligence <br className="hidden md:block" /> and what it means for
              you
            </label>
            <div className="relative grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5 z-20 mb-8 md:mb-10">
              {/* <Image
                src="/images/emotional-intelligence/checkout/premium-effect-mobile-1.png"
                alt="Premium Mobile 1"
                width={375}
                height={557}
                className="w-[98px] h-auto block md:hidden absolute right-[-16px] z-10"
                style={{ top: "-106px" }}
              /> */}
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-1.svg"
                alt="Premium Heart"
                heading="Emotional Self-Awareness"
                open={true}>
                Discover how well you understand your own emotions and how the impact they have on your decisions and
                behavior.
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-2.svg"
                alt="Premium Hearts"
                heading="Self Regulation"
                open={false}>
                Manage emotions effectively to stay composed and respond thoughtfully in challenging situations
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-3.svg"
                alt="Premium Partner"
                heading="Empathy and Social Awareness"
                open={false}>
                Learn how to perceive and interpret others’ emotions to respond empathetically and build stronger
                connections
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-4.svg"
                alt="Premium Love Style"
                heading="Relationship Management"
                open={false}>
                Build and nurture strong connections by fostering trust, clear communication, and mutual respect
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-5.svg"
                alt="Premium Heart Box"
                heading="Adaptability and Resilience"
                open={false}>
                Cultivate flexibility and mental strength to navigate change and recover quickly from challenges,
                maintaining balance and focus
              </BoxWithIcon>
              {/* <Image
                src="/images/emotional-intelligence/checkout/premium-effect-mobile-2.svg"
                alt="Premium Mobile 2"
                width={375}
                height={733}
                className="w-[98px] h-auto block md:hidden absolute left-[-16px] z-10"
                style={{ top: "calc(83% - 87px)" }}
              /> */}
              <BoxWithIcon
                source="/images/emotional-intelligence/checkout/premium-6.svg"
                alt="Premium Smile"
                heading="Areas for Continued Growth"
                open={false}>
                Identify key areas for continued growth and improvement to enhance your emotional intelligence and
                strengthen relationships
              </BoxWithIcon>
            </div>
            <button
              className="flex justify-center w-full md:w-auto items-center py-[18.5px] md:px-[77px] md:py-[24px] bg-[#8C36D0] rounded-[10px] font-ppmori font-bold text-[16px] md:text-[20px] leading-[24px] text-white mb-[16px] md:mb-0 tracking-tight"
              onClick={() => router.push('/emotional-intelligence/payment')} // Redirect to checkout route
            >
              Unlock My Results
            </button>
          </div>
        </div>
        <div className="max-w-[1440px] mx-auto bg-white px-4 md:px-[82px] md:py-[80px] mb-[40px] md:mb-0 flex items-center">
          <ReviewSlider />
        </div>
      </main>
      <div className="md:hidden flex flex-col items-center gap-[6px] w-full p-4 border border-[#E6E3E8] rounded-tl-[24px] rounded-tr-[24px] rounded-br-[0px] rounded-bl-[0px] bg-white">
        <button
          onClick={() => router.push('/emotional-intelligence/payment')} // Redirect to checkout route
          className="w-full font-ppmori font-semibold text-[16px] py-[20px] px-[40px] rounded-[10px] bg-[#8C36D0] text-white leading-[24px]">
          Get My Results
        </button>
        <PaymentCards />
      </div>
    </div>
  );
};

export default Checkout;
