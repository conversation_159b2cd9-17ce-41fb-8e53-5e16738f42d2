import { useTranslations } from 'next-intl';
import OffersPath from '@/components/OffersFlow/OffersPath';
import OffersCard from '@/components/OffersFlow/OffersCard';
import OfferChargeNotice from '@/components/OffersFlow/OfferChargeNotice';
import type { BaseResetHandlerProps } from '@/components/OffersFlow/types';

function FinalAreYouSure({ handleNext, handleReset }: BaseResetHandlerProps) {
  const t = useTranslations('offers_flow.two');
  const ct = useTranslations('offers_flow');

  return (
    <div className="md:mx-auto mx-[20px]">
      <OffersPath section={3} />
      <div className="font-ppmori text-center pt-[55.6px] md:pt-[80px] md:mb-[32px] mb-[20px]">
        <h1 className="md:text-[36px] md:leading-[56px] text-[32px] leading-[37px] text-[#191919] md:mb-[20px] mb-[15px]">
          {t('header.title')}
        </h1>
        <p className="mx-auto md:text-[18px] md:leading-[26px] font-normal text-[16px] leading-[27px] text-[#8893AC] md:mb-[40px] mb-[15px]">
          {t('header.description')}
        </p>
        <h3 className="font-semibold md:text-[24px] leading-[32px] text-[22px] text-[#191919]">
          {t('header.whats_in')}
        </h3>
      </div>
      <div className="max-w-[1276px] mx-auto flex md:flex-row flex-col gap-[20px]">
        <OffersCard
          icon="/images/offers/guide.svg"
          header={t('cards.career_guide.header')}
          text={t('cards.career_guide.text')}
        />
        <OffersCard
          icon="/images/offers/secrets.svg"
          header={t('cards.stress_management.header')}
          text={t('cards.stress_management.text')}
        />
        <OffersCard
          icon="/images/offers/toolkit.svg"
          header={t('cards.brain_biohacks.header')}
          text={t('cards.brain_biohacks.text')}
        />
      </div>
      <div className="mx-auto md:block hidden md:mt-[32px] md:mb-[160px] font-ppmori font-normal md:text-[15px] md:leading-[27px] text-[#8893AC] text-center">
        <OfferChargeNotice />
      </div>
      <div className="fixed bottom-0 left-0 w-full z-50 bg-white border-t border-[#8F949F]/30 px-[20px] py-[15px] md:px-[82px] md:py-[20px] flex justify-between">
        <button
          onClick={() => {
            handleNext();
          }}
          className="rounded-[10px] bg-[#E8EDF8] py-[15px] px-[20px] text-[#191919] font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {ct('buttons.dont_want_excel')}
        </button>
        <button
          onClick={() => {
            handleReset();
          }}
          className="rounded-[10px] bg-[#FF932F] py-[15px] px-[20px] text-white font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {ct('buttons.accept')}
        </button>
      </div>
      <div className="md:hidden block mt-[20px] mb-[120px] font-ppmori font-normal text-[#8893AC] text-center px-[20px]">
        <OfferChargeNotice />
      </div>
    </div>
  );
}

export default FinalAreYouSure;
