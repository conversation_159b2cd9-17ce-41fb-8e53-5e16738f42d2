const categoriesAndQuestions: { [key: string]: string[] } = {
  'Emotional Self-Awareness': [
    'I am aware of my emotions as I experience them.',
    'I can easily identify why I am feeling a certain way.',
    'I take time to reflect on my emotions and reactions after an event.',
    'I am aware when I am feeling overwhelmed and know how to manage it.',
    'I regularly practice self-care to maintain my emotional well-being.',
    'I can identify and challenge my negative self-talk.',
    'I understand how my actions affect others emotionally.',
    'I accept responsibility for my emotions rather than blaming others.',
    'I am mindful of how my emotional state affects my work performance.',
    'I feel comfortable expressing both positive and negative emotions.',
    'I am aware of how my emotions influence my behavior.',
    'I often consider how my words and actions will affect others emotionally.',
  ],
  'Self-Regulation': [
    'I stay calm and composed under pressure.',
    'I can effectively manage my negative emotions.',
    'I can recover quickly from setbacks or disappointments.',
    'I maintain control over my emotions in challenging situations.',
    'I handle stress well without it affecting my performance.',
    'I can stay focused even when I am emotionally charged.',
    'I can separate my emotions from the facts in decision-making.',
    'I accept criticism without becoming defensive or upset.',
    'I adapt my emotional responses based on the situation and people involved.',
    'I can find constructive ways to express anger or frustration.',
    'I can cheer others up when they are feeling down.',
    'I manage conflicts constructively and stay composed.',
  ],
  'Empathy and Social Awareness': [
    'I am good at recognizing the emotions of others.',
    'I can sense when someone is upset even if they don’t say it.',
    "I am empathetic towards others' feelings and perspectives.",
    'I actively listen to others without interrupting.',
    'I can influence other people’s emotions in a positive way.',
    'I recognize when someone needs emotional support.',
    'I understand and appreciate diverse perspectives.',
    'I often help others manage their emotions.',
    'I am good at reading social cues in group settings.',
    'I encourage others to express their feelings openly.',
    "I am considerate of others' emotional needs.",
    'I strive to maintain harmony in my relationships.',
  ],
  'Relationship Management': [
    'I find it easy to communicate my feelings to others.',
    'I am able to manage conflicts constructively.',
    'I often resolve misunderstandings quickly.',
    'I build and maintain strong relationships with different types of people.',
    'I can mediate when others are in conflict.',
    'I can influence others to resolve their conflicts positively.',
    "I express empathy without being overwhelmed by others' emotions.",
    'I can express my needs and boundaries clearly in relationships.',
    'I communicate openly and avoid bottling up my feelings.',
    'I am comfortable discussing sensitive topics without getting defensive.',
    'I am proactive in finding solutions when I or others feel stuck emotionally.',
    'I remain patient and understanding when others are emotional.',
  ],
  'Adaptability and Resilience': [
    'I can adapt my approach based on the emotional tone of a situation.',
    'I handle change with flexibility and emotional resilience.',
    'I can maintain emotional balance during challenging tasks.',
    'I recover quickly from setbacks or disappointments.',
    'I can turn a negative situation into a learning experience.',
    'I handle criticism without taking it personally.',
    'I can let go of grudges and forgive easily.',
    'I stay positive even when faced with setbacks.',
    'I can maintain focus on my goals despite emotional distractions.',
    'I handle change well without becoming overwhelmed.',
    'I maintain control over my emotions during stressful changes.',
    'I am resilient in the face of emotional challenges.',
  ],
};

export default categoriesAndQuestions;
