//https://matheswaaran.medium.com/cookie-consent-in-nextjs-a05af7c941c8
'use client';

import { useState, useEffect } from 'react';
import { hasCookie, setCookie } from 'cookies-next';
import { useTranslations } from 'next-intl';
import ConsentButton from '@/components/Buttons/ConsentButton';

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(true);
  const t = useTranslations('cookie_consent');

  useEffect(() => {
    setShowConsent(hasCookie('localConsent'));
  }, []);

  const acceptCookie = () => {
    setShowConsent(true);
    setCookie('localConsent', 'true', {});
  };

  if (showConsent) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-slate-700 bg-opacity-70">
      <div className="fixed bottom-0 left-0 right-0 flex items-center justify-between px-10 py-6 bg-grey-bg">
        <p className="mr-16">
          {t('message')}{' '}
          <a href='/privacy' className='text-primary'>
            {t('privacy_link')}
          </a>
          .
        </p>
        <ConsentButton {...{ onClick: acceptCookie, type: 'primary', text: t('btn_accept') }} />
      </div>
    </div>
  );
};

export default CookieConsent;
