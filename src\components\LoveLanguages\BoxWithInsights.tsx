import Image from 'next/image';
import React from 'react';

type BoxWithInsights = {
  color: string;
  source: string;
  alt: string;
  heading: string;
  subheading: string;
  description: string;
};

const BoxWithInsights: React.FC<BoxWithInsights> = ({ color, source, alt, heading, subheading, description }) => {
  return (
    <div className="box-content p-4 md:p-8 bg-white border-l-4 rounded-[12px] z-20" style={{ borderColor: color }}>
      <div className="flex flex-col md:flex-row gap-[20px] md:gap-[43px]">
        <div className="flex flex-col gap-4 md:gap-0 md:justify-between">
          <div className="flex flex-col md:w-[253px] gap-[6px] md:gap-[2px]">
            <h3 className="font-raleway font-bold text-[#0E2432] text-[20px] md:text-[24px] leading-[24px] md:leading-[32px]">
              {heading}
            </h3>
            <h5 className="font-raleway font-semibold text-[#828E98] text-[18px] md:text-[20px] leading-[22px] md:leading-[30px]">
              {subheading}
            </h5>
          </div>
          <Image
            src={source}
            alt={alt}
            width={224}
            height={92}
            className="w-[212px] h-[91px] md:w-[224px] md:h-[92px] mt-[-12px] md:mt-0 mb-[-20px] ml-[-12px]"
          />
        </div>
        <p className="font-raleway font-medium text-[#828E98] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px]">
          {description}
        </p>
      </div>
    </div>
  );
};

export default BoxWithInsights;
