import { useContext, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Offers, PriceDetails } from '@/app/prices';
import OffersPath from '@/components/OffersFlow/OffersPath';
import ProductCard from '@/components/OffersFlow/ProductCard';
import type { BaseNextHandlerProps } from '@/components/OffersFlow/types';
import { useRouter } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

/** The normalized shape your UI consumes */
export interface Price extends Partial<PriceDetails> {
  id: number;
  productKey: 'career_education' | 'stress_management' | 'brain_biohacks';
}

const PRODUCT_MAP = [
  { id: 1, prop: 'careerAndEducation', productKey: 'career_education' as const },
  { id: 2, prop: 'stressManagement', productKey: 'stress_management' as const },
  { id: 3, prop: 'brainBiohacks', productKey: 'brain_biohacks' as const },
];

/**
 * Turn an Offers payload into an array of Prices for the UI.
 *
 * @param offers  The incoming offers (may be undefined)
 * @returns       A list of Price entries for products that actually exist in the payload
 */
export const getPrices = (offers?: Offers): Price[] => {
  const products = offers?.products ?? {};

  return PRODUCT_MAP.map(({ id, prop, productKey }) => {
    const offer = (products as any)[prop] as Partial<PriceDetails> | undefined;

    return {
      id,
      productKey,
      amount: offer?.amount,
      formatted: offer?.formatted,
      original: offer?.original,
    };
  }).filter(Boolean) as Price[];
};

const getAllInOneBundleAmount = (offers?: Offers) => offers?.products?.allInOneBundle?.amount ?? 0;

function ToolkitsOffer({ handleNext }: BaseNextHandlerProps) {
  const router = useRouter();
  const ct = useTranslations('offers_flow');
  const t = useTranslations('offers_flow.three');
  const { offers } = useContext(SessionContext);
  const [selected, setSelected] = useState<number[]>([]);
  const prices = getPrices(offers);

  const selectedTotalAmount = selected.reduce((sum, id) => {
    const item = prices.find(p => p.id === id);
    return sum + (item?.amount ?? 0);
  }, 0);

  const totalPrice = ((selectedTotalAmount || getAllInOneBundleAmount(offers)) ?? 0).toFixed(2);

  const toggleSelect = (id: number) => {
    setSelected(prev => (prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]));
  };

  return (
    <div className="md:mx-auto mx-[20px]">
      <OffersPath section={2} />

      {/* Header */}
      <div className="text-center md:mt-[80px] mt-[55.6px] font-ppmori ">
        <h1 className="font-semibold md:text-[36px] md:leading-[56px] text-[32px] leading-[37px] text-[#191919]">
          {t('header.title1')} <br className="md:hidden block" /> {t('header.title2')}
        </h1>
        <div className="w-fit mx-auto flex flex-row gap-[6px] md:rounded-[8px] rounded-[5px] md:mt-[22px] mt-[15px] md:py-[10px] md:px-[14px] py-[5px] px-[28px] bg-[#FFF4EA] border border-[0.5px] border-[#FF932F]/25">
          <img src="/images/offers/fire.svg" className="md:w-[16px] md:h-[16px] w-[14.4px] h-[14.4px]" />
          <span className="text-[#FF932F] font-ppmori font-semibold md:text-[16px] leading-[20px] text-[13px] leading-[18px]">
            {t('header.available_now')}
          </span>
        </div>
        <h3 className="md:pt-4 pt-[11.6px] font-normal md:text-[18px] md:leading-[32px] text-[16px] leading-[27px] md:text-[#191919] text-[#8893AC]">
          {t('header.description1')} <br className="md:hidden block" /> {t('header.description2')}
        </h3>
      </div>

      {/* Product Cards */}
      <div className="max-w-[952px] md:mt-[32px] mt-[20px] mx-auto flex md:flex-row flex-col gap-[20px]">
        {prices.map(({ id, productKey, formatted, original }) => {
          return (
            <ProductCard
              key={id}
              id={id}
              isSelected={selected.includes(id)}
              productKey={productKey}
              formatted={formatted || ''}
              original={original || ''}
              toggleSelect={toggleSelect}
            />
          );
        })}
      </div>

      {/* Confirmation */}
      <div className="mx-auto md:block hidden md:mt-[32px] md:mb-[160px] font-ppmori font-normal md:text-[15px] md:leading-[27px] text-[#8893AC] text-center">
        By clicking '{selected.length ? ct('buttons.accept') : ct('buttons.claim')}', we'll charge {offers?.symbol}
        {totalPrice} to your payment method on file. This is a one-time charge, not a recurring one.
      </div>

      {/* Buttons */}
      <div className="fixed bottom-0 left-0 w-full z-50 bg-white border-t border-[#8F949F]/30 px-[20px] py-[15px] md:px-[82px] md:py-[20px] flex justify-between">
        <button
          onClick={() => handleNext()}
          className="rounded-[10px] bg-[#E8EDF8] py-[15px] px-[20px] text-[#191919] font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {ct('buttons.skip')}
        </button>
        <button
          onClick={() => (selected.length ? handleNext() : router.push('/results'))}
          className="rounded-[10px] bg-[#FF932F] py-[15px] px-[20px] text-white font-ppmori font-semibold md:text-[20px] text-[18px] md:leading-[24px] leading-[21.6px]">
          {selected.length ? ct('buttons.accept') : ct('buttons.claim')}
        </button>
      </div>

      {/* Mobile Confirmation */}
      <div className="md:hidden block mt-[20px] mb-[120px] font-ppmori font-normal text-[#8893AC] text-center px-[20px]">
        By clicking '{selected.length ? ct('buttons.accept') : ct('buttons.claim')}', we'll charge {offers?.symbol}
        {totalPrice} to your payment method on file. This is a one-time charge, not a recurring one.
      </div>
    </div>
  );
}

export default ToolkitsOffer;
