import { useTranslations } from 'next-intl';
import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

const SubscriptionChargeAnswer = () => {
  const { siteConfig } = useContext(SessionContext);
  const t = useTranslations('support_page.faq_details.subscription-charge.answer');

  // Get the list items safely
  const welcomeEmailItems = t.raw('welcome_email_items') || [];
  const sectionsItems = t.raw('sections_items') || [];
  const billingItems = t.raw('billing_items') || [];

  return (
    <>
      <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {t.rich('paragraph1', {
          siteName: siteConfig.siteName,
          span: chunks => <span className="text-primary">{chunks}</span>,
        })}
      </p>
      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph2') }}
      />
      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph3') }}
      />
      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t('paragraph4') }}
      />

      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {welcomeEmailItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>

      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph5') }}
      />

      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {sectionsItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>

      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph6') }}
      />
      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t('paragraph7') }}
      />

      <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
        {billingItems.map((item: string, index: number) => (
          <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
        ))}
      </ul>

      <p
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]"
        dangerouslySetInnerHTML={{ __html: t('paragraph8', { siteName: siteConfig.siteName }) }}
      />
    </>
  );
};

export default SubscriptionChargeAnswer;
