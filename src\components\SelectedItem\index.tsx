import { FC } from 'react';
import CheckedCheckbox from '@/components/CheckedCheckbox';

interface SelectedItemProps {
  text?: String;
}

const SelectedItem: FC<SelectedItemProps> = ({ text }) => {
  return (
    <div className="rounded-lg flex flex-start items-center gap-[10px]">
      <label className="inline-block">
        <CheckedCheckbox />
        <div className={`w-6 h-6 rounded-[5px] flex items-center justify-center transition bg-[#F0F1F5] shadow`}>
          <svg className="w-6 h-6 text-[#5DC4FF]" fill="none" stroke="currentColor" strokeWidth={3} viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
          </svg>
        </div>
      </label>
      <span className="font-segoe font-normal text-[14px] leading-[21px] text-[#3F425E]">{text}</span>
    </div>
  );
};

export default SelectedItem;
