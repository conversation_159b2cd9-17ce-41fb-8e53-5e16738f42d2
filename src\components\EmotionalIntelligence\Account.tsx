'use client';

import React from 'react';
import Image from 'next/image';
import { Link } from '@/lib/i18n/routing';
import { useTranslations } from 'next-intl';

const Account: React.FC = () => {
  const t = useTranslations('emotional_intelligence.account');
  
  return (
    <Link href="/user/account" className="flex items-center justify-end relative z-10">
      <Image
        src="/images/emotional-intelligence/account.png"
        alt={t('alt')}
        width={62}
        height={62}
        className="w-[38px] md:w-[62px] h-auto z-20"
      />
    </Link>
  );
};

export default Account;
