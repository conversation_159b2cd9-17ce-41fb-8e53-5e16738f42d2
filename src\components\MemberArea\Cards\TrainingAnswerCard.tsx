import Image from 'next/image';
import { useTranslations } from 'next-intl';
// import { useEffect, useState } from 'react';
// import { getDownloadURL, ref } from 'firebase/storage';
// import { storage } from '@/utils/firebase';

const TrainingAnswerCard = ({
  question,
  answerId,
  selected,
}: {
  question: {
    id: number;
    originalQuestionId: number;
    correctAnswerId: number;
    category: string;
    explanation: string;
  };
  answerId: number;
  selected: boolean;
}) => {
  const t = useTranslations('questions.alt');
  // const [imageUrl, setImageUrl] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchImageUrl = async () => {
  //     try {
  //       const imageRef = ref(storage, `training-questions/${question.originalQuestionId}/answers/${answerId}.svg`);
  //       const url = await getDownloadURL(imageRef);
  //       setImageUrl(url);
  //     } catch (error) {
  //       console.error('Error fetching image URL:', error);
  //     }
  //   };

  //   fetchImageUrl();
  // }, [answerId, question]);

  return (
    <div
      className={`p-4 flex flex-col justify-center items-center text-center relative cursor-pointer border border-transparent ${
        selected ? (question.correctAnswerId == answerId ? '!border-[#10B981]' : '!border-red-500') : ''
      }`}
      style={{
        boxShadow:
          '-3.563px 7.126px 12.471px 0px rgba(104, 129, 177, 0.08), -0.891px 0.891px 0.891px 0.891px rgba(141, 160, 188, 0.08)',
      }}>
      <Image
        src={`/training-questions/${question.originalQuestionId}/answers/${answerId}.svg`}
        alt={t('answer_image')}
        width={120}
        height={120}
        className="transition-opacity opacity-0 duration-[1s]"
        unoptimized
        draggable={false}
        onLoad={e => e.currentTarget.classList.remove('opacity-0')}
        priority
      />
      <span className="font-semibold" style={{ color: '#191919', fontSize: 20, lineHeight: '180%' }}>
        {(answerId + 9).toString(36)}
      </span>
    </div>
  );
};

export default TrainingAnswerCard;
