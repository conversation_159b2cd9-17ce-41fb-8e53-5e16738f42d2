'use client';

import { useTranslations } from 'next-intl';
import ImproveCard from '@/components/ImproveCard';

export default function ImpactSection() {
  const t = useTranslations('checkout_v2.impact');

  return (
    <div className="md:max-w-[1276px] m-auto py-6 px-3 md:px-0">
      <h1 
        className="text-[38px] md:text-[52px] text-center text-[#191919] font-bold tracking-tight leading-[56px] py-10"
        dangerouslySetInnerHTML={{ __html: t.raw('title') }}
      />
      <div className="md:flex md:space-x-6 md:mb-10">
        <ImproveCard
          imgSource="improve_1"
          header={t('cards.confident_solutions.header')}
          text={t('cards.confident_solutions.text')}
        />
        <ImproveCard
          imgSource="improve_2"
          header={t('cards.surpass_competitors.header')}
          text={t('cards.surpass_competitors.text')}
        />
        <ImproveCard
          imgSource="improve_3"
          header={t('cards.skill_mastery.header')}
          text={t('cards.skill_mastery.text')}
        />
      </div>
      <div className="md:flex md:space-x-6 md:mb-10">
        <ImproveCard
          imgSource="improve_4"
          header={t('cards.career_advancement.header')}
          text={t('cards.career_advancement.text')}
        />
        <ImproveCard
          imgSource="improve_5"
          header={t('cards.smart_choices.header')}
          text={t('cards.smart_choices.text')}
        />
        <ImproveCard
          imgSource="improve_6"
          header={t('cards.confidently_conquer.header')}
          text={t('cards.confidently_conquer.text')}
        />
      </div>
    </div>
  );
} 