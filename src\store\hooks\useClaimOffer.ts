import { useCallback, useState } from 'react';
import lscache from 'lscache';
import { update } from '@/store/utils';

/**
 * React hook to manage the current claim offer status.
 *
 * Initializes the claimOffer state from local storage (key: 'se-cp_cop') if available,
 * otherwise defaults to false.
 *
 * @example
 * const { claimOffer, setClaimOffer, updateClaimOffer } = useClaimOffer();
 * updateClaimOffer(true);
 */
export function useClaimOffer() {
  const [claimOffer, setClaimOffer] = useState<boolean>(lscache.get('se-cp_cop') ?? false);

  // Update claim offer in localStorage
  const updateClaimOffer = useCallback(
    async (newClaimOffer: boolean) => {
      update({
        contextPrefix: 'se',
        variableName: 'cp_cop',
        newVariable: newClaimOffer,
        setter: setClaimOffer,
        getter: () => claimOffer,
      });
    },
    [claimOffer]
  );

  return { claimOffer, setClaimOffer, updateClaimOffer };
}
