'use client';
import { createContext, useState, useEffect, useCallback } from 'react';
import lscache from 'lscache';
import { update } from './utils';
import { formatDate } from '@/utils/date';
import type { UiContextProps } from './types';

const UiContext = createContext<UiContextProps>({
  completedByNumber: 0,
  today: formatDate(new Date()),
  time: 0,
  updateCompletedByNumber: async (newCompletedByNumber: number) => {},
  updateToday: async (newToday: string) => {},
  updateTime: async (newTime: number) => {},
});

export const UiProvider: any = ({ children }: { children: any }) => {
  const [completedByNumber, setCompletedByNumber] = useState<number>(+lscache.get('ui-completedByNumber')); //Completed by how many users
  const [today, setToday] = useState<string>(lscache.get('ui-today') || formatDate(new Date()));
  const [time, setTime] = useState<number>(+lscache.get('ui-time') || 0); //Time spent

  const createUi = useCallback(() => {
    updateToday(formatDate(new Date()));
    updateCompletedByNumber(Math.floor(Math.random() * (10000 - 5000 + 1) + 5000));
  }, []);

  useEffect(() => {
    const todayFromLocalStorage = lscache.get('ui-today');
    const completedByNumberFromLocalStorage = lscache.get('ui-completedByNumber');
    if (todayFromLocalStorage !== null && completedByNumberFromLocalStorage !== null) {
      hydrateFromLocalStorage();
    } else {
      createUi();
    }
  }, [createUi]);

  const hydrateFromLocalStorage = () => {
    const completedByNumberFromLocalStorage = lscache.get('ui-completedByNumber');
    completedByNumberFromLocalStorage && setCompletedByNumber(completedByNumberFromLocalStorage);

    const todayFromLocalStorage = lscache.get('ui-today');
    todayFromLocalStorage && setToday(todayFromLocalStorage);

    const timeFromLocalStorage = lscache.get('ui-time');
    timeFromLocalStorage && setTime(timeFromLocalStorage);
  };

  const updateCompletedByNumber = useCallback(
    async (newCompletedByNumber: number) => {
      const result = update({
        contextPrefix: 'ui',
        variableName: 'completedByNumber',
        newVariable: newCompletedByNumber,
        oldVariable: completedByNumber,
        setter: setCompletedByNumber,
        getter: () => completedByNumber,
      });
      
    },
    [completedByNumber],
  );

  const updateToday = useCallback(
    async (newToday: string) => {
      const result = update({
        contextPrefix: 'ui',
        variableName: 'today',
        newVariable: newToday,
        oldVariable: today,
        setter: setToday,
        getter: () => today,
      });
      
    },
    [today],
  );

  const updateTime = useCallback(
    async (newTime: number) => {
      const result = update({
        contextPrefix: 'ui',
        variableName: 'time',
        newVariable: newTime,
        oldVariable: time,
        setter: setTime,
        getter: () => time,
      });
      
    },
    [time],
  );

  return (
    <UiContext.Provider
      value={{
        completedByNumber,
        today,
        time,
        updateCompletedByNumber,
        updateToday,
        updateTime,
      }}
    >
      {children}
    </UiContext.Provider>
  );
};

export default UiContext;
