import GuaranteeTag from '@/components/Tags/Checkout/GuaranteeTag';
import { useTranslations } from 'next-intl';

const GuaranteeSection = () => {
  const t = useTranslations('checkout.guarantee_section.items');
  
  const guarantees = [
    t('premium_support'),
    t('refund'),
    t('satisfaction')
  ];

  return (
    <ul className='flex flex-wrap flex-col m-auto md:flex-row w-[calc(100%-32px)] md:w-full md:w-auto justify-center gap-2 mt-4'>
      {guarantees.map((text, i) => (
        <GuaranteeTag key={i} {...{ text }} />
      ))}
    </ul>
  );
};

export default GuaranteeSection;
