import '@/sass/form.scss';
import Brand from '@/components/Brand';
import { UiProvider } from '@/store/UiContext';

export default function FormLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div>
      <div className='flex justify-center pt-10 pb-11'>
        <Brand {...{ position: 'title' }} />
      </div>
      <UiProvider>{children}</UiProvider>
    </div>
  );
}
