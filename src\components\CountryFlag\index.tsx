'use client';

import { memo } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { getCountryFlag } from '@/utils/getCountryFlag';

interface CountryFlagProps {
  countryId: string;
  countryName: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

const CountryFlag = ({
  countryId,
  countryName,
  width = 30,
  height = 16,
  className = '',
  priority = false
}: CountryFlagProps) => {
  const t = useTranslations('home_page');
  const flagSrc = getCountryFlag(countryId);
  const fallbackSrc = getCountryFlag('us');

  return (
    <Image
      className={className}
      src={flagSrc}
      alt={t('alt.country_flag', { country: countryName })}
      width={width}
      height={height}
      priority={priority}
      unoptimized
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.src = fallbackSrc;
      }}
    />
  );
};

export default memo(CountryFlag); 