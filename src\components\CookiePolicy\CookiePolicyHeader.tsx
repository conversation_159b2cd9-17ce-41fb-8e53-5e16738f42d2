'use client';

import { useTranslations } from 'next-intl';

interface CookiePolicyHeaderProps {
  effectiveDate: string;
  lastUpdatedDate: string;
}

const CookiePolicyHeader = ({ effectiveDate, lastUpdatedDate }: CookiePolicyHeaderProps) => {
  const t = useTranslations('cookie_policy');

  return (
    <div className="cookie-policy-header">
      <h1 className='text-5xl'>{t('title')}</h1>
      <p style={{ marginTop: 16 }}>{t('effective_date', { date: effectiveDate })}</p>
      <p>{t('last_updated', { date: lastUpdatedDate })}</p>
      &nbsp;
    </div>
  );
};

export default CookiePolicyHeader; 