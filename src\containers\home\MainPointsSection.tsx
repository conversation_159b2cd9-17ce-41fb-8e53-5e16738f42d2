import { useTranslations } from 'next-intl';
import MainPointsCard from '@/components/Cards/HomePage/MainPointsCard';

const MainPointsSection = () => {
  const t = useTranslations('home_page');

  const data = [
    {
      img: '/home/<USER>/1.jpg',
      title: t('main_points_card1_title'),
      text: t('main_points_card1_text'),
    },
    {
      img: '/home/<USER>/2.jpg',
      title: t('main_points_card2_title'),
      text: t('main_points_card2_text'),
    },
    {
      img: '/home/<USER>/3.jpg',
      title: t('main_points_card3_title'),
      text: t('main_points_card3_text'),
    },
  ];

  return (
    <section className='m-auto lg:mt-10 pt-0'>
      <h2 className='big text-center max-w-[90vw] md:max-w-[508px] mx-auto'>
        {t('main_points_title')}
      </h2>
      <p className='text-center max-w-[90vw] md:max-w-[460px]' style={{ margin: '24px auto 60px auto' }}>
        {t('main_points_description')}
      </p>
      <div
        className='flex flex-wrap basis-1/3 gap-10 lg:gap-16 mx-5 sm:mx-0'
        style={{ justifyContent: 'center', zoom: 0.8 }}
      >
        {data.map((card, i) => (
          <MainPointsCard key={i} {...{ ...card, order: i, t }} />
        ))}
      </div>
    </section>
  );
};

export default MainPointsSection;
