'use client';

import { useTranslations } from 'next-intl';
import requireAuth from '@/components/requireAuth';
import ToolkitsCard from '@/components/ToolkitsCard';
import { useToolkitPlan } from '@/store/hooks/useToolkitPlan';
import { useRouter } from '@/lib/i18n/routing';

function ToolkitsPage() {
  const t = useTranslations('academy');
  const tt = useTranslations('toolkits');
  const router = useRouter();
  const { hasCareerToolkitAccess, hasStressToolkitAccess, hasBrainToolkitAccess } = useToolkitPlan();

  const handleBack = () => {
    router.push('/academy');
  };

  /**
   * Download the Career & Education IQ Guide toolkit
   */
  const handleCareerClick = () => {
    if (!hasCareerToolkitAccess) return;

    try {
      // Track the download event
      // Create download link and trigger download
    } catch (error) {
      console.error('Error downloading career toolkit:', error);
    }
  };

  /**
   * Download the Stress Management Secrets toolkit
   */
  const handleStressClick = () => {
    if (!hasStressToolkitAccess) return;

    try {
      // Track the download event
      // Create download link and trigger download
    } catch (error) {
      console.error('Error downloading stress toolkit:', error);
    }
  };

  /**
   * Download the Brain Biohacks Toolkit
   */
  const handleBrainClick = () => {
    if (!hasBrainToolkitAccess) return;

    try {
      // Track the download event
      // Create download link and trigger download
    } catch (error) {
      console.error('Error downloading brain toolkit:', error);
    }
  };

  return (
    <div className="w-full md:max-w-[1440px] flex flex-col gap-[32px] font-ppmori md:mt-4 md:mx-[82px] mx-4 md:mb-[180px]">
      <button className="flex flex-row gap-1" onClick={handleBack}>
        <img src="/images/offers/back.svg" className="w-[26px] h-auto" />
        <span className="font-semibold text-[18px] leading-[26px] text-[#191919] mt-[2.4px]">{t('back')}</span>
      </button>
      <div className="w-full flex md:flex-row flex-col gap-[20px] mx-auto">
        <ToolkitsCard
          imgSource="/images/career.svg"
          imgWidth={60}
          imgHeight={60}
          header={tt('career.header')}
          text={tt('career.text')}
          available={!!hasCareerToolkitAccess}
          onClick={handleCareerClick}
        />
        <ToolkitsCard
          imgSource="/images/stress.svg"
          imgWidth={60}
          imgHeight={60}
          header={tt('stress.header')}
          text={tt('stress.text')}
          available={!!hasStressToolkitAccess}
          onClick={handleStressClick}
        />
        <ToolkitsCard
          imgSource="/images/brain.svg"
          imgWidth={60}
          imgHeight={60}
          header={tt('brain.header')}
          text={tt('brain.text')}
          available={!!hasBrainToolkitAccess}
          onClick={handleBrainClick}
        />
      </div>
    </div>
  );
}

export default requireAuth(ToolkitsPage, ['has_toolkits_access']);
