.steps-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-top: 3px solid theme('colors.primary');
  padding-top: 15px;
}

.steps-container .dot {
  //opacity: 0;
  background-color: #fff;
  border: 2px solid theme('colors.primary');
  margin-top: -25px;
  width: 18px;
  height: 18px;
  line-height: 18px;
  border-radius: 50%;
  max-height: calc(100vw / 30 - 2px);
  max-width: calc(100vw / 30 - 2px);
  /*z-index: 99;*/
}

@media only screen and (max-width: 540px) {
  .steps-container .dot {
    margin-top: -23px;
  }
}

@media only screen and (max-width: 360px) {
  .steps-container .dot {
    margin-top: -21px;
  }
}

.steps-container .text {
  margin-top: -45px;
  position: relative;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #191919;
  line-height: 125%;
}

.steps-container .step-1 .text,
.steps-container .step-30 .text {
  color: rgba(143, 148, 159, 0.3);
}

/*.steps-container .step-2 .dot {
  //opacity: 1 !important;
  background-color: #fff;
  border: 2px solid theme('colors.primary');
  margin-top: -25px;
  //width: 18px;
  width: 60px;
  height: 18px;
  line-height: 18px;
  //border-radius: 50%;
}*/

.steps-container .active {
  background-color: theme('colors.primary') !important;
  opacity: 1 !important;
  width: 18px;
  border-radius: 50%;
}

/*@keyframes showTimer {
  to {
    visibility: visible;
    color: theme('colors.primary');
  }
}

.timer {
  visibility: hidden;
  color: #fff;
  animation: showTimer 0s 1s forwards;
}*/
