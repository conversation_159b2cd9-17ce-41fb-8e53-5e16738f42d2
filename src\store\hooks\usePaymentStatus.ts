import { useState } from 'react';
import lscache from 'lscache';

/**
 * React hook to manage the current payment status.
 *
 * Initializes the payment status state from local storage (key: 'se-cp_pss') if available.
 *
 * @example
 * const { paymentStatus, setPaymentStatus } = usePaymentStatus();
 * setPaymentStatus('success');
 */
export function usePaymentStatus() {
  const [paymentStatus, setPaymentStatus] = useState<string>(lscache.get('se-cp_pss'));
  return { paymentStatus, setPaymentStatus };
}
