import Image from "next/image";
import React, { ReactNode } from "react";

type IconWithSmallSupportProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithSmallSupport: React.FC<IconWithSmallSupportProps> = ({
  source,
  alt,
  children,
}) => {
  return (
    <div className="flex flex-row items-center py-[8px] px-[12px] md:px-[10px] md:py-[6px] gap-[8px] bg-[#F6F6F6] rounded-[8px]">
      <Image
        src={source}
        alt={alt}
        width={18}
        height={18}
        className="w-[16px] h-[16px]"
      />
      <span
        className="font-raleway font-medium text-[14px] leading-[18px] text-[#0E2432]"
        style={{
          fontFeatureSettings: "'pnum' on, 'lnum' on",
        }}
      >
        {children}
      </span>
    </div>
  );
};

export default IconWithSmallSupport;
