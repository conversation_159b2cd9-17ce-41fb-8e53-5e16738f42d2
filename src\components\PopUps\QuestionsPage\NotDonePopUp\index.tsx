import { memo } from 'react';
import Image from 'next/image';
import Modal from 'react-modal';
import { useTranslations } from 'next-intl';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { Link } from '@/lib/i18n/routing';
import { PostHogEventEnum } from '@/store/types';

interface NotDonePopUpProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const NotDonePopUp = ({
  visible,
  setVisible,
}: NotDonePopUpProps) => {
  const { captureEvent } = usePostHogAnalytics();
  const t = useTranslations('questions.not_done_popup');

  return (
    <Modal
      isOpen={visible}
      ariaHideApp={false}
      shouldCloseOnOverlayClick={true}
      onRequestClose={() => {
        setVisible(false);
      }}
      contentLabel="I am not done"
      className=""
      style={{
        content: {
          bottom: 'auto',
          minHeight: '10rem',
          left: 'calc(50% - 10px)',
          padding: '2rem',
          position: 'fixed',
          right: 'auto',
          top: '50%',
          transform: 'translate(-50%,-50%)',
          minWidth: '20rem',
          width: 'calc(95% - 20px)',
          maxWidth: 547,
          margin: 10,
          borderRadius: 16,
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          textAlign: 'center',
        },
      }}>
      <Image
        src={`/questions/popup.svg`}
        alt={t('alt.popup_illustration')}
        width={203}
        height={136}
        priority
      />
      <h4 className="big" style={{ marginTop: 32, maxWidth: 400 }}>
        {t('title')}
      </h4>
      <p style={{ marginTop: 20, maxWidth: 480 }}>
        {t('description')}
      </p>
      <div
        className="flex flex-wrap justify-center popupMd:justify-between gap-4"
        style={{ marginTop: 32, padding: '0 21.5px', width: '100%' }}>
        <div>
          <button
            onClick={() => {
              captureEvent(PostHogEventEnum.DECLINED_GET_RESULTS, {
                description: 'Declined to get results with some skipped questions',
              });
              setVisible(false);
            }}
            className={`button primaryColor font-semibold !text-lg/6 xs:!text-xl/6 text-primary border border-primary`}
            style={{
              padding: '17px 32px',
              display: 'inline-flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              background: '#fff',
              borderRadius: 10,
              letterSpacing: '-0.6px',
            }}>
            {t('buttons.no')}
          </button>
        </div>
        <div>
          <Link
            href={`/calculation?origin=questions`}
            className={`button primaryColor font-semibold bg-primary border border-primary`}
            style={{
              padding: '17px 32px',
              display: 'inline-flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              borderRadius: 10,
              fontSize: 20,
              color: '#fff',
              lineHeight: '120%',
              letterSpacing: '-0.6px',
            }}>
            <button
              onClick={() =>
                captureEvent(PostHogEventEnum.CONFIRMED_GET_RESULTS, {
                  description: 'Confirmed to get results with some skipped questions',
                })
              }>
              {t('buttons.yes')}
            </button>
          </Link>
        </div>
      </div>
    </Modal>
  );
};

export default memo(NotDonePopUp);
