"use client";

import { FC, memo, useContext } from 'react';
import { Link } from '@/lib/i18n/routing';
import BrandLogo from './BrandLogo';

interface BrandProps {
  style?: any;
  classes?: string;
  position?: string;
}

const Brand: FC<BrandProps> = ({ style, classes, position }) => {
  return (
    <Link href='/' className={`flex gap-3 items-center ${position !== 'title' ? 'mr-5' : 'justify-center'} ${classes}`}>
      <BrandLogo />
    </Link>
  );
};

export default memo(Brand);
