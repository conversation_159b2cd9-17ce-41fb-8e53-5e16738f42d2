import lscache from 'lscache';

export const update = ({
  contextPrefix,
  variableName,
  newVariable,
  oldVariable,
  setter,
  getter,
}: {
  contextPrefix: string;
  variableName: string;
  newVariable: string | number | any;
  oldVariable?: string | number | any;
  setter: React.Dispatch<React.SetStateAction<any>>;
  getter: () => any;
}) => {
  setter(newVariable);
  const addedToLocalStorage = lscache.set('' + contextPrefix + '-' + variableName, newVariable, 525601);
  return addedToLocalStorage
    ? `Prop: ${variableName} successfully updated.`
    : `Failure during update for prop called: ${variableName}.`;
};

export const remove = ({
  contextPrefix,
  variableName,
  newVariable,
  setter,
}: {
  contextPrefix: string;
  variableName: string;
  newVariable: string | number | any;
  setter: React.Dispatch<React.SetStateAction<any>>;
}) => {
  if (typeof newVariable === 'number') {
    setter(0);
  }
  if (typeof newVariable === 'string') {
    setter('');
  }
  if (typeof newVariable === 'boolean') {
    setter(false);
  }

  lscache.remove('' + contextPrefix + '-' + variableName);
  return `Prop: ${variableName} successfully removed.`;
};
