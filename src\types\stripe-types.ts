import { PageTypes } from '@/store/types';
import { FixedPlanDuration, SubscriptionPlan, PlanType } from './plan-types';

/**
 * Common properties shared by all plan metadata types.
 */
export interface StripeBasePlanMetadata {
  email: string;
  name: string;
  sessionId: string;
  clickId?: string | null;
  completionTime?: number | null;
  checkoutVersion?: string;
  isTrial?: boolean | null;
  landingUrl?: string | null;
  ip?: string;
  country?: string;
  currency?: string;
  pageType?: PageTypes;
  deviceType?: string;
  deviceOS?: string;
  browser?: string;
  plan?: SubscriptionPlan;
  planType?: PlanType;
}

/**
 * Metadata for a one-time (fixed) plan purchase.
 */
type StripeOnetimePlanMetadata = StripeBasePlanMetadata & {
  duration: FixedPlanDuration;
};

/**
 * Metadata for a subscription plan purchase.
 */
type StripeSubscriptionPlanMetadata = StripeBasePlanMetadata;

/**
 * Union type for all possible plan metadata types.
 */
export type StripePlanMetadata = StripeOnetimePlanMetadata | StripeSubscriptionPlanMetadata;
