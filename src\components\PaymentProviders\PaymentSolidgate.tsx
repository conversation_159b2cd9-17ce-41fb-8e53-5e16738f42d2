'use client';

import { useContext, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import CheckoutFormSolidgate from '@/components/PaymentProviders/CheckoutForms/Solidgate';
import { PaymentTextWrapper } from '@/components/PaymentProviders/PaymentText';
import useSolidgateMerchant, { MerchantData } from '@/hooks/useSolidgateMerchant';
import SessionContext from '@/store/SessionContext';
import type { PaymentContextProps } from '@/types/payment';
import { useTrackGTMEvents } from '@/hooks/useTrackGTMEvents';

export default function PaymentSolidgate({ context }: PaymentContextProps) {
  const { clickId, isTrial, isOneTime, time } = context;
  const t = useTranslations('checkout.payment_details');
  const { prices, plan } = useContext(SessionContext);
  const { getSubscriptionMerchantData } = useSolidgateMerchant();
  const { trackGTMBeingCheckoutEvent } = useTrackGTMEvents();
  const [merchantData, setMerchantData] = useState<MerchantData | null>(null);

  useEffect(() => {
    /**
     * Retrieves and sets the merchant data based on the payment type,
     * and triggers the GTM checkout tracking event.
     */
    const getSolidgateMerchant = async () => {
      const merchantData = await getSubscriptionMerchantData({ clickId, time, isTrial });
      if (!merchantData) return;

      setMerchantData(merchantData);
      trackGTMBeingCheckoutEvent(prices); // Track post-checkout event
    };

    getSolidgateMerchant();
  }, [clickId, time, isTrial]);

  return (
    merchantData && (
      <div id="paymentDetails" className="w-full flex justify-center">
        <div className="flex flex-col w-full">
          <h4 className="text-center md:text-left mb-4 md:mb-6">{t('title')}</h4>
          <div className="w-full px-5 md:px-0">
            <CheckoutFormSolidgate context={{ clickId, isTrial, isOneTime, time }} />

            <PaymentTextWrapper isTrial={isTrial ?? false} isOneTime={isOneTime ?? false} prices={prices} plan={plan} />
          </div>
        </div>
      </div>
    )
  );
}
