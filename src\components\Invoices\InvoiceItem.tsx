'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { centsToCurrency } from '@/app/utils';

interface InvoiceItemProps {
  amount: number;
  currency: string;
  status: string;
  date: string;
  children?: React.ReactNode;
}

const InvoiceItem: React.FC<InvoiceItemProps> = ({ amount, currency, status, date, children }) => {
  const t = useTranslations('invoices');

  return (
    <div className="flex gap-3 mt-5">
      <div>
        {t('labels.amount')}: <span className="font-bold text-primary">{centsToCurrency(amount)}</span>{' '}
        <span>{currency},</span>
      </div>
      <div>
        {t('labels.status')}: <span className="font-bold text-primary">{status}</span>
      </div>
      <div>
        {t('labels.date')}: <span className="font-bold text-primary">{date}</span>
      </div>
      {children}
    </div>
  );
};

export default InvoiceItem;
