//https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#link-component

'use client';

import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import { Link as I18nLink, usePathname } from '@/lib/i18n/routing';
import { menuItemTranslationKeys } from '@/app/config';
import useLayoutStore from '@/store/useLayoutStore';

type MenuItemProps = {
  item: {
    id: string;
    path: string;
    title: string;
    memberOnly?: boolean;
  };
};

const MenuItem = ({ item: { id, path, title, memberOnly } }: MenuItemProps) => {
  const t = useTranslations();
  const activePath = usePathname();
  const { handleMobileOpen } = useLayoutStore();

  // Use regular Link for insights to bypass internationalization
  const Link = id === 'insights' ? NextLink : I18nLink;

  return (
    <>
      <Link
        className={`${path === activePath ? 'border-primary' : 'border-transparent'} transition-all duration-300 lgh:text-base xl:!text-lg border-b-2 hover:border-primary pb-1 font-semibold  ${memberOnly ? 'text-primary' : 'text-black'}`}
        href={`${path}`}
        onClick={() => handleMobileOpen(false)}
        prefetch={path.includes('insights') ? false : true}
      >
        {t(menuItemTranslationKeys[id] || title)}
      </Link>
    </>
  );
};
export default MenuItem;
