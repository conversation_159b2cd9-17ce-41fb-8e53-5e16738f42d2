const posthogApiKey: string | undefined = process.env.NEXT_PUBLIC_POSTHOG_API_KEY;
const posthogHost: string | undefined = process.env.NEXT_PUBLIC_POSTHOG_HOST;

if (!posthogApiKey || !posthogHost) {
  throw new Error('Missing NEXT_PUBLIC_POSTHOG_API_KEY or NEXT_PUBLIC_POSTHOG_HOST');
}

const posthogScript = `
!function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);

posthog.init('${posthogApiKey}', {
    api_host: '${posthogHost}',
    autocapture: false,
    person_profiles: 'always',
    capture_pageview: false
});

posthog.capture('$pageview', {
    current_url: window.location.href,
    page_title: document.title,
    origin: "Unbounce landing page",
    timestamp: new Date().toISOString()
});

function trackCTAClick() {
    const ctaElements = document.querySelectorAll('.lp-element.lp-pom-button');
    if (ctaElements.length > 0) {
        ctaElements.forEach(element => {
            if (element.dataset.posthogTracked) return;
            element.dataset.posthogTracked = 'true';
            element.addEventListener('click', function() {
                const spanLabel = element.querySelector('span.label');
                const buttonText = spanLabel.textContent?.trim();
                
                posthog.capture('Start test', {
                    button_text: buttonText,
                    page_url: window.location.href,
                    timestamp: new Date().toISOString(),
                    button_id: element.id || 'no-id'
                });
            });
        });
    } else {
        console.warn('No elements with classes "lp-element" and "lp-pom-button" found');
    }
}

document.addEventListener('DOMContentLoaded', trackCTAClick);
`;

export default posthogScript;
