const getCompanyInfo = require('./companies.config.js');

/**
 * Gets the website URL with appropriate fallback
 * @returns {string} The website URL
 */
const getWebsiteUrl = () => {
  if (typeof window !== 'undefined') {
    // Running on client-side
    return process.env.NEXT_PUBLIC_WEBSITE_URL ?? `https://${window.location.hostname}`;
  }

  // Running on server-side
  return process.env.NEXT_PUBLIC_WEBSITE_URL ?? process.env.NEXT_PUBLIC_HOST_URL;
};

const siteConfig = {
  siteName: 'IQ International',
  domain: 'iqinternational.org',
  websiteUrl: getWebsiteUrl(), // Fallback to host URL if website URL is not defined
  supportEmail: '<EMAIL>',
  hostingProvider: 'Vercel Inc.',
  gtmId: 'GTM-WRTN3SWB',
  companyName: null,
  companyAddress: null,
  companyRegistrationNumber: null,
  companyPhone: null,
  euVat: null,
  logo: {
    path: '/images/Logo.svg',
    width: 300,
    height: 36,
  },
  primaryColor: '#ff932f',
  insights: {
    show: true,
    url: 'https://blog.iqinternational.org',
  },
  fonts: {
    src: [
      {
        path: './fonts/PPMori-Regular.woff2',
        weight: '400',
        style: 'normal',
      },
      {
        path: './fonts/PPMori-SemiBold.woff2',
        weight: '600',
        style: 'normal',
      },
    ],
  },
};

function getSiteConfig(country) {
  return { ...siteConfig, ...getCompanyInfo(country) };
}

module.exports = { getSiteConfig, siteConfig };
