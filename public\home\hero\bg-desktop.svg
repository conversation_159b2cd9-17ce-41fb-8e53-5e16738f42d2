<svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1140_4854)">
<rect width="1440" height="900" fill="url(#paint0_linear_1140_4854)"/>
<g filter="url(#filter0_f_1140_4854)">
<circle cx="24" cy="824" r="270" fill="#E5D1FF" fill-opacity="0.12"/>
</g>
<g filter="url(#filter1_f_1140_4854)">
<ellipse cx="1106" cy="180" rx="345" ry="270" fill="#FDF8EA"/>
</g>
<g filter="url(#filter2_f_1140_4854)">
<circle cx="153" cy="201" r="270" fill="white"/>
</g>
<g filter="url(#filter3_f_1140_4854)">
<circle cx="1382" cy="690" r="270" fill="#D1FFF1" fill-opacity="0.43"/>
</g>
</g>
<defs>
<filter id="filter0_f_1140_4854" x="-646" y="154" width="1340" height="1340" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_1140_4854"/>
</filter>
<filter id="filter1_f_1140_4854" x="361" y="-490" width="1490" height="1340" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_1140_4854"/>
</filter>
<filter id="filter2_f_1140_4854" x="-517" y="-469" width="1340" height="1340" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_1140_4854"/>
</filter>
<filter id="filter3_f_1140_4854" x="712" y="20" width="1340" height="1340" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_1140_4854"/>
</filter>
<linearGradient id="paint0_linear_1140_4854" x1="720" y1="0" x2="720" y2="900" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6F9FF" stop-opacity="0.3"/>
<stop offset="1" stop-color="#F6F9FF"/>
</linearGradient>
<clipPath id="clip0_1140_4854">
<rect width="1440" height="900" fill="white"/>
</clipPath>
</defs>
</svg>
