import { usePostHog } from 'posthog-js/react';
import { PostHogEventEnum } from '@/store/types';
import { useCallback } from 'react';

interface EventProperties {
  [key: string]: any;
}

export const usePostHogAnalytics = () => {
  const posthog = usePostHog();

  const captureEvent = (eventName: PostHogEventEnum, properties: EventProperties) => {
    if (posthog) posthog.capture(eventName, properties);
  };

  /**
   * Update the user properties of the PostHog user
   * If the user is identified, update its properties
   * If the user is anonymous, store the properties to send them when the user is identified
   */
  const updateUserProperties = useCallback(
    (properties: EventProperties) => {
      if (!posthog) return;

      // Set initial properties for anonymous and identified users
      posthog.setPersonProperties(properties);
    },
    [posthog]
  );

  /**
   * Extended identifyUser function that includes pending properties
   */
  const identifyUserWithPendingProperties = useCallback(
    (distinctId: string, properties: EventProperties) => {
      if (!posthog) return;

      // Identify the user with all properties
      posthog.identify(distinctId, properties);
    },
    [posthog]
  );

  return {
    captureEvent,
    identifyUser: identifyUserWithPendingProperties,
    updateUserProperties,
  };
};
