'use client';

import { useEffect, useContext } from 'react';
import { useTimer } from 'react-timer-hook';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { minutesToDo } from '@/config';
import UiContext from '@/store/UiContext';

const Timer = () => {
  const t = useTranslations('questions');
  const { time, updateTime } = useContext(UiContext);
  //totalSeconds: total number of seconds left in timer NOT converted to minutes, hours or days
  const { start, minutes, seconds, totalSeconds } = useTimer({
    expiryTimestamp: new Date(Date.now() + (minutesToDo * 60 - time) * 1000), //30 minutes now
    onExpire: () => console.warn('onExpire called'),
  });

  useEffect(() => {
    start();
  }, [start]);

  useEffect(() => {
    updateTime(minutesToDo * 60 - totalSeconds); //Full 30 minutes, left
  }, [totalSeconds, updateTime]);

  return (
    <div className='flex timer' style={{ alignItems: 'center' }}>
      <Image
        src={`/questions/clock.svg`}
        alt={t('alt.timer_clock')}
        width={24}
        height={24}
        style={{ height: 'fit-content' }}
        priority
      />
      <div className='text-primary' style={{ minWidth: 50, width: 50, textAlign: 'right', lineHeight: 'normal' }}>
        <span>{String(minutes).padStart(2, '0')}</span>:<span>{String(seconds).padStart(2, '0')}</span>
      </div>
    </div>
  );
};

export default Timer;
