<svg width="101" height="101" viewBox="0 0 101 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bdii_4076_3637)">
<rect x="20.3711" y="16.0156" width="64.9892" height="64.9892" rx="10.8315" transform="rotate(-10.716 20.3711 16.0156)" fill="white" fill-opacity="0.12"/>
<rect x="20.8814" y="16.3635" width="64.1157" height="64.1157" rx="10.3948" transform="rotate(-10.716 20.8814 16.3635)" stroke="url(#paint0_linear_4076_3637)" stroke-width="0.873488"/>
<g clip-path="url(#clip0_4076_3637)">
<path d="M55.858 42.9747C54.2833 41.9859 52.1315 42.6496 51.445 44.4963C50.9999 45.6966 51.4466 47.0518 52.4228 47.8783L55.3812 50.4926C55.9419 50.9872 55.995 51.8424 55.4991 52.4034C55.0045 52.9641 54.1493 53.0171 53.5884 52.5212L50.6299 49.9069C48.7893 48.3498 48.0784 45.7812 48.9064 43.5536C49.0186 43.2513 49.1617 42.9665 49.318 42.6931L47.8464 34.9198C47.5171 33.18 45.9655 31.8328 44.202 31.9862C42.1744 32.1634 40.7906 34.0387 41.1598 35.9887L43.8156 50.0175C44.353 52.856 46.0028 55.3626 48.3977 56.9801L51.2283 58.8898C51.8068 59.2529 52.4998 59.3835 53.1715 59.2563L63.1195 57.373C63.8537 57.234 64.3368 56.5253 64.1978 55.7911L63.2359 50.71C62.8191 48.5086 61.511 46.5766 59.6213 45.3719L55.8577 42.9734L55.858 42.9747ZM68.7885 27.3344C67.0908 27.8349 66.1393 29.6574 66.4687 31.3972L67.9402 39.1704C68.1857 39.3678 68.4214 39.5795 68.6379 39.8209C70.2215 41.5919 70.5001 44.2424 69.3559 46.3645L66.0765 51.1518L66.8601 55.2912C66.9488 55.7594 66.9371 56.2218 66.8672 56.6676L70.4106 55.9968C71.081 55.8699 71.6796 55.4948 72.0854 54.9454L74.0201 52.1377C75.6597 49.7578 76.2802 46.82 75.7423 43.9789L73.0859 29.9474C72.7168 27.9974 70.7446 26.7574 68.7912 27.3339L68.7885 27.3344ZM66.6193 41.6249C65.3053 40.157 63.0598 40.3258 61.9555 41.8217L61.0458 43.0714L61.0772 43.0916C62.865 44.2315 64.2528 45.8853 65.113 47.7947L66.944 45.1306C67.5506 44.0044 67.4708 42.5784 66.618 41.6252L66.6193 41.6249ZM63.0937 28.1038C63.6384 30.9809 60.5643 35.2664 58.9793 37.1192C58.4681 37.7161 57.6115 37.8783 56.9176 37.5095C54.7647 36.3629 50.3372 33.499 49.7925 30.622C49.3398 28.7112 50.5174 26.795 52.4253 26.3346C54.3695 26.0657 56.166 27.4189 56.4431 29.3629C55.9905 27.4522 57.1681 25.5359 59.076 25.0755C61.0201 24.8067 62.8167 26.1599 63.0937 28.1038Z" fill="#EDEDED"/>
</g>
</g>
<defs>
<filter id="filter0_bdii_4076_3637" x="-19.6289" y="-36.0684" width="155.939" height="155.939" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4076_3637"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.72714" dy="8.83102"/>
<feGaussianBlur stdDeviation="6.98791"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0174417 0 0 0 0 0.031694 0 0 0 0 0.37375 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_4076_3637" result="effect2_dropShadow_4076_3637"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4076_3637" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.25"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_4076_3637"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.25"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_4076_3637" result="effect4_innerShadow_4076_3637"/>
</filter>
<linearGradient id="paint0_linear_4076_3637" x1="52.8657" y1="16.0156" x2="52.8657" y2="81.0049" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.1"/>
</linearGradient>
<clipPath id="clip0_4076_3637">
<rect width="32.49" height="32.49" fill="white" transform="translate(39.7852 28.7256) rotate(-10.72)"/>
</clipPath>
</defs>
</svg>
