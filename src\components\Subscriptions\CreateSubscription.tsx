'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { httpsCallable } from 'firebase/functions';
import SubscribePaymentForm from '@/components/SubscribePaymentForm';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';
import { functions } from '@/utils/firebase';
import { PaymentProvider } from '@/store/types';

interface CreateSubscriptionProps {
  onProcessing: (loading: boolean) => void;
}

export default function CreateSubscription({ onProcessing }: CreateSubscriptionProps) {
  const t = useTranslations('members.subscription');
  const { user } = useSubscriptionPlan();
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  /**
   * Creates a new subscription for the user.
   *
   * @returns
   */
  async function createSubscription() {
    if (!user) return;

    onProcessing(true);

    try {
      const result: any = await httpsCallable(
        functions,
        user.activeSubscriptionType === PaymentProvider.SOLIDGATE ? 'createSubscriptionSolidgate' : 'createSubscription'
      )();

      if (!result.data.success) {
        setClientSecret(result.data.clientSecret);
        setShowPaymentForm(true);
      } else {
        window.location = '/user/subscription?success=true' as any;
      }
    } catch (error) {
      console.log('Error while creating subscription:', error);
    } finally {
      onProcessing(false);
    }
  }

  useEffect(() => {
    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    if (clientSecret) {
      setClientSecret(clientSecret);
      setShowPaymentForm(true);
    }
  }, []);

  if (!user) return null;

  return (
    <>
      {showPaymentForm && clientSecret ? (
        <SubscribePaymentForm clientSecret={clientSecret} />
      ) : (
        <>
          <span className="text-md text-gray-400 mb-2">{t('no_active_subscription')}</span>
          <button onClick={createSubscription} className="primary rounded-lg">
            {t('btn_subscribe')}
          </button>
        </>
      )}
    </>
  );
}
