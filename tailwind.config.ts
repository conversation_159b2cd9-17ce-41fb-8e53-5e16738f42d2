import { screens } from 'tailwindcss/defaultTheme';
import type { Config } from 'tailwindcss';
import { getSiteConfig } from './site.config.js';
const siteConfig = getSiteConfig();


const config: Config = {
  content: [
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/containers/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    /*screens:{
      xs: '360px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },*/
    /*screens: {
      xs: '360px',
      ...screens,
    },*/
    screens: {
      xxs: '360px',
      xs: '410px',
      popupMd: '594px',
      ...screens,
    },
    extend: {
      fontFamily: {
        raleway: ["Raleway", "sans-serif"],
        switzer: ["Switzer", "sans-serif"],
        ppmori: ["PPMori", "sans-serif"],
        inter: ["Inter", "sans-serif"],
        segoe: ["Segoe UI", 'system-ui', 'sans-serif'],
        kumbh: ["Kumbh Sans", "Sans-serif"],
      },
      screens: {
        sh: { raw: '(max-height: 570px)' },
        h: { raw: '(max-height: 820px)' },
        mdh: { raw: '(min-width: 768px) and  (max-height: 701px)' },
        lgh: { raw: '(min-width: 1024px) and  (min-height: 701px)' },
        xlgh: { raw: '(min-width: 1280px) and  (min-height: 701px)' },
        xlh: { raw: '(min-width: 1400px) and (min-height: 701px)' },
      },
      colors: {
        'primary': siteConfig.primaryColor,
        'default-black': '#191919',
        'grey-text': '#C1CFE9',
        'light-grey-text': 'rgba(193, 207, 233, 0.60)',
        'grey-bg': '#F6F9FF',
        'grey-96': '#F5F5F5',
        'grey-93': '#EAECF0',
        'grey-91':"#E5E7EB",
        'grey-84': '#D4D4D8',
        'grey-26': '#3F3F46',
        'grey-5':"#0D0D0E",
        'green': '#70C684'
      },
      inset: {
        '1/2': '50%'
      }
    },
  },
  plugins: [],
};
export default config;