import { useMemo } from 'react';
import { useTranslations } from 'next-intl';
import TestimonialCard from '@/components/Cards/CheckoutPage/TestimonialCard';

const TestimonialSection = () => {
  const t = useTranslations('checkout.testimonial_section');
  
  const testimonials = useMemo(
    () => [
      {
        title: t('testimonials.valuable_insights.title'),
        text: t('testimonials.valuable_insights.text'),
      },
      {
        title: t('testimonials.must_try.title'),
        text: t('testimonials.must_try.text'),
      },
      {
        title: t('testimonials.impressive_revelations.title'),
        text: t('testimonials.impressive_revelations.text'),
      },
      {
        title: t('testimonials.growth_booster.title'),
        text: t('testimonials.growth_booster.text'),
      },
      {
        title: t('testimonials.mind_unveiled.title'),
        text: t('testimonials.mind_unveiled.text'),
      },
      {
        title: t('testimonials.cognitive_mastery.title'),
        text: t('testimonials.cognitive_mastery.text'),
      },
    ],
    [t],
  );

  return (
    <div className='md:mt-10 lg:mt-0'>
      <h4 className='text-center md:text-left mb-4 md:mb-6'>{t('title')}</h4>
      <ul className='bg-grey-bg flex flex-wrap p-6 mx-4 md:mx-0'>
        {/**gap:5 */}
        {testimonials.map((testimonial, index) => (
          <TestimonialCard key={index} {...{ testimonial, index: index + 1 }} />
        ))}
      </ul>
    </div>
  );
};

export default TestimonialSection;
