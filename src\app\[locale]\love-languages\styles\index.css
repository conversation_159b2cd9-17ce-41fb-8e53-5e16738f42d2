@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Switzer font */
@font-face {
  font-family: "Switzer";
  src: url("/fonts/Switzer-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Switzer";
  src: url("/fonts/Switzer-Medium.otf") format("opentype");
  font-weight: 500; /* Medium weight */
  font-style: normal;
}

@font-face {
  font-family: "Switzer";
  src: url("/fonts/Switzer-SemiBold.otf") format("opentype");
  font-weight: 600; /* Semibold weight */
  font-style: normal;
}

@font-face {
  font-family: "Switzer";
  src: url("/fonts/Switzer-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

/* Raleway font */
@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Medium.ttf") format("truetype");
  font-weight: 500; /* Medium weight */
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-SemiBold.ttf") format("truetype");
  font-weight: 600; /* Semibold weight */
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Black.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

.swiper-pagination {
  position: relative !important;
  margin-top: 56px;
}

/* Apply margin-top: 25px for screens less than 768px */
@media (max-width: 768px) {
  .swiper-pagination {
    margin-top: 25px;
  }
}

.swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  background-color: #6dcaff !important;
}

.swiper {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.swiper-slide {
  display: flex !important;
  flex-direction: column;
  height: auto !important;
}