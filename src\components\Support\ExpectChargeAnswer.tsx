import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import SessionContext from '@/store/SessionContext';

const ExpectChargeAnswer = () => {
  const t = useTranslations('support_page.faq_details.expect-charge.answer');
  const { siteConfig } = useContext(SessionContext);
  
  return (
    <>
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph1', { siteName: siteConfig.siteName }) }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph2') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph3') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph4') }} 
      />
    </>
  );
};

export default ExpectChargeAnswer; 