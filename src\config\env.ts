/**
 * The current app environment, as set by NEXT_PUBLIC_ENV.
 * One of 'dev' | 'stage' | 'prod'.
 */
export const ENV = (process.env.NEXT_PUBLIC_ENV as 'dev' | 'stage' | 'prod') ?? 'dev';

const isEnv = (e: typeof ENV) => ENV === e;

/**
 * True when running in local development.
 */
export const IS_DEVELOPMENT = isEnv('dev');

/**
 * True when running in the staging environment.
 */
export const IS_STAGING = isEnv('stage');

/**
 * True when running in the live production environment.
 */
export const IS_PRODUCTION = isEnv('prod');
