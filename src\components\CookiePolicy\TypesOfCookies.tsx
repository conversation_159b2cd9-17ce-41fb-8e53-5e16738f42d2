'use client';

import { useTranslations } from 'next-intl';

const TypesOfCookies = () => {
  const t = useTranslations('cookie_policy');

  return (
    <div className="types-of-cookies-section">
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        {t('sections.types_of_cookies.title')}
      </h3>
      <div className='cky-audit-table-element'></div>
      &nbsp;
    </div>
  );
};

export default TypesOfCookies; 