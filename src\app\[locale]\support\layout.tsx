import { useTranslations } from 'next-intl';
import MobileMenu from '@/components/MobileMenu';
import { siteConfig } from '../../../../site.config';

export default function SupportLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const t = useTranslations('support_page');
  const siteName = siteConfig.siteName ?? '';

  return (
    <div className="w-full">
      <div className="w-full flex flex-col">
        <div className="w-full bg-[#EBF8FF]">
          <div className="max-w-[1120px] mx-auto py-[17.5px] md:py-[27px] text-[#0D0D0E] font-ppmori font-semibold text-[25.8px] md:text-[31.15px] leading-[48px] text-center md:text-left">
            {t('title', { siteName })}
          </div>
        </div>
        {children}
      </div>
      <MobileMenu />
    </div>
  );
}
