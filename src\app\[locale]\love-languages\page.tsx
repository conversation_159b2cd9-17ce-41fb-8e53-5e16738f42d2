'use client';

import Image from 'next/image';
import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n/routing';
import Logo from '@/components/LoveLanguages/Logo';
import IconWithDescription from '@/components/LoveLanguages/IconWithDescription';
import BoxWithIcon from '@/components/LoveLanguages/BoxWithIcon';
import LoginButton from '@/components/LoveLanguages/LoginButton';
import Footer from '@/components/LoveLanguages/GFooter';
import Account from '@/components/LoveLanguages/Account';
import { UserContext } from '@/store/UserContext';

const LandingPage = () => {
  const { user } = useContext(UserContext);
  const t = useTranslations('love-languages.landing');

  return (
    <div className="relative w-full overflow-x-hidden">
      <Image
        src="/images/love-languages/gradient-desktop.png"
        alt={t('alt_text.desktop_gradient')}
        width={1110}
        height={830}
        className="absolute left-[calc(50%-159px)] top-[0px] w-[1130px] h-auto hidden md:block z-10"
      />
      <div className="max-w-[1440px] mx-auto relative">
        <Image
          src="/images/love-languages/photos-desktop.png"
          alt={t('alt_text.desktop_photos')}
          width={630}
          height={649}
          className="absolute right-[78.33px] top-[145.15px] w-[630px] h-auto md:h-auto hidden md:block z-10"
        />
      </div>
      <header className="max-w-[1440px] mx-auto flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px] z-20">
        <Logo />
        {user ? <Account /> : <LoginButton />}
      </header>
      <main className="max-w-[1440px] mx-auto">
        <div className="hero-section relative w-full m-0 p-0 md:pb-[75px]">
          {/* <div className="relative z-0">
          <Image
            src="/images/love-languages/gradient-desktop.svg"
            alt={t('alt_text.desktop_gradient')}
            width={890}
            height={830}
            className="absolute right-[0px] top-[0px] w-[890px] h-auto md:h-auto hidden md:block z-0"
            priority
          />
          <Image
            src="/images/love-languages/photos-desktop.svg"
            alt={t('alt_text.desktop_photos')}
            width={620.27}
            height={638.12}
            className="absolute right-[78.33px] top-[145.15px] w-[620.27px] h-auto md:h-auto hidden md:block z-10"
          />
          <Image
            src="/images/love-languages/effects-desktop.svg"
            alt={t('alt_text.desktop_effects')}
            width={271.2}
            height={207}
            className="absolute right-[395px] top-[121px] w-[271.2] h-auto md:h-auto hidden md:block z-5"
          />
        </div>
        <div className="flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px]">
          <Logo />
          <LoginButton />
        </div> */}
          <div className="px-[16px] pt-[16px] md:pl-[82px] md:pt-[17px] md:max-w-[668px] relative z-20 mb-[24px] md:mb-[0px]">
            <div className="inline-flex bg-[#F2FAFF] px-[16px] py-[6px] rounded-[60px] mb-2">
              <label className="text-[#5DC4FF] font-raleway font-semibold text-[14px] leading-[20px] md:text-[16px] md:leading-[24px] uppercase">
                {t.rich('hero.title', {
                  count: 5,
                  span: (chunks) => <span className="font-sans">{chunks}</span>
                })}
              </label>
            </div>
            <label className="block text-[#0E2432] font-raleway font-bold tracking-[-0.05em] text-[36px] leading-[40px] md:mb-[28px] md:text-[64px] md:leading-[74px] mb-[16px] z-20">
              {t.rich('hero.subtitle', {
                br: (chunks) => <br />,
              })}
            </label>
            <label className="block text-[#828E98] font-raleway text-[16px] leading-[22px] md:text-[18px] md:leading-[27px] font-medium md:mr-[50px] mb-[24px]">
              {t.rich('hero.description', {
                strong: (chunks) => <span className="font-semibold text-[#0E2432]">{chunks}</span>
              })}
            </label>
            <div className="flex flex-col gap-[16px] mb-[40px]">
              <IconWithDescription source="/images/love-languages/heart.png" alt="Heart Icon">
                {t.rich('hero.benefits.transform_relationships', {
                  strong: (chunks) => <span className="font-semibold text-[#0E2432]">{chunks}</span>
                })}
              </IconWithDescription>
              <IconWithDescription source="/images/love-languages/connection.png" alt="Connection Icon">
                {t.rich('hero.benefits.strengthen_connections', {
                  strong: (chunks) => <span className="font-semibold text-[#0E2432]">{chunks}</span>
                })}
              </IconWithDescription>
              <IconWithDescription source="/images/love-languages/growth.png" alt="Growth Icon">
                {t.rich('hero.benefits.personal_growth', {
                  strong: (chunks) => <span className="font-semibold text-[#0E2432]">{chunks}</span>
                })}
              </IconWithDescription>
            </div>
            <div className="relative w-full z-20 flex items-center">
              <div className="absolute inset-x-0 -bottom-[5px] w-full md:w-[300px] h-[30%] rounded-full bg-[#37aef3] blur-[20px] opacity-100"></div>
              <Link
                href="/love-languages/test"
                className="font-switzer font-semibold shadow-[0px_1px_1px_rgba(35,_123,_255,_0.7)] w-full md:w-[300px] bg-[#5DC4FF] rounded-[10px] flex items-center justify-center px-[40px] py-[20px] tracking-[0.03em] text-white leading-[24px] text-[16px] md:text-[20px] relative z-10">
                <button>{t('btn_cta')}</button>
              </Link>
            </div>
          </div>
          <div className="relative flex items-center justify-center md:hidden top-[-54px]">
            <Image
              src="/images/love-languages/photos-mobile.png"
              alt={t('alt_text.mobile_photos')}
              width={329}
              height={342}
              className="absolute w-full h-auto pl-[23px] pr-[33px] z-10"
            />
            <Image
              src="/images/love-languages/gradient-mobile.png"
              alt={t('alt_text.mobile_gradient')}
              width={375}
              height={527}
              className="w-full h-auto z-0 mt-[-36px]"
            />
          </div>
        </div>
        <div className="evolution-section relative w-full m-0 p-0 px-[16px] md:py-[80px] md:px-[82px] mb-[60px] md:mb-[0px] mt-[-76px] md:mt-0">
          <div className="absolute inset-0 flex justify-center items-center z-10">
            <Image
              src="/images/love-languages/evolution-desktop.png"
              alt={t('alt_text.evolution_desktop')}
              width={944}
              height={873}
              className="hidden md:block mt-[172px] w-auto z-10"
              style={{ height: 'calc(100% - 60px)' }}
              priority
            />
          </div>

          <label className="font-raleway font-bold flex justify-center text-center text-[32px] leading-[36px] md:text-[56px] md:leading-[66px] tracking-[-0.03em] md:mb-[40px] mb-[26px] z-10 text-[#0E2432] z-20">
            {t.rich('evolution_section.title', {
              br: (chunks) => <br />,
            })} 
          </label>

          <div className="relative grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-5 z-20">
            <Image
              src="/images/love-languages/evolution-mobile-1.png"
              alt={t('alt_text.evolution_mobile_1')}
              width={228}
              height={428}
              className="opacity-90 block md:hidden absolute right-[-16px] z-10"
              style={{ top: 'calc(25 - 252px)' }}
            />
            <BoxWithIcon
              source="/images/love-languages/origin.svg"
              alt={t('alt_text.origin_icon')}
              heading={t('evolution_section.origins_section.heading')}
            >
              {t.rich('evolution_section.origins_section.description', {
                count: 5,
                year: 1980,
                span: (chunks) => <span className="font-sans">{chunks}</span>,
              })}
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/love-languages/limitation.svg"
              alt={t('alt_text.limitation_icon')}
              heading={t('evolution_section.limitations_section.heading')}
            >
              {t.rich('evolution_section.limitations_section.description', {
                count: 5,
                span: (chunks) => <span className="font-sans">{chunks}</span>,
              })}
            </BoxWithIcon>
            <Image
              src="/images/love-languages/evolution-mobile-2.png"
              alt={t('alt_text.evolution_mobile_2')}
              width={269}
              height={414}
              className="opacity-90 block md:hidden absolute left-[-16px] z-10"
              style={{ top: 'calc(75% - 208px)' }}
            />
            <BoxWithIcon 
              source="/images/love-languages/love.svg" 
              alt={t('alt_text.love_icon')} 
              heading={t('evolution_section.new_way_section.heading')}
            >
              {t.rich('evolution_section.new_way_section.description', {
                count: 7,
                span: (chunks) => <span className="font-sans">{chunks}</span>,
              })}
            </BoxWithIcon>
            <BoxWithIcon 
              source="/images/love-languages/spectrum.svg" 
              alt={t('alt_text.spectrum_icon')} 
              heading={t('evolution_section.spectrum_section.heading')}
            >
              {t.rich('evolution_section.spectrum_section.description', {
                count: 5,
                span: (chunks) => <span className="font-sans">{chunks}</span>,  
              })}
            </BoxWithIcon>
          </div>
        </div>
        <Footer />
      </main>
    </div>
  );
};

export default LandingPage;
