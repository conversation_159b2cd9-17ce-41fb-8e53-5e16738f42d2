'use client';

import type { FC } from 'react';
import { Link } from '@/lib/i18n/routing';
import { useTranslations } from 'next-intl';

interface LoginButtonProps {
  className?: string;
  style?: any;
}

const LoginButton: FC<LoginButtonProps> = ({ className, style }) => {
  const t = useTranslations('menu.buttons');
  return (
    <Link
      href={`/login`}
      className={`text-center inline-flex justify-center w-full sm:w-40 button secondary ${className} text-sm md:text-xl font-semibold ml-3`}
      style={{
        borderRadius: 10,
        lineHeight: '120%',
        ...style,
      }}
    >
      {t('btn_login')}
    </Link>
  );
};

export default LoginButton;
