'use client';

import { useContext } from 'react';
import { menuItems } from '@/app/config';
import Brand from '@/components/Brand';
import CtaButton from '@/components/Buttons/CtaButton';
import HamburgerButton from '@/components/Buttons/HamburgerButton';
import MenuItem from '@/components/Menu/MenuItem';
import MobileMenu from '@/components/MobileMenu';
import { usePathname } from '@/lib/i18n/routing';
import { UserContext } from '@/store/UserContext';

// Pages where header should be hidden
const HEADER_HIDDEN_PAGES: ReadonlyArray<string> = [
  '/form',
  '/form-v3',
  '/calculation',
  '/calculation-v3',
  '/checkout',
  '/mobile-checkout',
  '/offers',
  '/offers/2',
  '/offers/3',
  '/offers/4',
  '/questions',
] as const;

const Header = () => {
  const { user } = useContext(UserContext);
  const activePath = usePathname();
  const shouldShowHeader = !HEADER_HIDDEN_PAGES.includes(activePath);

  if (!shouldShowHeader) return null;

  // Filter menu items based on current path and user status
  const visibleMenuItems = menuItems.filter(item => {
    const isPricingOnResults = activePath === '/results' && item.id === 'pricing';
    const shouldShowForUser = user ? !item.hideIfLoggedIn : !item.memberOnly;
    return !isPricingOnResults && shouldShowForUser;
  });

  return (
    <div className="header relative z-50 w-full py-3 flex justify-center px-[5%] md:px-[5.69444%] bg-transparent">
      <div className="w-full flex gap-5 xxs:gap-0 items-center justify-between 2xl:max-w-[calc(1440px)] m-auto">
        {/* Logo/Brand */}
        <Brand {...{ classes: '!justify-start', position: 'title' }} />

        {/* Desktop Navigation */}
        <nav className="header-links mt-[15px] mb-[4px] hidden lg:flex gap-10 py-2 items-center">
          {visibleMenuItems.map(item => (
            <MenuItem key={item.id} item={item} />
          ))}
        </nav>

        {/* Right Side Actions */}
        <div className="flex">
          {/* CTA Button - Hidden on mobile */}
          <div className="md:block">
            <CtaButton {...{ type: 'secondary' }} />
          </div>

          {/* Mobile Menu Toggle */}
          <div className="flex lg:hidden order-2">
            <HamburgerButton />
          </div>

          {/* Mobile Menu Overlay */}
          <MobileMenu />
        </div>
      </div>
    </div>
  );
};

export default Header;
