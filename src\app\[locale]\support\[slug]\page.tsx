'use client';

import { useRouter } from '@/lib/i18n/routing';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  AutomaticRenewalsAnswer,
  UpdateEmailAnswer,
  CancelSubscriptionAnswer,
  LoseAccessToResultsAnswer,
  SubscriptionChargeAnswer,
  RequestRefundAnswer,
  ExpectChargeAnswer,
  ContactUsAnswer,
} from '@/components/Support';

type Question = {
  question: string;
  answer: JSX.Element;
};

export default function SupportFAQDetails() {
  const router = useRouter();
  const params = useParams();
  const t = useTranslations('support_page');
  const slug = params.slug as string;

  // Map of slug to component
  const faqData: { [key: string]: Question } = {
    'automatic-renewals': {
      question: t('faq_details.automatic-renewals.question'),
      answer: <AutomaticRenewalsAnswer />,
    },
    'update-email': {
      question: t('faq_details.update-email.question'),
      answer: <UpdateEmailAnswer />,
    },
    'cancel-subscription': {
      question: t('faq_details.cancel-subscription.question'),
      answer: <CancelSubscriptionAnswer />,
    },
    'lose-access-to-results': {
      question: t('faq_details.lose-access-to-results.question'),
      answer: <LoseAccessToResultsAnswer />,
    },
    'subscription-charge': {
      question: t('faq_details.subscription-charge.question'),
      answer: <SubscriptionChargeAnswer />,
    },
    'request-refund': {
      question: t('faq_details.request-refund.question'),
      answer: <RequestRefundAnswer />,
    },
    'expect-charge': {
      question: t('faq_details.expect-charge.question'),
      answer: <ExpectChargeAnswer />,
    },
    'contact-us': {
      question: t('faq_details.contact-us.question'),
      answer: <ContactUsAnswer />,
    },
  };

  const handleBackToList = () => {
    router.push('/support');
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  return (
    <div className="w-full max-w-[1120px] mx-auto flex flex-col md:mt-[30px]">
      <div className="truncate text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69] px-[33px] md:px-0 mt-[20px] md:mt-0">
        <span className="hover:underline cursor-pointer" onClick={handleBackToHome}>
          {t('breadcrumb.home')}
        </span>{' '}
        {'>'}{' '}
        <span className="hover:underline cursor-pointer" onClick={handleBackToList}>
          {t('breadcrumb.support')}
        </span>{' '}
        {'>'} <span className="text-primary">{faqData[slug].question}</span>
      </div>
      <div className="w-full max-w-[1120px] mt-[15px] flex flex-col gap-[18px] px-[33px] md:px-0 mb-[48px] md:mb-0">
        <h2 className="text-[20px] font-semibold leading-[27px] tracking-[0] text-[#0D0D0E]">
          {faqData[slug].question}
        </h2>
        {faqData[slug].answer}
      </div>
    </div>
  );
}
