"use client";

import { snakeCase } from 'lodash';
import { useContext, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

const PrivacyPolicy = () => {
  const { siteConfig } = useContext(SessionContext);
  const t = useTranslations('privacy_policy');
  
  const data = useMemo(
    () => [
      {
        title: t('sections.introduction.title'),
        text: [
          t('sections.introduction.paragraph1', {
            companyName: siteConfig.companyName || '',
            companyAddress: siteConfig.companyAddress || '',
            supportEmail: siteConfig.supportEmail || '',
            companyPhone: siteConfig.companyPhone || '',
            domain: siteConfig.domain || ''
          }),
          `${t('sections.introduction.paragraph2')}\n\n${t('last_updated')}\n${t('effective_date')}`,
        ],
      },
      {
        title: t('sections.consent.title'),
        text: [t('sections.consent.paragraph1')],
      },
      {
        title: t('sections.information_collect.title'),
        text: [
          t('sections.information_collect.paragraph1'),
          t('sections.information_collect.paragraph2'),
        ],
      },
      {
        title: t('sections.how_we_use.title'),
        text: [
          t('sections.how_we_use.paragraph1'),
          t.raw('sections.how_we_use.purposes'),
        ],
      },
      {
        title: t('sections.log_files.title'),
        text: [
          t('sections.log_files.paragraph1', {
            siteName: siteConfig.siteName || ''
          }),
        ],
      },
      {
        title: t('sections.cookies.title'),
        text: [
          t('sections.cookies.paragraph1', {
            siteName: siteConfig.siteName || ''
          }),
          t('sections.cookies.paragraph2'),
        ],
      },
      // {
      //   title: t('sections.advertising_partners.title'),
      //   text: [
      //     t('sections.advertising_partners.paragraph1', {
      //       siteName: siteConfig.siteName || ''
      //     }),
      //     t('sections.advertising_partners.paragraph2', {
      //       siteName: siteConfig.siteName || ''
      //     }),
      //     t('sections.advertising_partners.paragraph3', {
      //       siteName: siteConfig.siteName || ''
      //     }),
      //   ],
      // },
      {
        title: t('sections.third_party.title'),
        text: [
          t('sections.third_party.paragraph1'),
          t('sections.third_party.paragraph2'),
          t('sections.third_party.paragraph3'),
        ],
      },
      {
        title: t('sections.ccpa_rights.title'),
        text: [
          t('sections.ccpa_rights.paragraph1'),
          t('sections.ccpa_rights.paragraph2'),
          t('sections.ccpa_rights.paragraph3'),
          t('sections.ccpa_rights.paragraph4'),
          t('sections.ccpa_rights.paragraph5'),
        ],
      },
      {
        title: t('sections.gdpr_rights.title'),
        text: [
          t('sections.gdpr_rights.paragraph1', {
            supportEmail: siteConfig.supportEmail || ''
          }),
          t('sections.gdpr_rights.paragraph2', {
            supportEmail: siteConfig.supportEmail || ''
          }),
          t('sections.gdpr_rights.paragraph3'),
        ],
      },
      {
        title: t('sections.retention.title'),
        text: [
          t('sections.retention.paragraph1'),
        ],
      },
      {
        title: t('sections.security.title'),
        text: [
          t('sections.security.paragraph1'),
        ],
      },
      {
        title: t('sections.grievance.title'),
        text: [
          t('sections.grievance.paragraph1', {
            companyName: siteConfig.companyName || '',
            companyAddress: siteConfig.companyAddress || '',
            supportEmail: siteConfig.supportEmail || ''
          }),
        ],
      },
      {
        title: t('sections.children.title'),
        text: [
          t('sections.children.paragraph1'),
          t('sections.children.paragraph2', {
            siteName: siteConfig.siteName || ''
          }),
        ],
      },
    ],
    [siteConfig, t]
  );

  return (
    <div>
      <div className="w-full h-[100px] sm:h-[130px]"></div>
      <section id="privacy" className="flex flex-wrap justify-between max-w-[1440px] m-auto mt-5 pt-0">
        <div className="flex flex-wrap flex-col max-w-[416px] w-full lg:w-[40%]">
          <div>
            <h1 className="mb-10 text-5xl" style={{ maxWidth: 353 }}>
              {t('title')}
            </h1>
          </div>
          <ol style={{ color: '#191919', listStyleType: 'decimal', paddingLeft: 20 }}>
            {data.map((item, i) => (
              <a key={i} href={`#${snakeCase(item.title)}`}>
                <li style={{ paddingLeft: 6, marginBottom: 12 }}>{item.title}</li>
              </a>
            ))}
          </ol>
        </div>
        <div className="max-w-[736px] w-full lg:w-[60%]">
          {data.map((item, index) => (
            <div key={index}>
              <h3
                className="big"
                id={snakeCase(item.title)}
                style={{ marginBottom: 24, marginTop: index > 0 ? 48 : 0, scrollMarginTop: 150 }}>
                {item.title}
              </h3>
              {item.text.map((paragraph, i) =>
                paragraph.constructor === Array ? (
                  <div key={i}>
                    <ul style={{ listStyleType: 'disc', paddingLeft: 40 }}>
                      {paragraph.map((li, i) => (
                        <li key={i}>{li}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p key={i} style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
                    {paragraph}
                    {item.title.includes('Cookies') && i === 1 && (
                      <span>
                        <Link className='hover:underline text-primary mb-4' target='_blank' href='/cookies'>
                          {t('sections.cookies.cookie_policy_link')}
                        </Link>
                        {'.'}
                      </span>
                    )}
                  </p>
                )
              )}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default PrivacyPolicy;
