'use client';

import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

const QuestionSteps = ({ questionId }: { questionId: number }) => {
  const { updateQuestionId, answers } = useContext(SessionContext);

  const steps: number[] = [];
  for (let i = 1; i <= 30; i++) {
    steps.push(i);
  }

  return (
    <div className='steps-container'>
      {steps.map((step) => {
        const answered = answers.filter((ans) => +ans.questionId === step).length > 0;
        const passedBy = +step < +questionId;
        return (
          <div
            key={step}
            className={`step-${step}`}
            onClick={() => {
              updateQuestionId(+step);
            }}
          >
            <div
              className={`dot${questionId === step ? ' active' : ''}`}
              style={{
                ...(passedBy && !answered ? { opacity: '1', cursor: 'pointer' } : { opacity: '0' }),
              }}
            ></div>
            <div className='text' style={{ ...(step === questionId && { color: '#191919' }) }}>
              {(step === questionId && questionId) || (step === 1 && 1) || (step === 30 && 30)}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default QuestionSteps;
