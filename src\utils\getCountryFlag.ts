/**
 * Utility function to get country flag URL from flagcdn.com
 * @param countryId - The country ID (e.g., 'us', 'gb', 'fr', 'us-ca')
 * @returns The URL for the country flag image
 */
export const getCountryFlag = (countryId: string): string => {
  // Ensure countryId is lowercase and valid
  const normalizedId = countryId.toLowerCase().trim();
  
  // If it's a US state (e.g., us-ca), return US flag
  if (normalizedId.startsWith('us-')) {
    return `https://flagcdn.com/w80/us.png`;
  }

  return `https://flagcdn.com/w80/${normalizedId}.png`;
};

/**
 * Utility function to get country flag URL with custom size
 * @param countryId - The country ID (e.g., 'us', 'gb', 'fr', 'us-ca')
 * @param size - The size of the flag (e.g., 'w20', 'w40', 'w80', 'w160')
 * @returns The URL for the country flag image with specified size
 */
export const getCountryFlagWithSize = (countryId: string, size: 'w20' | 'w40' | 'w80' | 'w160' = 'w80'): string => {
  const normalizedId = countryId.toLowerCase().trim();
  
  // If it's a US state (e.g., us-ca), return US flag
  if (normalizedId.startsWith('us-')) {
    return `https://flagcdn.com/${size}/us.png`;
  }

  return `https://flagcdn.com/${size}/${normalizedId}.png`;
};