@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Inter font */
@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter_24pt-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter_24pt-Medium.ttf") format("truetype");
  font-weight: 500; /* Medium weight */
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter_24pt-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter_24pt-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter_24pt-Black.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: "PPMori";
  src: url("/fonts/PPMori-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "PPMori";
  src: url("/fonts/PPMori-SemiBold.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
}

.swiper-pagination {
  position: relative !important;
  margin-top: 56px;
}

/* Apply margin-top: 25px for screens less than 768px */
@media (max-width: 768px) {
  .swiper-pagination {
    margin-top: 25px;
  }
}

.swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  background-color: #8C36D0 !important;
}

.swiper {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.swiper-slide {
  display: flex !important;
  flex-direction: column;
  height: auto !important;
}