<svg width="102" height="101" viewBox="0 0 102 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_4076_4446)">
<rect width="64.9892" height="64.9892" rx="10.8315" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 96.7441 15.7793)" fill="white"/>
<rect x="-0.510336" y="0.347919" width="64.1157" height="64.1157" rx="10.3948" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 95.7971 15.6905)" stroke="#F0EBF6" stroke-width="0.873488"/>
<g clip-path="url(#clip0_4076_4446)">
<path d="M53.8089 24.1925C49.4075 23.3593 45.1505 26.261 44.3173 30.6624C43.484 35.0638 46.3858 39.3208 50.7871 40.154C55.1885 40.9872 59.4455 38.0855 60.2788 33.6841C61.112 29.2827 58.2103 25.0257 53.8089 24.1925ZM55.2677 36.6897C54.9516 35.1212 53.7248 33.8212 52.0568 33.5054L52.0356 33.5014C50.3676 33.1856 48.7484 33.9441 47.8821 35.2888C47.0665 34.1313 46.6953 32.664 46.9788 31.1663C47.5343 28.232 50.3723 26.2975 53.3066 26.853C56.2408 27.4085 58.1753 30.2465 57.6198 33.1807C57.336 34.6798 56.4526 35.911 55.2691 36.69L55.2677 36.6897ZM54.6709 30.5558C54.4624 31.6571 53.3994 32.3817 52.298 32.1732C51.1967 31.9648 50.472 30.9017 50.6805 29.8003C50.889 28.699 51.9521 27.9744 53.0534 28.1829C54.1548 28.3914 54.8794 29.4544 54.6709 30.5558ZM66.7486 43.1757C62.3473 42.3425 58.0903 45.2442 57.257 49.6456C56.4238 54.047 59.3255 58.304 63.7269 59.1372C68.1283 59.9705 72.3853 57.0687 73.2185 52.6673C74.0518 48.266 71.15 44.009 66.7486 43.1757ZM68.2075 55.673C67.8913 54.1044 66.6646 52.8044 64.9966 52.4886L64.9753 52.4846C63.3073 52.1688 61.6881 52.9273 60.8218 54.272C60.0062 53.1145 59.6351 51.6472 59.9186 50.1495C60.4741 47.2152 63.3121 45.2807 66.2463 45.8362C69.1806 46.3917 71.1151 49.2297 70.5596 52.164C70.2758 53.663 69.3924 54.8942 68.2088 55.6732L68.2075 55.673ZM67.6107 49.539C67.4022 50.6403 66.3391 51.365 65.2378 51.1565C64.1364 50.948 63.4118 49.8849 63.6203 48.7836C63.8288 47.6822 64.8919 46.9576 65.9932 47.1661C67.0945 47.3746 67.8192 48.4377 67.6107 49.539ZM63.4426 31.5275C63.5816 30.7933 64.289 30.3099 65.0246 30.4492L67.6848 30.9528C70.6191 31.5083 72.5536 34.3463 71.9981 37.2805L71.4944 39.9408C71.3554 40.675 70.6481 41.1583 69.9125 41.0191C69.1769 40.8798 68.6952 40.1714 68.8342 39.4372L69.3378 36.7769C69.6156 35.3098 68.6483 33.8908 67.1812 33.613L64.5209 33.1094C63.7854 32.9702 63.3036 32.2617 63.4426 31.5275ZM54.0932 51.8022C53.9542 52.5365 53.2468 53.0198 52.5112 52.8805L49.851 52.3769C46.9167 51.8214 44.9822 48.9834 45.5377 46.0492L46.0413 43.3889C46.1803 42.6547 46.8877 42.1714 47.6233 42.3106C48.3588 42.4499 48.8406 43.1583 48.7016 43.8926L48.198 46.5528C47.9202 48.0199 48.8875 49.4389 50.3546 49.7167L53.0148 50.2203C53.7504 50.3595 54.2322 51.068 54.0932 51.8022Z" fill="url(#paint0_linear_4076_4446)"/>
</g>
</g>
<defs>
<filter id="filter0_dii_4076_4446" x="-0.898265" y="-1.44948" width="103.891" height="103.891" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.72714" dy="8.83102"/>
<feGaussianBlur stdDeviation="6.98791"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0174417 0 0 0 0 0.031694 0 0 0 0 0.37375 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4076_4446"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4076_4446" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_4076_4446"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_4076_4446" result="effect3_innerShadow_4076_4446"/>
</filter>
<linearGradient id="paint0_linear_4076_4446" x1="76.9436" y1="32.9457" x2="43.5203" y2="42.9404" gradientUnits="userSpaceOnUse">
<stop stop-color="#DFE33B"/>
<stop offset="1" stop-color="#E6E965"/>
</linearGradient>
<clipPath id="clip0_4076_4446">
<rect width="32.49" height="32.49" fill="white" transform="translate(45.8281 22.6816) rotate(10.72)"/>
</clipPath>
</defs>
</svg>
