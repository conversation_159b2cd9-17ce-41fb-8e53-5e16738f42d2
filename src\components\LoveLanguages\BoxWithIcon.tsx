import Image from "next/image";
import React, { ReactNode } from "react";

type BoxWithIcon = {
  source: string;
  alt: string;
  heading: string;
  children?: ReactNode;
};

const BoxWithIcon: React.FC<BoxWithIcon> = ({
  source,
  alt,
  heading,
  children,
}) => {
  return (
    <div className="box-content p-5 md:p-6 bg-white border-l-4 border-[#5DC4FF] shadow-[0px_4px_12px_rgba(0,_80,_105,_0.08)] rounded-[12px] z-20">
      <Image
        src={source}
        alt={alt}
        width={56}
        height={56}
        className="w-[48px] h-[48px] md:w-[56px] md:h-[56px] mb-[12px] md:mb-[20px]"
      />
      <label className="block font-raleway font-bold text-[#0E2432] text-[22px] md:text-[26px] leading-[26px] md:leading-[30px] mb-[12px]">
        {heading}
      </label>
      <label className="block font-raleway font-medium text-[#828E98] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px]">
        {children}
      </label>
    </div>
  );
};

export default BoxWithIcon;
