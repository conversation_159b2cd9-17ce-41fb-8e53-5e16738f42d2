/* ------------------------------------------------------------------
 * Global Edge middleware
 * -----------------------------------------------------------------*/
import { NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from '@/lib/i18n/routing';
import { defaultCountry, defaultLocale, Locale, isMultiLanguageEnabled, isValidLocale } from '@/lib/i18n/locales';

// Create the next-intl handler
const handleI18nRouting = createMiddleware(routing);

/* ------------------------------------------------------------------
* Only run for "real" pages – skip static assets etc.
* -----------------------------------------------------------------*/
export const config = {
  // Match all pages except API routes, static files, and Next.js internals
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)', '/']
};

/**
 * Extract locale from URL pathname
 */
const extractLocaleFromPath = (pathname: string): Locale | null => {
  const firstSegment = pathname.split('/')[1];
  return isValidLocale(firstSegment) ? firstSegment : null;
};

/**
 * Detect locale from browser's Accept-Language header
 * Falls back to English
 */
const detectLocaleFromBrowser = (acceptLanguage: string | null): Locale => {
  if (!acceptLanguage) return defaultLocale;

  // Get the first preferred language code
  const primaryLanguage = acceptLanguage
    .split(',')[0]
    .split(';')[0]
    .toLowerCase()
    .trim();

  // Check for exact match
  if (isValidLocale(primaryLanguage)) {
    return primaryLanguage;
  }

  // Check for language family match (e.g., 'en-US' matches 'en')
  const languageFamily = primaryLanguage.split('-')[0];
  return Object.values(Locale).find(locale => 
    locale.toLowerCase() === languageFamily
  ) || defaultLocale;
};

/**
 * Multi-language detection with priority:
 * 1. URL path
 * 2. Cookie preference
 * 3. Browser language
 * 4. English fallback
 */
const detectLanguage = (req: NextRequest): Locale => {
  if (!isMultiLanguageEnabled) return defaultLocale;

  // Check URL path first (highest priority)
  const pathLocale = extractLocaleFromPath(req.nextUrl.pathname);
  if (pathLocale) return pathLocale;

  // Check cookie preference
  const cookieLocale = req.cookies.get('preferred-language')?.value;
  if (cookieLocale && isValidLocale(cookieLocale)) {
    return cookieLocale;
  }

  // Check browser language
  return detectLocaleFromBrowser(req.headers.get('accept-language'));
};

/**
 * Middleware to handle locale detection and request enhancement
 */
export function middleware(req: NextRequest): NextResponse {
  const ip = req.ip ?? '';
  const country = req.geo?.country ?? defaultCountry;
  const region = req.geo?.region ?? '';

  const detectedLanguage = detectLanguage(req);
  
  // Create headers once
  const headers = new Headers(req.headers);
  headers.set('x-ip', ip);
  headers.set('x-locale', detectedLanguage);

  // Create response with enhanced headers
  const response = handleI18nRouting(new NextRequest(req.nextUrl, { headers }));

  // Set cookies
  response.cookies.set('locale', country);
  response.cookies.set('region', region);
  response.cookies.set('preferred-language', detectedLanguage);

  // Set response headers
  response.headers.set('x-ip', ip);
  response.headers.set('x-locale', detectedLanguage);

  return response;
}