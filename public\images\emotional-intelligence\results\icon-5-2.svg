<svg width="102" height="101" viewBox="0 0 102 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_4076_4709)">
<rect width="64.9892" height="64.9892" rx="10.8315" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 96.7441 15.7793)" fill="white"/>
<rect x="-0.510336" y="0.347919" width="64.1157" height="64.1157" rx="10.3948" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 95.7971 15.6905)" stroke="#F0EBF6" stroke-width="0.873488"/>
<g clip-path="url(#clip0_4076_4709)">
<path d="M61.6791 42.9747C63.2538 41.9859 65.4056 42.6496 66.0921 44.4963C66.5373 45.6966 66.0906 47.0518 65.1143 47.8783L62.1559 50.4926C61.5952 50.9872 61.5421 51.8424 62.038 52.4034C62.5326 52.9641 63.3878 53.0171 63.9487 52.5212L66.9072 49.9069C68.7478 48.3498 69.4587 45.7812 68.6307 43.5536C68.5185 43.2513 68.3754 42.9665 68.2191 42.6931L69.6907 34.9198C70.0201 33.18 71.5716 31.8328 73.3351 31.9862C75.3627 32.1634 76.7465 34.0387 76.3773 35.9887L73.7215 50.0175C73.1841 52.856 71.5343 55.3626 69.1394 56.9801L66.3088 58.8898C65.7303 59.2529 65.0374 59.3835 64.3656 59.2563L54.4176 57.373C53.6834 57.234 53.2003 56.5253 53.3393 55.7911L54.3012 50.71C54.718 48.5086 56.0262 46.5766 57.9158 45.3719L61.6794 42.9734L61.6791 42.9747ZM48.7486 27.3344C50.4463 27.8349 51.3978 29.6574 51.0684 31.3972L49.5969 39.1704C49.3514 39.3678 49.1157 39.5795 48.8992 39.8209C47.3156 41.5919 47.037 44.2424 48.1812 46.3645L51.4606 51.1518L50.677 55.2912C50.5884 55.7594 50.6 56.2218 50.6699 56.6676L47.1265 55.9968C46.4561 55.8699 45.8575 55.4948 45.4517 54.9454L43.517 52.1377C41.8774 49.7578 41.257 46.82 41.7948 43.9789L44.4512 29.9474C44.8203 27.9974 46.7925 26.7574 48.7459 27.3339L48.7486 27.3344ZM50.9178 41.6249C52.2318 40.157 54.4774 40.3258 55.5816 41.8217L56.4913 43.0714L56.46 43.0916C54.6721 44.2315 53.2843 45.8853 52.4241 47.7947L50.5931 45.1306C49.9865 44.0044 50.0663 42.5784 50.9191 41.6252L50.9178 41.6249ZM54.4434 28.1038C53.8987 30.9809 56.9728 35.2664 58.5578 37.1192C59.069 37.7161 59.9256 37.8783 60.6195 37.5095C62.7724 36.3629 67.1999 33.499 67.7446 30.622C68.1973 28.7112 67.0197 26.795 65.1118 26.3346C63.1676 26.0657 61.3711 27.4189 61.094 29.3629C61.5467 27.4522 60.369 25.5359 58.4612 25.0755C56.517 24.8067 54.7205 26.1599 54.4434 28.1038Z" fill="url(#paint0_linear_4076_4709)"/>
</g>
</g>
<defs>
<filter id="filter0_dii_4076_4709" x="-0.898265" y="-1.44948" width="103.891" height="103.891" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.72714" dy="8.83102"/>
<feGaussianBlur stdDeviation="6.98791"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0174417 0 0 0 0 0.031694 0 0 0 0 0.37375 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4076_4709"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4076_4709" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_4076_4709"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_4076_4709" result="effect3_innerShadow_4076_4709"/>
</filter>
<linearGradient id="paint0_linear_4076_4709" x1="40.9985" y1="39.2043" x2="87.9949" y2="46.2613" gradientUnits="userSpaceOnUse">
<stop stop-color="#A1F679"/>
<stop offset="0.723662" stop-color="#5ED036"/>
</linearGradient>
<clipPath id="clip0_4076_4709">
<rect width="32.49" height="32.49" fill="white" transform="matrix(-0.982548 -0.18601 -0.18601 0.982548 77.752 28.7256)"/>
</clipPath>
</defs>
</svg>
