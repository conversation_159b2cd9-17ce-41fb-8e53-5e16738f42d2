'use client';

import { useEffect, useState, useContext } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { StripeElementsOptions } from '@stripe/stripe-js';
import CheckoutFormStripe from '@/components/PaymentProviders/CheckoutForms/Stripe';
import { PaymentTextWrapper } from '@/components/PaymentProviders/PaymentText';
import { createStripeOptions, Elements, stripe } from '@/lib/stripe/client';
import useStripeMerchant from '@/hooks/useStripeMerchant';
import { useTrackGTMEvents } from '@/hooks/useTrackGTMEvents';
import SessionContext from '@/store/SessionContext';
import type { PaymentContextProps } from '@/types/payment';

export default function PaymentStripe({ context }: PaymentContextProps) {
  const { clickId, isTrial, isOneTime, time } = context;
  const locale = useLocale();
  const t = useTranslations('checkout.payment_details');
  const { prices, plan } = useContext(SessionContext);
  const { getSubscriptionMerchantData, getOnetimeMerchantData } = useStripeMerchant();
  const { trackGTMBeingCheckoutEvent } = useTrackGTMEvents();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const stripeOptions: StripeElementsOptions = createStripeOptions(clientSecret!, locale);

  useEffect(() => {
    /**
     * Retrieves and sets the Stripe client secret based on the payment type,
     * and triggers the GTM checkout tracking event.
     */
    const getStripeMerchant = async (): Promise<void> => {
      const merchantData = isOneTime
        ? await getOnetimeMerchantData({ clickId, time, isOneTime }, 30)
        : await getSubscriptionMerchantData({ clickId, time, isTrial, isOneTime });

      if (!merchantData) return;

      setClientSecret(merchantData);
      trackGTMBeingCheckoutEvent(prices);
    };

    getStripeMerchant();
  }, [clickId, time, isTrial, isOneTime]);

  return (
    clientSecret && (
      <div id="paymentDetails" className="w-full flex justify-center">
        <div className="flex flex-col w-full">
          <h4 className="text-center md:text-left mb-4 md:mb-6">{t('title')}</h4>
          <div className="w-full px-5 md:px-0">
            <Elements options={stripeOptions} stripe={stripe}>
              <CheckoutFormStripe context={{ clickId, isTrial, isOneTime, time }} />
            </Elements>

            <PaymentTextWrapper isTrial={isTrial ?? false} isOneTime={isOneTime ?? false} prices={prices} plan={plan} />
          </div>
        </div>
      </div>
    )
  );
}
