'use client';

import { memo, useContext } from 'react';
import { useTranslations } from 'next-intl';
import SessionContext from '@/store/SessionContext';

const Copyright = () => {
  const { siteConfig } = useContext(SessionContext);
  const siteName = siteConfig.siteName ?? '';
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footer.copyright');

  return (
    <span className="tracking-[0] leading-[24px] text-[16px] font-normal text-[#000000] font-segoe">
      {t('text', { currentYear, siteName })}
    </span>
  );
};

export default memo(Copyright);
