'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

interface ProductCardProps {
  id: number;
  isSelected: boolean;
  productKey: string;
  formatted: string;
  original: string;
  toggleSelect: (id: number) => void;
}

function ProductCard({ id, isSelected, productKey, formatted, original, toggleSelect }: ProductCardProps) {
  const t = useTranslations('offers_flow.three');

  return (
    <div
      key={id}
      onClick={() => toggleSelect(id)}
      className={`w-full cursor-pointer flex border flex-col p-4 rounded-[12px] shadow-[2px_8px_14px_0px_#6881B114,_1px_1px_1px_1px_#8DA0BC14] ${
        isSelected ? 'border-[#FF9500]' : 'border-[#C1CFE9]/45'
      }`}>
      <div className="flex flex-row justify-between">
        <span className="w-fit rounded-[8px] bg-[#4BCA7D] text-white font-ppmori font-semibold text-[16px] leading-[20px] p-[10px]">
          {t(`products.${productKey}.save_percent`)}
        </span>
        <div
          className={`flex items-center w-[30px] h-[30px] border rounded-full ${
            isSelected ? 'border-[8px] border-[#FF9500]' : 'border-[#8F949F]/30'
          }`}></div>
      </div>
      <span className="md:mt-[18px] mt-[10px] font-ppmori font-semibold md:text-[20px] md:leading-[26px] text-[#191919]">
        {t(`products.${productKey}.title1`)} <br className="md:block hidden" /> {t(`products.${productKey}.title2`)}
      </span>
      <span className="mt-[10px] font-normal text-[16px] leading-[20px] text-[#8893AC]">
        {t(`products.${productKey}.description`)}
      </span>
      <div className="md:mt-[20px] mt-[10px] flex flex-row gap-[6px] font-ppmori font-semibold">
        <span className="text-[30px] leading-[34px] text-[#191919]">{formatted}</span>
        <span className="text-[18px] leading-[20px] text-[#8893AC] flex items-center line-through">{original}</span>
      </div>
    </div>
  );
}

export default ProductCard;
