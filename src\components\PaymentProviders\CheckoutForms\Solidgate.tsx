'use client';

import { useContext, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Payment, { ClientSdkInstance, FailMessage } from '@solidgate/react-sdk';

import CompliantTrialText from '@/components/PaymentProviders/CheckoutForms/CompliantTrialText';
import PriceTotalText from '@/components/PaymentProviders/CheckoutForms/PriceTotalText';
import useSolidgateMerchant from '@/hooks/useSolidgateMerchant';
import { useTrackGTMEvents } from '@/hooks/useTrackGTMEvents';
import { usePostHogEvents } from '@/hooks/usePostHogEvents';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';
import { PaymentContextProps } from '@/types/payment';
import { getClickIdFromStorage } from '@/utils/getClickIdFromStorage';
import { COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';

import '@/sass/form.scss';
import '@/sass/spinner.scss';

// Constants moved outside component for better performance
const FORM_PARAMS = { allowSubmit: false } as const;

export default function CheckoutFormSolidgate({ context }: PaymentContextProps) {
  const { isTrial, isOneTime, time } = context;
  const t = useTranslations('checkout.payment_details.form');
  const appleContainerRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const hasFiredGTM = useRef(false);
  const { trackPostHogPaymentFailedEvent, trackPostHogCheckoutConfirmedEvent, trackPostHogPurchaseEvent } =
    usePostHogEvents();
  const { trackGTMPurchaseEvent } = useTrackGTMEvents();
  const { getSubscriptionMerchantData } = useSolidgateMerchant();
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [form, setForm] = useState<ClientSdkInstance | null>(null);
  const { checkoutId, prices, merchantData } = useContext(SessionContext);
  const { checkoutVersion, paymentSystem, plan } = useContext(SessionContext);
  const compliantVersion = useMemo(() => time < COMPLIANCE_TIME_THRESHOLD, [time]);
  const clickId = useMemo(() => getClickIdFromStorage(), []);

  const handleOnReadyPaymentInstance = (form: ClientSdkInstance) => {
    setForm(form);
  };

  /**
   * Handles the payment success event.
   */
  const onPaymentSuccess = () => {
    // Track the purchase event in Google Tag Manager (GTM) analytics
    trackGTMPurchaseEvent(prices, checkoutId, hasFiredGTM);

    // Track the purchase event in PostHog analytics
    trackPostHogPurchaseEvent({
      paymentSystem: PaymentProvider.SOLIDGATE,
      checkoutVersion,
      testType: 'iq',
      pageType: 'IQ',
      ...(typeof merchantData === 'object' ? merchantData : {}),
    });
    router.push('/results?init=1');
  };

  /**
   * Handles the payment failure event.
   */
  const onPaymentFail = (e: FailMessage) => {
    trackPostHogPaymentFailedEvent({
      paymentSystem: PaymentProvider.SOLIDGATE,
      checkoutVersion,
      testType: 'iq',
      pageType: 'IQ',
      reason: e.message,
    });
    setMessage('Something went wrong.');
    document.getElementById('paymentDetails')!.scrollIntoView();
  };

  /**
   * Handles the submit event.
   */
  const onSubmit = () => {
    setIsLoading(true);

    // Track the checkout confirmed event in PostHog analytics
    trackPostHogCheckoutConfirmedEvent({
      paymentSystem: PaymentProvider.SOLIDGATE,
      checkoutVersion,
      testType: 'iq',
      pageType: 'IQ',
    });

    form?.submit();

    setIsLoading(false);
  };

  /**
   * Spinner component for loading indication.
   */
  const Spinner = () => <div className="spinner" id="spinner"></div>;

  /**
   * Renders a retry payment button.
   *
   * When clicked, it fetches the subscription merchant data and resets the message state.
   *
   * @returns The retry <button /> element.
   */
  const RetryButton = () => (
    <button
      type="button"
      onClick={async () => {
        await getSubscriptionMerchantData({ clickId, time, isTrial });
        setMessage(null);
      }}
      className="flex button danger text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px]">
      <span>{t('retry_payment')}</span>
    </button>
  );

  /**
   * Renders the checkout submit button.
   *
   * Disables itself while loading.
   * Shows a spinner when loading, or localized button text for trial vs. one-time.
   *
   * @returns The submit <button /> element.
   */
  const SubmitButton = () => (
    <button
      id="submit"
      type="button"
      disabled={isLoading}
      className="flex w-full button primary text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px] leading"
      onClick={onSubmit}>
      <span id="button-text">
        {isLoading ? <Spinner /> : compliantVersion && isTrial ? t('start_trial_button') : t('get_results_button')}
      </span>
    </button>
  );

  return (
    merchantData && (
      <div id="paymentDetails" className="flex justify-center">
        <div className="flex flex-col grow">
          <div className="mx-5">
            {message && (
              <>
                <div className="mb-5 mt-2 text-red-500 font-bold text-lg">{message}</div>
                <RetryButton />
              </>
            )}

            <PriceTotalText isTrial={isTrial} isOneTime={isOneTime} prices={prices} plan={plan} />

            <CompliantTrialText compliantVersion={compliantVersion} isTrial={isTrial} prices={prices} plan={plan} />

            {typeof merchantData === 'object' && merchantData && (
              <>
                <div ref={appleContainerRef} />

                <Payment
                  merchantData={merchantData}
                  onReadyPaymentInstance={handleOnReadyPaymentInstance}
                  applePayContainerRef={appleContainerRef}
                  onSuccess={onPaymentSuccess}
                  onFail={e => onPaymentFail(e)}
                  formParams={FORM_PARAMS}
                  width="100%"
                />
              </>
            )}

            <SubmitButton />
          </div>
        </div>
      </div>
    )
  );
}
