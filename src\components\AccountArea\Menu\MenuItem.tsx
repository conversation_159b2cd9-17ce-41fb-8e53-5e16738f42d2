'use client';

import React from 'react';
import { Link, usePathname } from '@/lib/i18n/routing';
import useLayoutStore from '@/store/useLayoutStore';

const MenuItem = ({ item: { path, title } }: { item: { id: string; path: string; title: string } }) => {
  const activePath = usePathname();
  const { handleMobileOpen } = useLayoutStore();
  
  return (
    <>
      <Link
        className={`link ${path === activePath ? 'active' : ''} text-black lgh:text-base xl:!text-lg`}
        href={`${path}`}
        onClick={() => handleMobileOpen(false)}
        prefetch={path.includes('insights') ? false : true}
      >
        {title}
      </Link>
      <span className='line'></span>
    </>
  );
};
export default MenuItem;
