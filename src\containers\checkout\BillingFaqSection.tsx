import { useMemo } from 'react';
import { useTranslations } from 'next-intl';
import BillingFaqCard from '@/components/Cards/CheckoutPage/BillingFaqCard';

const BillingFaqSection = () => {
  const t = useTranslations('checkout.billing_faq');

  const faqs = useMemo(
    () => [
      {
        img: <svg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 36 36' fill='none'>
          <rect width='36' height='36' fill='white' />
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M6.00391 9.69336H30.0004V26.3063H6.00391V9.69336Z'
            className='fill-primary opacity-15'
          />
          <path d='M6 14.3086H29.9965' className="stroke-primary" strokeWidth='2' />
          <path d='M21.6914 22.6152L27.2291 22.6152' className="stroke-primary" strokeWidth='2' />
          <path d='M18 22.6152L20.7688 22.6152' className="stroke-primary" strokeWidth='2' />
        </svg>,
        title: t('items.why_not_free.title'),
        text: t('items.why_not_free.text'),
      },
      {
        img: <svg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 36 36' fill='none'>
          <rect width='36' height='36' fill='white' />
          <path d='M7.62891 16H27.272V29.0002H7.62891V16Z' className='fill-primary opacity-15' />
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M13.0849 13.0001C13.0849 11.9392 13.5448 10.9218 14.3634 10.1716C15.1821 9.42146 16.2923 9.00003 17.45 9.00003C18.6078 9.00003 19.718 9.42146 20.5367 10.1716C21.3553 10.9218 21.8152 11.9392 21.8152 13.0001V16.0001H23.9977V13.0001C23.9977 11.4088 23.3079 9.88261 22.08 8.75738C20.852 7.63215 19.1866 7 17.45 7C15.7135 7 14.0481 7.63215 12.8201 8.75738C11.5922 9.88261 10.9023 11.4088 10.9023 13.0001V16.0001H13.0849V13.0001ZM16.3588 20.5002V24.5002H18.5413V20.5002H16.3588Z'
            className="fill-primary"
          />
        </svg>,
        title: t('items.security.title'),
        text: t('items.security.text'),
      },
      {
        img: <svg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 36 36' fill='none'>
          <rect width='36' height='36' fill='white' />
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M30.0006 10.2852H16.1238L11.0312 25.7138H30.0006V10.2852Z'
            className='fill-primary opacity-15'
          />
          <path
            d='M16.9102 17.9993C16.9102 18.73 17.1975 19.4307 17.709 19.9474C18.2205 20.4641 18.9142 20.7544 19.6375 20.7544C20.3609 20.7544 21.0546 20.4641 21.5661 19.9474C22.0775 19.4307 22.3649 18.73 22.3649 17.9993C22.3649 17.2686 22.0775 16.5678 21.5661 16.0511C21.0546 15.5344 20.3609 15.2441 19.6375 15.2441C18.9142 15.2441 18.2205 15.5344 17.709 16.0511C17.1975 16.5678 16.9102 17.2686 16.9102 17.9993Z'
            className="fill-primary"
          />
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M6 10.7129L13.6715 10.7129L13.0543 12.4272H6V10.7129ZM6 23.5701H9.42859L8.82859 25.2844H6L6 23.5701ZM6 18.8558H10.9715L11.5715 17.1415H6V18.8558Z'
            className="fill-primary"
          />
        </svg>,
        title: t('items.after_payment.title'),
        text: t('items.after_payment.text'),
      },
    ],
    [t]
  );

  return (
    <div className='mt-[60px] mb-[150px] md:mb-[60px]'>
      <h4 className='text-center mb-4 md:mb-6'>{t('title')}</h4>
      <ul className='flex flex-wrap justify-center 2xl:justify-between gap-5 mx-4 md:mx-0'>
        {faqs.map((faq, i) => (
          <BillingFaqCard key={i} {...{ faq }} />
        ))}
      </ul>
    </div>
  );
};

export default BillingFaqSection;
