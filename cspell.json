{"version": "0.2", "language": "en", "words": [], "ignoreWords": ["SOLIDGATE", "solidgate", "lscache"], "patterns": [{"name": "HTML-like", "pattern": "/<[^>]*>/g", "description": "HTML-like tags"}, {"name": "CSS-classes", "pattern": "/\\b[A-Z][a-zA-Z0-9]*\\b/g", "description": "CSS class names"}, {"name": "camelCase", "pattern": "/\\b[a-z]+[A-Z][a-zA-Z0-9]*\\b/g", "description": "camelCase identifiers"}, {"name": "PascalCase", "pattern": "/\\b[A-Z][a-zA-Z0-9]*\\b/g", "description": "PascalCase identifiers"}], "ignorePaths": ["node_modules/**", ".git/**", "dist/**", "build/**", "coverage/**", "*.min.js", "package-lock.json", "yarn.lock"]}