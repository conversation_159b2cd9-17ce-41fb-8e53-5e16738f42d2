"use client";

import SessionContext from "@/store/SessionContext";
import { useContext } from "react";
import { useTranslations } from 'next-intl';

const LegalNotice = () => {
    const t = useTranslations('legal_notice');
    const { siteConfig } = useContext(SessionContext);
        
    return <div className="flex flex-col justify-center gap-4">
        <h1 className="text-5xl font-bold mb-10">{t('title')}</h1>
        <div className="flex gap-2 text-xl">
            <span className="font-bold">{t('labels.website_url')}:</span>
            <span>{siteConfig.domain}</span>
        </div>
        {siteConfig.companyName &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.managed_by')}:</span>
                <span>{siteConfig.companyName}</span>
            </div>
        }
        {siteConfig.companyAddress &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.registered_address')}:</span>
                <span>{siteConfig.companyAddress}</span>
            </div>
        }
        {siteConfig.supportEmail &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.contact')}:</span>
                <span>{siteConfig.supportEmail}</span>
            </div>
        }
        {siteConfig.hostingProvider &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.hosting_provider')}:</span>
                <span>{siteConfig.hostingProvider}</span>
            </div>
        }
        {siteConfig.euVat &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.eu_vat')}:</span>
                <span>{siteConfig.euVat}</span>
            </div>
        }
        {siteConfig.companyRegistrationNumber &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">{t('labels.company_registration_number')}:</span>
                <span>{siteConfig.companyRegistrationNumber}</span>
            </div>
        }
    </div>
};

export default LegalNotice;