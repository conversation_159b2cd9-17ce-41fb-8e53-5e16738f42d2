import { z } from 'zod';

const Schema = z.object({
  stripePublishableKey: z.string().min(1, 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required'),
});

const stripeConfig = Schema.parse({
  stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
});

/**
 * Stripe configuration object validated at runtime using Zod.
 *
 * @property {string} stripePublishableKey - The Stripe publishable key, loaded from the environment variable NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.
 *                                           This value is required and validated to be a non-empty string.
 *
 * @example
 * import stripeConfig from '@/config/stripe.config';
 * const key = stripeConfig.stripePublishableKey;
 */
export default stripeConfig;
