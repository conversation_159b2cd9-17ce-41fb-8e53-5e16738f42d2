'use client';

import React, { useContext, ChangeEvent, useState, useEffect, useCallback, useMemo } from 'react';
import { useLocale } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import Logo from '@/components/EmotionalIntelligence/Landing/Logo';
import IconWithOrder from '@/components/EmotionalIntelligence/Payment/IconWithOrder';
import IconWithSmallSupport from '@/components/EmotionalIntelligence/Payment/IconWithSmallSupport';
import PaymentCards from '@/components/EmotionalIntelligence/Payment/PaymentCards';
import LanguageCheckoutForm from '@/components/EmotionalIntelligence/Payment/LanguageCheckoutForm';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementLocale, StripeElementsOptions } from '@stripe/stripe-js';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import { debounce, isEmpty } from 'lodash';
import { addFormDataToSessionDb } from '@/services/session';
import { COMPLIANCE_CHECKOUT_VERSION, COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';
import { getClickIdFromStorage } from '@/utils/getClickIdFromStorage';

const createPaymentIntent = httpsCallable(functions, 'createPaymentIntent');
const emailCheck = httpsCallable(functions, 'checkIfEmailExists');
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const Payment: React.FC = () => {
  const router = useRouter(); // Initialize useRouter
  const locale = useLocale();
  const [clientSecret, setClientSecret] = useState('');
  const [email, setEmail] = useState<string>('');
  const [emailError, setEmailError] = useState<string>('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const { time } = useContext(UiContext);
  const { checkoutVersion, formData, updateFormData, fetchAllSessionData, sessionId, prices, emotionalScores } =
    useContext(SessionContext);

  const compliantVersion = useMemo(() => time < COMPLIANCE_TIME_THRESHOLD, [time]);
  const clickId = useMemo(() => getClickIdFromStorage(), []);

  const stripeOptions: StripeElementsOptions = {
    clientSecret,
    locale: locale as StripeElementLocale,
    appearance: {
      theme: 'stripe',
      rules: {
        '.Label': {
          color: '#0E2432',
          fontFamily: 'Raleway, sans-serif',
          fontSize: '14px',
          fontWeight: '600',
          marginBottom: '8px',
          paddingTop: '0',
        },
        '.Input': {
          border: '1px solid #ebebeb',
          borderRadius: '10px',
          paddingTop: '14px',
          paddingBottom: '14px',
          color: '#0E2432',
        },
        select: {
          display: 'none',
        },
      },
    },
  };

  useEffect(() => {
    if (isEmpty(emotionalScores)) {
      router.push('/emotional-intelligence/test');
    }
    //addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'emotional-intelligence' });
    const urlClientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');

    const initializePaymentIntent = async () => {
      if ((email && isEmailValid && emailError === '') || urlClientSecret) {
        try {
          // Directly asserting the type on API call response
          const emailCheckResponse = (await emailCheck({
            email: localStorage.getItem('emotionalIntelligenceEmail') || email,
          })) as { data: { exists: boolean } };

          if (!emailCheckResponse.data.exists || urlClientSecret) {
            const res = (await createPaymentIntent({
              email:
                typeof window !== 'undefined'
                  ? localStorage.getItem('emotionalIntelligenceEmail') || email
                  : formData.email,
              name: formData.name,
              sessionId,
              clickId,
              landingUrl: localStorage.getItem('landingUrl'),
              country: prices.country,
              completionTime: time,
              checkoutVersion: compliantVersion ? COMPLIANCE_CHECKOUT_VERSION : checkoutVersion,
              price: prices.trial.amount,
              receipt_email: email,
              pageType: 'EI-page',
              metadata: {
                email,
              },
            })) as { data: { clientSecret: string } };

            setClientSecret(res.data.clientSecret);
          } else {
            setIsEmailValid(false);
            setEmailError('This email is already in use. Please use a different email.');
          }
        } catch (error) {
          console.error('Error initializing payment intent:', error);
        }
      }
    };

    initializePaymentIntent();
  }, [email, isEmailValid, formData, time, prices, checkoutVersion]);

  const reCreateClientSecret = () => {
    const urlClientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    const useremail =
      typeof window !== 'undefined' ? localStorage.getItem('emotionalIntelligenceEmail') || email : email;
    updateFormData({ ...formData, ['email']: useremail });
    addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'emotional-intelligence' });
    if ((email && isEmailValid && emailError === '') || urlClientSecret) {
      createPaymentIntent({
        email: typeof window !== 'undefined' ? localStorage.getItem('emotionalIntelligenceEmail') : formData.email,
        name: 'formData.name',
        sessionId,
        clickId,
        type: 'EI-page',
        landingUrl: localStorage.getItem('landingUrl'),
        country: prices.country,
        completionTime: time,
        checkoutVersion: compliantVersion ? COMPLIANCE_CHECKOUT_VERSION : checkoutVersion,
        price: prices.trial.amount,
        receipt_email: email,
        metadata: {
          email,
          type: 'EI-page',
        },
      }).then((res: any) => {
        setClientSecret(res.data.clientSecret);
      });
    }
  };

  const handleEmailChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setEmail(value);
    updateFormData({ ...formData, ['email']: value });
    setEmailError(validateEmail(value));
    debouncedValidateEmail(value);
    console.log('updateemail', formData);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) ? '' : 'Please enter a valid email address';
  };

  // Debounced function to validate email
  const debouncedValidateEmail = useCallback(
    debounce((value: string) => {
      const isValid = validateEmail(value);
      setEmailError(isValid);

      if (isValid.length === 0) {
        setIsEmailValid(true);
        if (typeof window !== 'undefined') {
          localStorage.setItem('emotionalIntelligenceEmail', value);
        }
        reCreateClientSecret();
      } else {
        setIsEmailValid(false);
      }
    }, 500), // Adjust debounce delay (500ms)
    []
  );

  return (
    <div className="max-w-[1440px] mx-auto">
      <header className="w-full flex justify-center items-center py-[13px] md:py-[43px] mb-[27px] md:mb-[18px]">
        <Logo />
      </header>
      <main>
        <div
          id="paymentDetails"
          className="px-[16px] md:px-[81px] flex-col flex lg:flex-row md:gap-[44px] md:mb-[40px]">
          <div className="w-full md:pl-[80px] order-2 md:order-none">
            <div className="flex flex-col items-center mb-[16px] md:mb-[24px]">
              <div className="w-full flex flex-row justify-between mb-5 md:mb-6">
                <span className="font-ppmori font-bold text-[20px] md:text-[26px] leading-[30px] md:leading-[32px] text-[#0E2432] tracking-normal md:tracking-tight">
                  Order Details
                </span>
                {/* <span
                  className="flex items-center gap-[10px] py-[6px] md:py-[8px] px-[10px] md:px-[12px] bg-[#F6F6F6] rounded-[8px] text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] font-ppmori font-medium text-[#0E2432]"
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                  7-Day Full Access
                </span> */}
              </div>
              <div className="w-full grid grid-cols-1 gap-2 md:gap-3 mb-3 md:mb-4">
                <IconWithOrder source="/images/emotional-intelligence/payment/order-1.svg" alt="order-1">
                  Your personalized <br />
                  Emotional Intelligence Assessment
                </IconWithOrder>
                <IconWithOrder source="/images/emotional-intelligence/payment/order-2.svg" alt="order-2">
                  Personal Growth Challenge
                </IconWithOrder>
                <IconWithOrder source="/images/emotional-intelligence/payment/order-3.svg" alt="order-3">
                  Plan to Fortify Your Connections
                </IconWithOrder>
                <IconWithOrder source="/images/emotional-intelligence/payment/order-4.svg" alt="order-4">
                  Access from any device anywhere
                </IconWithOrder>
              </div>
              <div className="w-full bg-[#F6F6F6] rounded-[12px] p-3 md:py-5 md:px-6 flex flex-row justify-between mb-5 md:mb-6">
                <label className="font-ppmori font-bold text-[#0E2432] text-[16px] leading-[24px] md:text-[20px] md:leading-[26px]">
                  Total
                </label>
                <label
                  className="font-ppmori font-bold text-[#0E2432] text-[16px] leading-[24px] md:text-[20px] md:leading-[26px]"
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                  {prices.trial.formatted}
                </label>
              </div>
              <PaymentCards jcb={false} mb_desktop="20px" mb_mobile="24px" h_mobile="28px" h_desktop="33px" />
              <div className="w-full opacity-50 border border-[#E2E2E2] mb-4 md:mb-5"></div>
              <div className="flex gap-[8px] justify-between md:gap-[6px] w-full flex-col md:flex-row">
                <IconWithSmallSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  14 days refund
                </IconWithSmallSupport>
                <IconWithSmallSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  Premium Support
                </IconWithSmallSupport>
                <IconWithSmallSupport source="/images/emotional-intelligence/checkout/check-dark.svg" alt="check-dark">
                  Satisfaction guarantee
                </IconWithSmallSupport>
              </div>
            </div>
          </div>
          <div className="w-full flex flex-col items-center order-1 md:order-none mb-[32px] md:mb-0">
            <div className="w-full flex flex-col gap-4 md:gap-6 p-4 md:p-6 shadow-[0_2px_12px_0_#5400690F] rounded-[12px] bg-white mb-6">
              <label className="font-ppmori font-bold text-[20px] md:text-[26px] leading-[24px] md:leading-[34px] text-[#0E2432]">
                What&apos;s Your Email address?
              </label>
              <div className="w-full flex flex-col gap-2">
                <label className="font-ppmori font-semibold text-[#0E2432] text-[14px] leading-[20px]">
                  Email address
                </label>
                <input
                  className="bg-white font-ppmori font-medium text-[16px] px-4 py-[14px] leading-[20px] text-[#7A8C97] border border-[#EBEBEB] rounded-[10px]"
                  placeholder="Enter your email"
                  value={email}
                  onChange={handleEmailChange}
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}
                />
                {emailError && <span className="text-red-500 text-sm">{emailError}</span>}
              </div>
            </div>
            <div className="relative w-full flex flex-col shadow-[0_2px_12px_0_#5400690F] rounded-[12px] bg-white">
              {!isEmailValid && (
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    cursor: 'not-allowed',
                    backgroundColor: '#ccc',
                    opacity: 0.5,
                    borderRadius: '10px',
                    zIndex: '999',
                    // Could add a subtle background or tooltip here
                    // backgroundColor: 'rgba(255, 255, 255, 0)',
                  }}
                  onClick={() => {
                    // If you want a custom message or tooltip:
                    console.log('You must enter an email before using Apple/Google Pay');
                  }}
                />
              )}
              {/* <button className="w-full flex justify-center shadow-[0px_4px_8px_0px_#003A6F0D] py-4 md:py-[19.5px] rounded-[8px] border border-[#C1CFE973] mb-4 md:mb-6">
                <Image
                  src="/images/emotional-intelligence/payment/paypal.svg"
                  alt="shield keyhole"
                  width={66}
                  height={18}
                  className="w-[66px] h-auto md:w-[66px]"
                />
              </button>
              <div className="w-full flex flex-row items-center gap-4 md:gap-5 mb-4 md:mb-6">
                <div className="w-full h-[1px] bg-[#E3E9F5]" />
                <label className="font-ppmori font-medium text-[#8C8492] text-[16px] leading-[20px]">Or</label>
                <div className="w-full h-[1px] bg-[#E3E9F5]" />
              </div> */}

              {clientSecret && (
                <div className="flex flex-wrap p-4 md:p-6">
                  {clientSecret && (
                    <Elements options={stripeOptions} stripe={stripePromise}>
                      <LanguageCheckoutForm
                        clientSecret={clientSecret}
                        email={email}
                        // reCreateClientSecret={reCreateClientSecret}
                      />
                    </Elements>
                  )}
                </div>
              )}
              {!clientSecret && (
                <div className="opacity-10">
                  <img className="w-full hidden sm:flex" src="/images/card-placeholder.svg" />
                  <img className="w-full sm:hidden" src="/images/card-placeholder-mobile.svg" />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Payment;
