import { useContext } from 'react';
import { UserContext } from '@/store/UserContext';

export const useToolkitPlan = () => {
  const { user } = useContext(UserContext);

  /**
   * Helper function to safely get global toolkit access
   */
  const hasGlobalToolkitAccess = (): boolean => {
    return !!user?.has_toolkits_access;
  };

  /**
   * Checks if the user has bundle access (access to all 3 toolkits at bundle price)
   */
  const hasBundleAccess = (): boolean => {
    return !!user?.has_toolkits_bundle_access;
  };

  /**
   * Checks if the user has access to the Career & Education IQ Guide toolkit
   */
  const hasCareerToolkitAccess = (): boolean => {
    return !!user?.has_career_toolkit_access;
  };

  /**
   * Checks if the user has access to the Stress Management Secrets toolkit
   */
  const hasStressToolkitAccess = (): boolean => {
    return !!user?.has_stress_toolkit_access;
  };

  /**
   * Checks if the user has access to the Brain Biohacks toolkit
   */
  const hasBrainToolkitAccess = (): boolean => {
    return !!user?.has_brain_toolkit_access;
  };

  /**
   * Checks if the user has access to a specific toolkit by name
   */
  const hasToolkitAccess = (toolkit: 'career' | 'stress' | 'brain'): boolean => {
    switch (toolkit) {
      case 'career':
        return hasCareerToolkitAccess();
      case 'stress':
        return hasStressToolkitAccess();
      case 'brain':
        return hasBrainToolkitAccess();
      default:
        return false;
    }
  };

  return {
    user,
    hasGlobalToolkitAccess: hasGlobalToolkitAccess(),
    hasBundleAccess: hasBundleAccess(),
    hasCareerToolkitAccess: hasCareerToolkitAccess(),
    hasStressToolkitAccess: hasStressToolkitAccess(),
    hasBrainToolkitAccess: hasBrainToolkitAccess(),
    hasToolkitAccess,
  };
};
