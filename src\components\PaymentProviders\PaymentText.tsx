import { useTranslations } from 'next-intl';
import { Prices } from '@/app/prices';
import { SubscriptionPlan, SubscriptionPlanEnum } from '@/types/plan-types';
import type { BasePaymentProps } from '@/types/payment';
import { getFormattedSubscriptionPrice } from '@/utils/getSubscriptionPlan';

/**
 * Renders payment text for trial, subscription, or one-time payment.
 * @param props - Payment props
 * @param type - 'trial', 'subscription', or 'onetime'
 */
function PaymentText(props: BasePaymentProps, type: 'trial' | 'subscription' | 'onetime') {
  const t = useTranslations('checkout.payment_details');
  const ct = useTranslations('common.per_period');

  if (type === 'onetime') {
    return <p className="text-xs mt-4">{t('onetime_text')}</p>;
  }

  const { prices, plan } = props;
  const price = getFormattedSubscriptionPrice(prices, plan);
  const vatText = prices.vatIncluded ? t('vat_included') : '';

  const values: Record<string, string> = {
    subscriptionPrice: price,
    vatIncluded: vatText,
    period: plan === SubscriptionPlanEnum.Weekly ? ct('week') : ct('month'),
  };
  if (type === 'trial') {
    values.trialPrice = prices.trial.formatted;
  }

  return <p className="text-xs mt-4">{t(type + '_text', values)}</p>;
}

export function TrialText(props: BasePaymentProps) {
  return PaymentText(props, 'trial');
}

export function SubscriptionText(props: BasePaymentProps) {
  return PaymentText(props, 'subscription');
}

export function OneTimeText() {
  return PaymentText({} as BasePaymentProps, 'onetime');
}

/**
 * Wrapper to select and render the correct payment text based on props.
 */
export function PaymentTextWrapper({
  isTrial,
  isOneTime,
  prices,
  plan,
}: {
  isTrial: boolean;
  isOneTime: boolean;
  prices: Prices;
  plan: SubscriptionPlan;
}) {
  if (isTrial && !isOneTime) {
    return <TrialText prices={prices} plan={plan} isTrial={isTrial} isOneTime={isOneTime} />;
  } else if (isOneTime) {
    return <OneTimeText />;
  } else {
    return <SubscriptionText prices={prices} plan={plan} isTrial={isTrial} isOneTime={isOneTime} />;
  }
}
