import React from 'react';
import { useTranslations } from 'next-intl';
import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

function OfferChargeNotice() {
  const t = useTranslations('offers_flow');
  const { offers } = useContext(SessionContext);
  return <>{t('common', { price: offers?.products?.allInOneBundle?.formatted || '' })}</>;
}

export default OfferChargeNotice;
