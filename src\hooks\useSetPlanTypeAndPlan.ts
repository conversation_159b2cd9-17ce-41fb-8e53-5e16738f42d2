import { useCallback, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import SessionContext from '@/store/SessionContext';
import { PlanTypeEnum, SubscriptionPlanEnum } from '@/types/plan-types';

export function useSetPlanTypeAndPlan() {
  const searchParams = useSearchParams();
  const { updatePlanType, updatePlan, updateFixedPrice, updateFixedCurrency } = useContext(SessionContext);

  /**
   * Sets plan type and plan based on query params 'f', 'fp', and 'fpc'.
   * Only updates if 'f', 'fp', and 'fpc' are all present and valid.
   */
  const setPlanTypeAndPlan = useCallback(() => {
    const f = searchParams.get('f');
    if (!isValidFParam(f)) return;

    updatePlanType(PlanTypeEnum.Onetime);
    updatePlan(SubscriptionPlanEnum.Fix);
  }, [searchParams, updatePlanType, updatePlan, updateFixedPrice, updateFixedCurrency]);

  return { setPlanTypeAndPlan };
}

/**
 * Checks if the 'f' query parameter is a valid value for setting plan type and plan.
 * Returns false for: 'false', '0', '', 'null', 'undefined' (case-insensitive, trimmed).
 * @param f - The value of the 'f' query parameter
 * @returns true if valid, false otherwise
 */
function isValidFParam(f: string | null): boolean {
  const invalid = new Set(['false', '0', '', 'null', 'undefined']);
  const normalized = (f ?? '').trim().toLowerCase();
  return !invalid.has(normalized);
}
