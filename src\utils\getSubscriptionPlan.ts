import { Prices } from '@/app/prices';
import { SubscriptionPlan, SubscriptionPlanEnum, FixedCurrency } from '@/types/plan-types';
import { ALLOWED_FIXED_PRICES } from '@/utils/constants';

/**
 * Determines the subscription plan name based on the "sp" query parameter.
 *
 * @param sp - The value of the "sp" query param (can be null or string)
 * @returns 'weekly' or 'monthly' (full plan name); defaults to 'monthly'
 *
 * Examples:
 * getSubscriptionPlan('w') => 'weekly'
 * getSubscriptionPlan('m') => 'monthly'
 * getSubscriptionPlan('') => 'monthly'
 * getSubscriptionPlan('random') => 'monthly'
 * getSubscriptionPlan(null) => 'monthly'
 */
export function getSubscriptionPlan(sp: string | null): SubscriptionPlan {
  switch (sp?.trim().toLowerCase()) {
    case 'w':
      return SubscriptionPlanEnum.Weekly;
    case 'm':
      return SubscriptionPlanEnum.Monthly;
    default:
      return SubscriptionPlanEnum.Monthly;
  }
}

/**
 * Returns the formatted subscription price string based on the selected plan type (weekly or monthly).
 *
 * @param prices - Pricing details for the current region
 * @param plan - Selected subscription plan ('weekly' or 'monthly')
 * @returns {string} The formatted price string (e.g., "$9.90")
 */
export function getFormattedSubscriptionPrice(prices: Prices, plan: SubscriptionPlan): string {
  if (plan === SubscriptionPlanEnum.Weekly && prices.subscription.weekly?.formatted) {
    return prices.subscription.weekly.formatted;
  }
  return prices.subscription.formatted;
}

/**
 * Returns the formatted price string for a one-time payment.
 *
 * @param prices - Pricing details for the current region
 * @returns {string} The formatted one-time price string (e.g., "$9.90")
 */
export function getFormattedOneTimePrice(prices: Prices): string {
  return prices.oneTime?.formatted ?? '';
}

/**
 * Returns the one-time price amount if available.
 *
 * @param prices - The pricing configuration for the selected country
 * @returns {number | undefined} The one-time price amount, or undefined if not available
 */
export function getOneTimeAmount(prices: Prices): number | undefined {
  return prices.oneTime?.amount;
}

/**
 * Returns the trial price amount if available.
 *
 * @param prices - The pricing configuration for the selected country
 * @returns {number | undefined} The trial price amount, or undefined if not available
 */
export function getTrialAmount(prices: Prices): number | undefined {
  return prices.trial?.amount;
}

/**
 * Returns the correct subscription price based on the selected plan.
 *
 * Priority:
 * 1. Weekly subscription price (if selected)
 * 2. Monthly/default subscription price
 *
 * @param prices - The pricing configuration for the selected country
 * @param plan - The selected subscription plan (weekly or monthly)
 * @returns {number} The applicable price amount
 */
export function getSubscriptionAmount(prices: Prices, plan: SubscriptionPlan): number {
  if (plan === SubscriptionPlanEnum.Weekly && prices.subscription.weekly?.amount) {
    return prices.subscription.weekly.amount;
  }

  return prices.subscription.amount;
}

/**
 * Checks if a price is allowed for a given currency
 */
export function isFixedPrice(price: number, currency: FixedCurrency): boolean {
  return ALLOWED_FIXED_PRICES[currency]?.prices.includes(price) ?? false;
}

/**
 * Returns a valid fixed price for a given currency.
 * If the requested price is allowed, returns it; otherwise, returns the default fixed price.
 *
 * @param price - The price from query params or user input
 * @param currency - The currency to check against
 * @returns The allowed price
 */
export function getFixedPrice(price: number | undefined, currency: FixedCurrency): number {
  if (typeof price === 'number' && isFixedPrice(price, currency)) {
    return price;
  }
  return ALLOWED_FIXED_PRICES[currency].default_price;
}

/**
 * Returns the currency code for a given country code.
 * (Currently always returns 'usd'; can be made dynamic later.)
 * @returns {string} The currency code (e.g., 'usd', 'eur', etc.)
 */
export function getCurrencyForUS(): string {
  return 'usd';
}

/**
 * Returns the currency code in uppercase for the current plan.
 * If one-time, uses getCurrencyForUS; otherwise, uses prices.currency.
 * @param isOneTime - Whether this is a one-time payment
 * @param prices - The Prices object
 * @param country - Optional country code (used for one-time payments)
 * @returns {string} The currency code in uppercase
 */
export function getPlanCurrency(isOneTime: boolean, prices: Prices): string {
  return isOneTime ? getCurrencyForUS().toUpperCase() : prices.currency.toUpperCase();
}
