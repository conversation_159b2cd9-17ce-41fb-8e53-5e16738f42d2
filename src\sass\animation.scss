/* Circular progress for Calculation page*/
.circular-progress {
  --size: 132px;
  --half-size: calc(var(--size) / 2);
  --stroke-width: 10px;
  --radius: calc((var(--size) - var(--stroke-width)) / 2);
  --circumference: calc(var(--radius) * pi * 2);
  --dash: calc((var(--progress) * var(--circumference)) / 100);
  /*https://cubic-bezier.com/#.06,.84,1,.25*/
  /*animation: progress-animation 5s cubic-bezier(0.06, 0.84, 1, 0.25) 1s 1 forwards;*/
  animation: progress-animation 5s cubic-bezier(0.06, 0.84, 1, 0.25) 1s 1 forwards;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.shorter-circular-progress {
  animation: progress-animation 3s cubic-bezier(0.06, 0.84, 1, 0.25) 1s 1 forwards;
}

.circular-progress circle {
  cx: var(--half-size);
  cy: var(--half-size);
  r: var(--radius);
  stroke-width: var(--stroke-width);
  fill: none;
  stroke-linecap: round;
}

.circular-progress circle.bg {
  stroke: #ddd;
}

.circular-progress circle.fg {
  transform: rotate(-90deg);
  transform-origin: var(--half-size) var(--half-size);
  stroke-dasharray: var(--dash) calc(var(--circumference) - var(--dash));
  transition: stroke-dasharray 0.3s linear 0s;
  stroke: theme('colors.primary');
}

@keyframes progress-animation {
  from {
    --progress: 0;
  }
  to {
    --progress: 100;
  }
}

@keyframes changeNumber {
  @for $i from 0 through 100 {
    $number: $i;
    $percent: $number + '%';
    #{$percent} {
      content: $i + '%';
    }
  }
}

.counter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  min-width: 72px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.counter {
  font-size: 28px;
  color: theme('colors.primary');
  line-height: 100%;
  padding-top: 5px;

  &:after {
    content: '0%';
    display: block;
    text-align: center;
    animation: changeNumber 5s cubic-bezier(0.06, 0.84, 1, 0.25) 1s 1 forwards; /*cubic-bezier(0.06, 0.84, 1, 0.25)*/
  }
}

.shorter-counter {
  font-size: 28px;
  color: theme('colors.primary');
  line-height: 100%;
  padding-top: 5px;

  &:after {
    content: '0%';
    display: block;
    text-align: center;
    animation: changeNumber 3s cubic-bezier(0.06, 0.84, 1, 0.25) 1s 1 forwards; /*cubic-bezier(0.06, 0.84, 1, 0.25)*/
  }
}

/*@function borderColor($i) {
  @if ($i > 20) {
    @return orange;
  } @else {
    @return transparent;
  }
}

$variable: var(--progress);*/

@keyframes changeFill {
  100% {
    fill: theme('colors.primary');
  }
}

path#tagCheck {
  animation: changeFill 1s linear 1s 1 forwards;
}

@keyframes changeBorderColor {
  100% {
    border: 1px solid theme('colors.primary');
    color: theme('colors.primary');
  }
}

.tag-0 {
  animation: changeBorderColor 1s linear 1s 1 forwards;
}

.tag-1 {
  animation: changeBorderColor 1s linear 2s 1 forwards;
}

.tag-2 {
  animation: changeBorderColor 1s linear 3s 1 forwards;
}

.tag-3 {
  animation: changeBorderColor 1s linear 4s 1 forwards;
}

.tag-4 {
  animation: changeBorderColor 1s linear 5s 1 forwards;
}
