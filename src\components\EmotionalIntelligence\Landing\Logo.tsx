"use client";

import React, { useContext } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';

const Logo: React.FC = () => {
  const { siteConfig } = useContext(SessionContext);
  const t = useTranslations('emotional_intelligence.landing.alt_text');
  
  return (
    <Link href="/">
      <Image
        src={siteConfig.logo.path} // Path relative to 'public'
        alt={t('logo')}
        width={siteConfig.logo.width}
        height={siteConfig.logo.height}
        className="max-w-[215px] md:max-w-[300px] z-20"
      />
    </Link>
  );
};

export default Logo;
