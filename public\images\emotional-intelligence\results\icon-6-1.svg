<svg width="102" height="101" viewBox="0 0 102 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_4076_4972)">
<rect width="64.9892" height="64.9892" rx="10.8315" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 96.7441 15.7793)" fill="white"/>
<rect x="-0.510336" y="0.347919" width="64.1157" height="64.1157" rx="10.3948" transform="matrix(-0.982561 -0.18594 -0.18594 0.982561 95.7971 15.6905)" stroke="#F0EBF6" stroke-width="0.873488"/>
<g clip-path="url(#clip0_4076_4972)">
<path d="M46.2744 27.5868C46.6222 25.7499 48.3924 24.5433 50.2293 24.891C52.0662 25.2388 53.2728 27.009 52.9251 28.8459C52.5773 30.6828 50.8071 31.8894 48.9702 31.5416C47.1333 31.1939 45.9267 29.4237 46.2744 27.5868ZM61.8352 35.2213L62.1649 35.1101C62.1699 34.7639 62.2047 34.4122 62.2715 34.0597C62.3382 33.7073 62.434 33.3686 62.5562 33.0431L62.29 32.8191C61.4322 32.0959 61.3234 30.8147 62.0465 29.9568C62.7695 29.1003 64.0492 28.9926 64.9073 29.7144L65.1759 29.9402C65.833 29.5549 66.5623 29.2906 67.3287 29.1725L67.3934 28.8307C67.6019 27.7294 68.6636 27.0045 69.7663 27.2132C70.869 27.422 71.5923 28.4848 71.3838 29.5861L71.3191 29.928C71.989 30.3193 72.5728 30.8308 73.0422 31.4295L73.3748 31.3174C74.4382 30.9621 75.5903 31.5288 75.949 32.5887C76.3086 33.6516 75.739 34.8044 74.6761 35.164L74.3465 35.2752C74.3415 35.6214 74.3066 35.9731 74.2399 36.3255C74.1732 36.678 74.0774 37.0167 73.9551 37.3422L74.2214 37.5662C75.0792 38.2893 75.188 39.5706 74.4648 40.4285C73.9748 41.0081 73.2299 41.2459 72.5329 41.114C72.2004 41.051 71.8808 40.9051 71.6039 40.6722L71.3352 40.4464C70.6781 40.8317 69.9488 41.096 69.1824 41.2141L69.1177 41.5559C68.9092 42.6573 67.8475 43.3821 66.7448 43.1734C65.6422 42.9646 64.9189 41.9018 65.1274 40.8005L65.1921 40.4586C64.5222 40.0673 63.9383 39.5558 63.4689 38.9572L63.1363 39.0692C62.7934 39.1847 62.4412 39.2035 62.11 39.1408C61.4131 39.0089 60.8064 38.5165 60.5624 37.7966C60.2028 36.7337 60.7724 35.5809 61.8352 35.2213ZM67.8793 37.1881C68.9793 37.3963 70.044 36.6706 70.2522 35.5706C70.4605 34.4706 69.7347 33.406 68.6347 33.1977C67.5347 32.9895 66.4701 33.7152 66.2618 34.8152C66.0536 35.9152 66.7793 36.9798 67.8793 37.1881ZM74.208 45.4749C73.7503 44.45 72.5516 43.9916 71.527 44.448L51.9638 53.1735C50.9389 53.6311 50.4789 54.8309 50.9365 55.8558C51.2192 56.488 51.7837 56.9049 52.4155 57.0245C52.8079 57.0988 53.2262 57.0581 53.6189 56.8831L73.1821 48.1576C74.207 47.6999 74.6657 46.4999 74.208 45.4749ZM54.7202 48.4748L55.0979 46.4796C55.3757 45.0125 54.4084 43.5935 52.9413 43.3158L50.281 42.8122L51.0365 38.8218L51.929 38.9908C53.1727 39.2262 54.4335 38.9634 55.4792 38.2506L58.507 36.1867C59.434 35.5548 59.673 34.2925 59.0411 33.3655C58.4092 32.4385 57.1469 32.1995 56.2199 32.8314L53.1921 34.8953C53.0418 34.9978 52.8627 35.0341 52.6844 35.0004L48.4666 34.2019C46.6297 33.8541 44.8595 35.0608 44.5118 36.8977L42.2165 49.0218C42.2006 49.1056 42.1688 49.1863 42.1218 49.2601L40.4422 51.8947C39.8389 52.8401 40.1166 54.0954 41.0621 54.6974C41.2872 54.8406 41.5287 54.9332 41.7761 54.98C42.5675 55.1298 43.4073 54.7983 43.8664 54.0765L45.5443 51.4429C45.871 50.9329 46.093 50.3715 46.2056 49.7769L46.864 46.2987L50.8544 47.0541L50.7285 47.7192C50.52 48.8205 51.2433 49.8833 52.346 50.0921C53.4486 50.3008 54.5104 49.5759 54.7189 48.4746L54.7202 48.4748Z" fill="url(#paint0_linear_4076_4972)"/>
</g>
</g>
<defs>
<filter id="filter0_dii_4076_4972" x="-0.898265" y="-1.44948" width="103.891" height="103.891" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.72714" dy="8.83102"/>
<feGaussianBlur stdDeviation="6.98791"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0174417 0 0 0 0 0.031694 0 0 0 0 0.37375 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4076_4972"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4076_4972" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_4076_4972"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.2139"/>
<feGaussianBlur stdDeviation="0.553475"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_4076_4972" result="effect3_innerShadow_4076_4972"/>
</filter>
<linearGradient id="paint0_linear_4076_4972" x1="74.397" y1="46.3915" x2="46.0935" y2="29.5156" gradientUnits="userSpaceOnUse">
<stop stop-color="#F4B368"/>
<stop offset="1" stop-color="#EDA047"/>
</linearGradient>
<clipPath id="clip0_4076_4972">
<rect width="32.49" height="32.49" fill="white" transform="translate(45.8281 22.6816) rotate(10.72)"/>
</clipPath>
</defs>
</svg>
