<svg width="78" height="82" viewBox="0 0 78 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1780_2181)">
<path d="M33.456 9.27855C35.7241 7.96909 36.6796 7.43033 37.6751 7.22417C38.5491 7.04318 39.4509 7.04318 40.3249 7.22417C41.3204 7.43033 42.2759 7.96909 44.544 9.27855L56.7714 16.338C59.0394 17.6475 59.9838 18.2057 60.6601 18.9647L61.3569 18.3438L60.6601 18.9647C61.2538 19.6311 61.7047 20.4121 61.985 21.2595C62.3042 22.2247 62.3154 23.3216 62.3154 25.9405V40.0595C62.3154 42.6784 62.3042 43.7753 61.985 44.7405C61.7047 45.5879 61.2538 46.3689 60.6601 47.0353C59.9838 47.7943 59.0394 48.3525 56.7714 49.662L44.544 56.7215C42.2759 58.0309 41.3204 58.5697 40.3249 58.7758C39.4509 58.9568 38.5491 58.9568 37.6751 58.7758C36.6796 58.5697 35.7241 58.0309 33.456 56.7215L21.2286 49.662C18.9606 48.3525 18.0162 47.7943 17.3399 47.0353L16.6431 47.6562L17.3399 47.0353C16.7462 46.3689 16.2953 45.5879 16.015 44.7405C15.6958 43.7753 15.6846 42.6784 15.6846 40.0595V25.9405C15.6846 23.3216 15.6958 22.2247 16.015 21.2595C16.2953 20.4121 16.7462 19.6311 17.3399 18.9647C18.0162 18.2057 18.9606 17.6475 21.2286 16.338L33.456 9.27855Z" fill="url(#paint0_radial_1780_2181)" fill-opacity="0.4"/>
<path d="M33.456 9.27855C35.7241 7.96909 36.6796 7.43033 37.6751 7.22417C38.5491 7.04318 39.4509 7.04318 40.3249 7.22417C41.3204 7.43033 42.2759 7.96909 44.544 9.27855L56.7714 16.338C59.0394 17.6475 59.9838 18.2057 60.6601 18.9647L61.3569 18.3438L60.6601 18.9647C61.2538 19.6311 61.7047 20.4121 61.985 21.2595C62.3042 22.2247 62.3154 23.3216 62.3154 25.9405V40.0595C62.3154 42.6784 62.3042 43.7753 61.985 44.7405C61.7047 45.5879 61.2538 46.3689 60.6601 47.0353C59.9838 47.7943 59.0394 48.3525 56.7714 49.662L44.544 56.7215C42.2759 58.0309 41.3204 58.5697 40.3249 58.7758C39.4509 58.9568 38.5491 58.9568 37.6751 58.7758C36.6796 58.5697 35.7241 58.0309 33.456 56.7215L21.2286 49.662C18.9606 48.3525 18.0162 47.7943 17.3399 47.0353L16.6431 47.6562L17.3399 47.0353C16.7462 46.3689 16.2953 45.5879 16.015 44.7405C15.6958 43.7753 15.6846 42.6784 15.6846 40.0595V25.9405C15.6846 23.3216 15.6958 22.2247 16.015 21.2595C16.2953 20.4121 16.7462 19.6311 17.3399 18.9647C18.0162 18.2057 18.9606 17.6475 21.2286 16.338L33.456 9.27855Z" stroke="url(#paint1_linear_1780_2181)" strokeWidth="1.86667"/>
<path d="M33.456 9.27855C35.7241 7.96909 36.6796 7.43033 37.6751 7.22417C38.5491 7.04318 39.4509 7.04318 40.3249 7.22417C41.3204 7.43033 42.2759 7.96909 44.544 9.27855L56.7714 16.338C59.0394 17.6475 59.9838 18.2057 60.6601 18.9647L61.3569 18.3438L60.6601 18.9647C61.2538 19.6311 61.7047 20.4121 61.985 21.2595C62.3042 22.2247 62.3154 23.3216 62.3154 25.9405V40.0595C62.3154 42.6784 62.3042 43.7753 61.985 44.7405C61.7047 45.5879 61.2538 46.3689 60.6601 47.0353C59.9838 47.7943 59.0394 48.3525 56.7714 49.662L44.544 56.7215C42.2759 58.0309 41.3204 58.5697 40.3249 58.7758C39.4509 58.9568 38.5491 58.9568 37.6751 58.7758C36.6796 58.5697 35.7241 58.0309 33.456 56.7215L21.2286 49.662C18.9606 48.3525 18.0162 47.7943 17.3399 47.0353L16.6431 47.6562L17.3399 47.0353C16.7462 46.3689 16.2953 45.5879 16.015 44.7405C15.6958 43.7753 15.6846 42.6784 15.6846 40.0595V25.9405C15.6846 23.3216 15.6958 22.2247 16.015 21.2595C16.2953 20.4121 16.7462 19.6311 17.3399 18.9647C18.0162 18.2057 18.9606 17.6475 21.2286 16.338L33.456 9.27855Z" stroke="url(#paint2_linear_1780_2181)" strokeWidth="1.86667"/>
<g filter="url(#filter1_d_1780_2181)">
<path d="M37.8519 15.5234C38.367 14.645 39.6369 14.645 40.1521 15.5234L45.1205 23.9943L54.7513 26.0654C55.7532 26.2808 56.1476 27.499 55.4621 28.2608L48.902 35.5516L49.8948 45.29C49.9984 46.3055 48.969 47.056 48.0339 46.6467L39.002 42.6944L29.97 46.6467C29.0349 47.056 28.0055 46.3055 28.109 45.29L29.102 35.5516L22.5418 28.2608C21.8563 27.499 22.2508 26.2808 23.2526 26.0654L32.8834 23.9943L37.8519 15.5234Z" fill="url(#paint3_linear_1780_2181)"/>
<path d="M37.8519 15.5234C38.367 14.645 39.6369 14.645 40.1521 15.5234L45.1205 23.9943L54.7513 26.0654C55.7532 26.2808 56.1476 27.499 55.4621 28.2608L48.902 35.5516L49.8948 45.29C49.9984 46.3055 48.969 47.056 48.0339 46.6467L39.002 42.6944L29.97 46.6467C29.0349 47.056 28.0055 46.3055 28.109 45.29L29.102 35.5516L22.5418 28.2608C21.8563 27.499 22.2508 26.2808 23.2526 26.0654L32.8834 23.9943L37.8519 15.5234Z" fill="url(#paint4_linear_1780_2181)" fill-opacity="0.5"/>
<path d="M44.9192 24.1124L44.9711 24.2009L45.0714 24.2225L54.7022 26.2935C55.5288 26.4713 55.8542 27.4762 55.2887 28.1047L48.7285 35.3956L48.6593 35.4724L48.6698 35.5753L49.6627 45.3137C49.7481 46.1515 48.8989 46.7706 48.1274 46.433L39.0955 42.4806L39.002 42.4397L38.9084 42.4806L29.8765 46.433C29.105 46.7706 28.2557 46.1515 28.3412 45.3137L29.3341 35.5753L29.3446 35.4724L29.2754 35.3956L22.7153 28.1047C22.1498 27.4762 22.4752 26.4713 23.3017 26.2935L32.9325 24.2225L33.0328 24.2009L33.0847 24.1124L38.0531 15.6414C38.4782 14.9168 39.5258 14.9168 39.9508 15.6414L44.9192 24.1124Z" stroke="url(#paint5_linear_1780_2181)" strokeWidth="0.466667"/>
</g>
</g>
<defs>
<filter id="filter0_d_1780_2181" x="0.751953" y="0.155273" width="76.4961" height="81.6895" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.405903 0 0 0 0 0.504479 0 0 0 0 0.695833 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1780_2181"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1780_2181" result="shape"/>
</filter>
<filter id="filter1_d_1780_2181" x="22.1992" y="14.8643" width="33.6055" height="33.8965" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.92549 0 0 0 0 0.501961 0 0 0 0 0.160784 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1780_2181"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1780_2181" result="shape"/>
</filter>
<radialGradient id="paint0_radial_1780_2181" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(39 33) rotate(90) scale(31.8889 38.8007)">
<stop offset="0.503375" stop-color="#FDE9D6"/>
<stop offset="1" stop-color="#ED7E17"/>
</radialGradient>
<linearGradient id="paint1_linear_1780_2181" x1="28.7333" y1="7.33333" x2="52.5333" y2="53.5333" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC14C"/>
<stop offset="1" stop-color="#EB7A3B"/>
</linearGradient>
<linearGradient id="paint2_linear_1780_2181" x1="14.6981" y1="5" x2="67.8331" y2="12.8233" gradientUnits="userSpaceOnUse">
<stop stop-color="#F37114"/>
<stop offset="1" stop-color="#F37114" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1780_2181" x1="36.4305" y1="15.5714" x2="43.8234" y2="44.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAD4D"/>
<stop offset="1" stop-color="#F5963D"/>
</linearGradient>
<linearGradient id="paint4_linear_1780_2181" x1="21.002" y1="13.5625" x2="59.4193" y2="16.3463" gradientUnits="userSpaceOnUse">
<stop stop-color="#F97619"/>
<stop offset="0.242682" stop-color="#FDE3D1"/>
<stop offset="0.582711" stop-color="#E1650B"/>
<stop offset="0.769298" stop-color="#E3711E"/>
<stop offset="0.801231" stop-color="#E2650C"/>
<stop offset="0.864351" stop-color="#F47B25"/>
<stop offset="0.91037" stop-color="#F3771F"/>
<stop offset="0.957571" stop-color="#FBD2B4"/>
</linearGradient>
<linearGradient id="paint5_linear_1780_2181" x1="39.002" y1="13.5625" x2="39.002" y2="47.5625" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7A266" stop-opacity="0.59"/>
<stop offset="0.552083" stop-color="#F59C48" stop-opacity="0.705938"/>
<stop offset="1" stop-color="#F48E2F" stop-opacity="0.8"/>
</linearGradient>
</defs>
</svg>
