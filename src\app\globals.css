@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("[locale]/love-languages/styles");
@import url("[locale]/emotional-intelligence/styles");

@property --progress {
  syntax: '<number>';
  inherits: false;
  initial-value: 0;
}

:root {
  --foreground-rgb: #eee;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --primary-color: theme('colors.primary');
  --progress: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.swal-blur > *:not(.swal2-container) {
  filter: blur(4px);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@media only screen and (min-width: 3500px) {
  body {
    zoom: 2;
    -moz-transform: scale(2);
    -moz-transform-origin: top;
  }
}

.zoom-06 {
  zoom: 0.6;
  -moz-transform: scale(0.6);
  -moz-transform-origin: top;
}

/*
https://stackoverflow.com/questions/1156278/how-can-i-scale-an-entire-web-page-with-css*/
@media only screen and (min-width: 768px) {
  .zoom-08 {
    zoom: 0.8;
    -moz-transform: scale(0.8);
    -moz-transform-origin: top;
  }
  .zoom-06 {
    zoom: 1;
    -moz-transform: scale(1);
    -moz-transform-origin: top;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #191919;
  font-weight: 600;
}

h1 {
  font-size: 68px;
  line-height: 114.706%;
  letter-spacing: -2.04px;
}

h2 {
  font-size: 52px;
  line-height: 107.692%;
  letter-spacing: -1.56px;
}

h3 {
  font-size: 32px;
  font-weight: 600;
  line-height: 112.5%;
}

h4 {
  font-size: 26px;
  line-height: 115.385%;
}

h5 {
  font-size: 20px;
  line-height: 130%;
}

h6 {
  font-size: 18px;
  line-height: 111.111%;
  letter-spacing: -0.18px;
}

p,
ul,
ol {
  font-size: 18px;
  color: #8893ac;
  line-height: 144.444%;
}

p.small {
  font-size: 16px;
  line-height: 150%;
}

@media only screen and (max-width: 768px) {
  h1,
  h2.big,
  h3.big {
    font-size: 38px;
    line-height: 126.316%;
    letter-spacing: -1.14px;
  }
  h4.big {
    font-size: 24px;
    line-height: 116.667%;
  }
  h4 {
    font-size: 20px;
    line-height: 130%;
  }
  h5,
  h4.small {
    font-size: 18px;
    line-height: 133.333%;
  }
  p,
  ul,
  ol {
    font-size: 16px;
  }
  p.small {
    font-size: 14px;
  }
}

@media only screen and (max-width: 1024px) {
  .home-hero-wave {
    -moz-transform: scale(-0.75, -0.75);
    -o-transform: scale(-0.75, -0.75);
    -webkit-transform: scale(-0.75, 0.75);
    transform: scale(-0.75, 0.75);
  }
}

.button {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  letter-spacing: -0.6px;
}

.primary {
  color: #fff;
  background: theme('colors.primary');
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 16px 40px 12px 40px;
}

.primary:hover {
  border: 1px solid theme('colors.primary');
}

.secondary {
  color: theme('colors.primary');
  /*background: #fff;*/
  border: 1px solid theme('colors.primary');
  padding: 16px 20px 12px 20px;
}

.secondary:hover {
  color: #fff;
  background: theme('colors.primary');
}

.danger {
  color: #ff2f2f;
  /*background: #fff;*/
  border: 1px solid #ff2f2f;
  padding: 16px 20px 12px 20px;
}

.danger:hover {
  color: #fff;
  background: #ff2f2f;
}


/* Checkbox */
/* The container */
.checkbox-container1 {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox-container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark1 {
  position: absolute;
  top: 0px;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border-radius: 4px !important;
}

/* On mouse-over, add a grey background color */
.checkbox-container1:hover input ~ .checkmark1 {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.checkbox-container1 input:checked ~ .checkmark1 {
  background-color: theme('colors.primary');
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark1:after {
  content: '';
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.checkbox-container1 input:checked ~ .checkmark1:after {
  display: block;
}
/* Style the checkmark/indicator */

.checkbox-container1 .checkmark1:after {
  left: 8px;
  top: 4px;
  width: 8px;
  height: 14px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

@media only screen and (max-width: 768px) {
  .primary {
    font-size: 16px;
    padding: 12px 18px;
  }
  .secondary {
    font-size: 14px;
    padding: 11px 16px;
  }
}

section {
  padding: 80px 5.69444% 80px 5.69444%;
}

@media only screen and (max-width: 500px) {
  section {
    padding: 40px 5.69444% 40px 5.69444%;
  }
}

@media only screen and (max-width: 768px) {
  #privacy .flex {
    width: 100% !important;
    margin-bottom: 40px;
  }
}

.distribution-section-list {
  width: 34%;
}

@media only screen and (max-width: 768px) {
  .distribution-section-list {
    width: 100%;
  }
}

.answer-card {
  border: 2px solid transparent;
}

.answer-card:hover {
  border: 2px solid orange;
}

#header > div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}


.cookie-policy a {
  color: theme('colors.primary');
}

.App-Overview {
  display: none !important;
}

.emotional-it-cls{
  @apply border border-[#8C36D0] bg-[#F6EFFC]
}

.gpay-btn .StripeElement{
  @apply w-full rounded-[10px]
}

.love-lang-cls{
  @apply border border-[#5DC4FF] bg-[#F0F9FF]
}