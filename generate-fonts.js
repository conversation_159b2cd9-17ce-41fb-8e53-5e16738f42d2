const fs = require("fs");
const path = require("path");
const siteConfig = require("./site.config.js").getSiteConfig();

const fontsJsContent = `
import localFont from "next/font/local";

const globalFont = localFont({
  src: ${JSON.stringify(siteConfig.fonts.src).replace(/\"(\w+)\":/g, '$1:')}
});

export default globalFont;
`;

fs.writeFileSync(path.join(__dirname, "./src/fonts.js"), fontsJsContent);

console.log("✅ Generated fonts.js with hardcoded literals for next/font/local");
