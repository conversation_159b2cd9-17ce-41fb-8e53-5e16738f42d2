import { useRef, useState, useEffect } from 'react';
import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper/modules';
import Image from 'next/image';
import StarRating from './StarRating';
import { useTranslations } from 'next-intl';

const reviews = [
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'Really effective, but I wish the subscription was cheaper.',
    date: '1 day ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 4,
    text: 'Great tool, but a bit pricey after the trial.',
    date: '2 weeks ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 4,
    text: 'Great tool, but a bit pricey after the trial.',
    date: '2 weeks ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'Really effective, but I wish the subscription was cheaper.',
    date: '1 day ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 4,
    text: 'Great tool, but a bit pricey after the trial.',
    date: '2 weeks ago',
  },
];

const ReviewSlider = () => {
  const t = useTranslations('love-languages.checkout.reviews');
  const swiperRef = useRef<SwiperRef | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    // Handle window resize event
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Set initial window width
    setWindowWidth(window.innerWidth);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handlePrevClick = () => {
    // Check if swiperRef is defined before trying to access swiper instance
    if (swiperRef.current) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNextClick = () => {
    // Check if swiperRef is defined before trying to access swiper instance
    if (swiperRef.current) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handleSlideChange = () => {
    if (swiperRef.current) {
      setCurrentSlide(swiperRef.current.swiper.realIndex); // Update current slide index
    }
  };

  const slidesPerView = windowWidth < 768 ? 1 : windowWidth < 1024 ? 2 : 3; // Adjust the number of slides based on the screen width

  return (
    <div className="w-full relative">
      <h2 className="font-raleway font-bold text-[24px] md:text-[40px] leading-[32px] md:leading-[48px] tracking-tight text-[#0E2432] mb-2">
        {t('title')}
      </h2>
      <div className="flex flex-row items-center gap-3 mb-5">
        <span
          className="text-[#6DCAFF] font-semibold font-raleway text-[28px] leading-[40px] md:text-[32px] md:leading-[56px] tracking-tight"
          style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
          4.5
        </span>
        <div className="flex text-[#6DCAFF] flex-row md:gap-1">
          <StarRating rating={4} />
        </div>
        <span
          className="font-raleway text-[#0E2432] text-[16px] md:text-[18px] leading-[20px]"
          style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
          based on <span className="font-semibold">73</span> reviews
        </span>
      </div>
      <div className="hidden md:flex flex-row absolute right-0 top-[16px] gap-3">
        <button
          className="flex flex-row justify-center items-center p-3 gap-2.5 bg-white border border-[#c1cfe9] border-opacity-45 shadow-[1px_1px_1px_1px_rgba(141,_160,_188,_0.08),_2px_8px_14px_rgba(104,_129,_177,_0.08)] rounded-full"
          onClick={handlePrevClick}>
          <Image
            width={24}
            height={24}
            className={'md:h-[24px] md:w-[24px]'}
            src={
              currentSlide === 0
                ? '/images/love-languages/prev-disabled.png'
                : '/images/love-languages/prev-enabled.png'
            }
            alt={t('alt_text.prev_disabled')}
          />
        </button>

        <button
          className="flex flex-row justify-center items-center p-3 gap-2.5 bg-white border border-[#c1cfe9] border-opacity-45 shadow-[1px_1px_1px_1px_rgba(141,_160,_188,_0.08),_2px_8px_14px_rgba(104,_129,_177,_0.08)] rounded-full"
          onClick={handleNextClick}>
          <Image
            width={24}
            height={24}
            className={'md:h-[24px] md:w-[24px]'}
            src={
              currentSlide === reviews.length - slidesPerView
                ? '/images/love-languages/next-disabled.png'
                : '/images/love-languages/next-enabled.png'
            }
            alt={t('alt_text.next_enabled')}
          />
        </button>
      </div>

      <Swiper
        ref={swiperRef}
        slidesPerView={1}
        spaceBetween={20}
        pagination={{ clickable: true }}
        navigation={false}
        modules={[Pagination, Navigation]}
        breakpoints={{
          640: { slidesPerView: 1 },
          768: { slidesPerView: 2 },
          1024: { slidesPerView: 3 },
        }}
        onSlideChange={handleSlideChange}>
        {reviews.map((review, index) => (
          <SwiperSlide
            key={index}
            className="flex flex-col justify-between bg-white shadow-[0px_4px_12px_rgba(0,_34,_45,_0.08)] rounded-[12px] p-6">
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-2">
                <span className="font-raleway font-bold md:text-[20px] text-[#0E2432] md:leading-[24px]">
                  {review.name}
                </span>
                <div className="flex items-center gap-1">
                  <StarRating rating={review.rating} />
                </div>
              </div>
              {review.verified && (
                <span className="text-[#6DCAFF] flex items-center md:mb-5">
                  <Image
                    width={20}
                    height={20}
                    className={'md:h-[20px] md:w-[20px] md:mr-2'}
                    src="/images/love-languages/check-circle.png"
                    alt={t('alt_text.check_circle')}
                  />
                  <span className="font-raleway font-medium md:text-[16px] text-[#0E2432] md:leading-[24px]">
                    {t('verified_customer')}
                  </span>
                </span>
              )}
              <p className="font-raleway font-medium text-[#828E98] md:text-[18px] md:leading-[24px] mb-4">
                {review.text}
              </p>
            </div>
            <div
              className="font-raleway md:text-[16px] md:leading-[20px] w-full text-right text-sm text-[#828E98]"
              style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
              {review.date}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default ReviewSlider;
