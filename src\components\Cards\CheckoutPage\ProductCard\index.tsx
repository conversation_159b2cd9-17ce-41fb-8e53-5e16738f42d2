import { memo } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

const ProductCard = ({ product: { title, text, img }, index, showOrdinals }: { product: any; index: number, showOrdinals: boolean }) => {
  const t = useTranslations('checkout.product_card');

  return (
    <div className='flex flex-wrap md:flex-nowrap bg-grey-bg p-5 mx-4 md:mx-0 mb-2 w-full'>
      {showOrdinals && 
        <div className='flex justify-center items-center pl-1 pr-6 text-[36px] md:text-[40px] text-light-grey-text font-semibold md:w-[53px]'>
          {index}
        </div>
      }
      <div className='order-1 md:order-none flex flex-col justify-between n-xl:min-w-[600px]'>
        <h5 className='pb-2'>{title}</h5>
        <p className=''>
          {text}
          {img.includes('report') && (
            <a
              style={{ color: '#191919', textDecoration: 'underline' }}
              target='_blank'
              href='https://storage.googleapis.com/iq-test-results/example/report.pdf'
            >
              {t('sample_report')}
            </a>
          )}
        </p>
      </div>
      <div className='w-[calc(100%-148px)] md:w-[30%] lg:w-[15%] n-md:hidden'></div>
      <div className='flex items-center'>
        <Image
          quality={100}
          src={img}
          alt={t('alt_text', { title })}
          width={img.includes('certificate') ? 112 : 98}
          height={82}
          style={{
            boxShadow: '0px 14px 18px -6px rgba(59, 104, 145, 0.05), 0px 12px 18px -12px rgba(54, 97, 136, 0.16)',
            marginLeft: index === 1 ? 8 : index === 2 ? -13 : 0,
          }}
          className={`max-w-none fit-content ${index === 3 ? 'md:!ml-[18px]' : ''}`}
          priority
        />
      </div>
    </div>
  );
};

export default memo(ProductCard);
