import { useContext, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import SessionContext from '@/store/SessionContext';
import { getSubscriptionPlan } from '@/utils/getSubscriptionPlan';
import { PlanTypeEnum } from '@/types/plan-types';

export function useSetSubscriptionPlan() {
  const searchParams = useSearchParams();
  const { updatePlanType, updatePlan } = useContext(SessionContext);

  // Set subscription plan using callback to prevent re-rendering
  const setSubscriptionPlan = useCallback(() => {
    const sp = searchParams.get('sp');

    // If sp is present, update price plan code in localStorage
    if (sp) {
      const plan = getSubscriptionPlan(sp);
      updatePlan(plan);
      updatePlanType(PlanTypeEnum.Subscription);
    }
    // If p is not present, do not touch localStorage
  }, [searchParams, updatePlan]);

  return { setSubscriptionPlan };
}
