.accordion {
  color: #191919;
  cursor: pointer;
  width: 100%;
  text-align: left;
  outline: none;
  font-size: 15px;
  transition: 0.4s;
  max-width: 736px;
  font-size: 20px;
  font-weight: 600;
  line-height: 130%;
  display: flex;
  justify-content: space-between;
}

.panel {
  background-color: transparent;
  max-height: 0;
  overflow: hidden;
  border-bottom: 1px solid rgba(193, 207, 233, 0.45);
  transition: max-height 0.2s ease-out;
}

.chevron {
  content: '\276F';
  display: inline-block;
  width: 1em;
  height: 1em;
  text-align: center;
  transform: rotate(90deg);
  transition: all 0.35s;
}
