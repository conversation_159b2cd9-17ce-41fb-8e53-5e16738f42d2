import React from 'react';
import { useTranslations } from 'next-intl';
import type { PaymentWithComplianceProps } from '@/types/payment';
import { SubscriptionPlanEnum } from '@/types/plan-types';
import { getFormattedSubscriptionPrice } from '@/utils/getSubscriptionPlan';

const CompliantTrialText: React.FC<PaymentWithComplianceProps> = ({ compliantVersion, isTrial, prices, plan }) => {
  if (!compliantVersion || !isTrial) return null;

  const t = useTranslations('checkout.payment_details.form');
  const ct = useTranslations('common.per_period');
  const vatText = prices.vatIncluded ? t('vat_included') : '';

  const values: Record<string, string> = {
    subscriptionPrice: getFormattedSubscriptionPrice(prices, plan),
    vatIncluded: vatText,
    period: plan === SubscriptionPlanEnum.Weekly ? ct('week') : ct('month'),
    trialPrice: prices.trial.formatted,
  };

  return <p className="text-sm mb-6">{t('trial_pricing_text', values)}</p>;
};

export default CompliantTrialText;
