'use client';

import { FC } from 'react';

interface ConsentButtonProps {
  onClick: () => void;
  className?: string;
  type?: 'primary' | 'secondary';
  style?: any;
  text?: string;
}

const ConsentButton: FC<ConsentButtonProps> = ({ onClick, className, type, style, text = 'Accept' }) => {
  return (
    <button
      onClick={onClick}
      className={`button ${type} ${className} text-${type === 'primary' ? 'base' : 'sm'} md:text-xl font-semibold`}
      style={{
        display: 'inline-flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        borderRadius: 10,
        lineHeight: '120%',
        ...style,
      }}
    >
      {text}
    </button>
  );
};

export default ConsentButton;
