import Image from 'next/image';

interface HelpItemProps {
  imgSource: string
  text: string
}

export default function HelpItem({ imgSource, text }: HelpItemProps) {
  return (
    <div className="flex flex-row items-center gap-2">
      <Image 
        src={`/images/checkout-v3/${imgSource}.svg`} 
        width={24} 
        height={24} 
        className="w-6 h-6" alt="tick" 
      />
      <div className="font-segoe font-normal text-[14px] leading-[21px] tracking-[0px] text-[#3F3F46]">{text}</div>
    </div>
  );
}