import { getClickId } from "./getClickId";

/**
 * Extracts the click ID from the runtime (e.g., window.clickflare) or from localStorage's landing URL.
 */
export const getClickIdFromStorage = (): string | null => {
    const clickId = getClickId();
    if (clickId) return clickId;
  
    // Fallback to localStorage landing URL
    if (typeof localStorage === 'undefined') return null;
  
    const landingUrl = localStorage.getItem('landingUrl');
    if (!landingUrl || landingUrl === 'null') return null;
  
    try {
      const parsedUrl = new URL(landingUrl);
      return parsedUrl.searchParams.get('click_id');
    } catch {
      return null;
    }
  };
  