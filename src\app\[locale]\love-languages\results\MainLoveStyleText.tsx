export const mainlovetext: { [key: string]: string } = {
    "Caring Gestures": "You feel most loved when your partner eases your daily burdens. Whether it’s helping with chores, running errands, or taking care of a task, these acts show you they truly care.",
    "Deep Understanding": "You feel most loved when your partner truly understands you. It’s the little things, like anticipating your needs or recognizing your feelings without words, that make you feel known and valued.",
    "Loving Words": "You feel most loved when your partner expresses their appreciation through words. Compliments, encouragement, and heartfelt messages mean the world to you, as they reinforce your value in their life.",
    "Meaningful Gifts": "You feel most loved when your partner gives you thoughtful gifts. It’s not about the cost, but the meaning behind the gesture—each gift reflects the care and attention they put into knowing what makes you happy.",
    "Nourishing Care": "You feel most loved when your partner prepares something special for you. Whether it’s a meal, a snack, or a favorite treat, these gestures show that they’re thinking of you and care about your well-being.",
    "Shared Experiences": "You feel most loved when your partner spends quality time with you. Whether it’s a shared adventure or a cozy night in, these moments strengthen your bond and create lasting memories.",
    "Touch of Affection": "You feel most loved when your partner is physically affectionate. Holding hands, hugs, and cuddles give you a sense of comfort and connection, making you feel secure in your relationship.",
}
