'use client';

import { useTranslations } from 'next-intl';
import requireAuth from '@/components/requireAuth';
import { Link } from '@/lib/i18n/routing';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';
import { PostHogEventEnum } from '@/store/types';

function Trainings() {
  const t = useTranslations('members.training');
  const { captureEvent } = usePostHogAnalytics();
  const { isSubscribed } = useSubscriptionPlan();

  return (
    <div className="flex flex-col w-full items-center m-auto">
      <h3 className="my-10">{t('title')}</h3>
      {isSubscribed && (
        <div className="flex gap-2">
          <Link
            className="secondary rounded-[10px]"
            href="/training/analytical"
            onClick={() => captureEvent(PostHogEventEnum.GO_TO_ANALYTICS_TRAINING_PAGE, {})}>
            {t('training_types.analytical')}
          </Link>
          <Link
            className="secondary rounded-[10px]"
            href="/training/pattern"
            onClick={() => captureEvent(PostHogEventEnum.GO_TO_PATTERN_TRAINING_PAGE, {})}>
            {t('training_types.pattern')}
          </Link>
          <Link
            className="secondary rounded-[10px]"
            href="/training/visual"
            onClick={() => captureEvent(PostHogEventEnum.GO_TO_VISUAL_TRAINING_PAGE, {})}>
            {t('training_types.visual')}
          </Link>
        </div>
      )}
    </div>
  );
}

export default requireAuth(Trainings, ['is_subscribed']);
