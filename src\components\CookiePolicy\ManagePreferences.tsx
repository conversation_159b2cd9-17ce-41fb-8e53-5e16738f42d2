'use client';

import { useTranslations } from 'next-intl';

const ManagePreferences = () => {
  const t = useTranslations('cookie_policy');

  return (
    <div className="manage-preferences-section">
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        {t('sections.manage_preferences.title')}
      </h3>
      <div className='mt-4 mb-4'>
        <a
          className='cky-banner-element button primary'
          style={{
            display: 'inline-flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            borderRadius: 10,
            lineHeight: '120%',
            color: '#fff',
            cursor: 'pointer',
          }}
        >
          {t('sections.manage_preferences.cookie_settings_button')}
        </a>
      </div>
      <div>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.paragraph1')}
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.paragraph2')}
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.chrome_browser')}:{' '}
          <a href='https://support.google.com/accounts/answer/32050' target='_blank'>
            https://support.google.com/accounts/answer/32050
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.safari_browser')}:{' '}
          <a href='https://support.apple.com/en-in/guide/safari/sfri11471/mac' target='_blank'>
            https://support.apple.com/en-in/guide/safari/sfri11471/mac
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.firefox_browser')}:{' '}
          <a
            href='https://support.mozilla.org/en-US/kb/clear-cookies-and-site-data-firefox?redirectslug=delete-cookies-remove-info-websites-stored&redirectlocale=en-US'
            target='_blank'
          >
            https://support.mozilla.org/en-US/kb/clear-cookies-and-site-data-firefox?redirectslug=delete-cookies-remove-info-websites-stored&redirectlocale=en-US
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.ie_browser')}:{' '}
          <a
            href='https://support.microsoft.com/en-us/topic/how-to-delete-cookie-files-in-internet-explorer-bca9446f-d873-78de-77ba-d42645fa52fc'
            target='_blank'
          >
            https://support.microsoft.com/en-us/topic/how-to-delete-cookie-files-in-internet-explorer-bca9446f-d873-78de-77ba-d42645fa52fc
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.manage_preferences.other_browsers')}
        </p>
      </div>
      &nbsp;
    </div>
  );
};

export default ManagePreferences; 