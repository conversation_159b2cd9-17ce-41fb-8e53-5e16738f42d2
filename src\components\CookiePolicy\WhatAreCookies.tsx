'use client';

import { useTranslations } from 'next-intl';

const WhatAreCookies = () => {
  const t = useTranslations('cookie_policy');

  return (
    <div className="what-are-cookies-section">
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        {t('sections.what_are_cookies.title')}
      </h3>
      <div className='cookie-policy-p'>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.what_are_cookies.paragraph1')}
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          {t('sections.what_are_cookies.paragraph2')}
        </p>
      </div>
      &nbsp;
    </div>
  );
};

export default WhatAreCookies; 