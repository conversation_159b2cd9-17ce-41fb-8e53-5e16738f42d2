import { useTranslations } from 'next-intl';

// Define the hook to get the questions from translations
export const useQuestions = () => {
  const t = useTranslations('love-languages.test');
  
  // Get the questions from translations
  const questions = t.raw('questions');

  return questions || [];
};

// For backward compatibility, you can also export the questions directly
// but it's better to use the hook above in your components
export const getQuestions = (t) => {
  return t.raw('love-languages.test.questions') || [];
};