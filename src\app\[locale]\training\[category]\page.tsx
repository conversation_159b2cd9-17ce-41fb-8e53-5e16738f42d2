'use client';

import { useContext, useState } from 'react';
import { useTranslations } from 'next-intl';
import TrainingQuestion from '@/components/TrainingQuestion';
import requireAuth from '@/components/requireAuth';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';
import { Link, useRouter } from '@/lib/i18n/routing';
import { UserContext } from '@/store/UserContext';

function TrainingCategory({ params: { category } }: { params: { category: string } }) {
  const t = useTranslations('members.training.category_page');
  const router = useRouter();
  const [currentQuestionId, setCurrentQuestionId] = useState<number>(1);
  const [answers, setAnswers] = useState<{ [id: number]: number }>({});
  const { trainingQuestions: questions } = useContext(UserContext);
  const { isSubscribed } = useSubscriptionPlan();

  const back = () => {
    if (currentQuestionId == 1) router.push('/training');
    else setCurrentQuestionId(currentQuestionId - 1);
  };

  const next = () => {
    setCurrentQuestionId(currentQuestionId + 1);
  };

  const selectAnswer = (answer_id: number) => {
    setAnswers({ ...answers, [currentQuestionId]: answer_id });
  };

  const restartTraining = () => {
    setCurrentQuestionId(1);
    setAnswers({});
  };

  if (!questions || !questions[category]) return null;

  return (
    isSubscribed && (
      <div className="flex flex-col mt-24">
        <h3 className="w-full text-center font-medium">
          {t('title')}: <span className="font-semibold">{category.slice(0, 1).toUpperCase() + category.slice(1)}</span>
        </h3>
        {currentQuestionId > questions[category].length && (
          <>
            <h5 className="mt-10 text-center">
              {t('your_score')}: {questions[category].filter(x => answers[x.id] == x.correctAnswerId).length}/
              {questions[category].length}
            </h5>
            <button
              onClick={restartTraining}
              className="button primary text-base font-semibold mb-5 rounded-lg mt-10 text-center">
              {t('restart_training')}
            </button>
            <Link className="button secondary text-base font-semibold mb-5 rounded-lg text-center" href="/training/">
              {t('go_back_to_trainings')}
            </Link>
          </>
        )}
        {currentQuestionId <= questions[category].length && (
          <TrainingQuestion
            category={category}
            questionId={currentQuestionId}
            back={back}
            next={next}
            selectAnswer={selectAnswer}
          />
        )}
      </div>
    )
  );
}

export default requireAuth(TrainingCategory, ['is_subscribed']);
