<svg width="375" height="960" viewBox="0 0 375 960" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1140_9491)">
<rect width="375" height="960" fill="url(#paint0_linear_1140_9491)"/>
<g filter="url(#filter0_f_1140_9491)">
<ellipse cx="164" cy="908.5" rx="107" ry="106.5" fill="#D1FFF1" fill-opacity="0.48"/>
</g>
<g filter="url(#filter1_f_1140_9491)">
<circle cx="357" cy="206" r="126" fill="#FDF8EA"/>
</g>
<g filter="url(#filter2_f_1140_9491)">
<circle cx="9.5" cy="113.5" r="106.5" fill="white"/>
</g>
<g filter="url(#filter3_f_1140_9491)">
<ellipse cx="457.5" cy="356" rx="98.5" ry="106" fill="#EAF4FD"/>
</g>
</g>
<defs>
<filter id="filter0_f_1140_9491" x="-143" y="602" width="614" height="613" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1140_9491"/>
</filter>
<filter id="filter1_f_1140_9491" x="-19" y="-170" width="752" height="752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="125" result="effect1_foregroundBlur_1140_9491"/>
</filter>
<filter id="filter2_f_1140_9491" x="-347" y="-243" width="713" height="713" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="125" result="effect1_foregroundBlur_1140_9491"/>
</filter>
<filter id="filter3_f_1140_9491" x="159" y="50" width="597" height="612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1140_9491"/>
</filter>
<linearGradient id="paint0_linear_1140_9491" x1="187.5" y1="0" x2="187.5" y2="960" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6F9FF" stop-opacity="0.3"/>
<stop offset="1" stop-color="#F6F9FF"/>
</linearGradient>
<clipPath id="clip0_1140_9491">
<rect width="375" height="960" fill="white"/>
</clipPath>
</defs>
</svg>
