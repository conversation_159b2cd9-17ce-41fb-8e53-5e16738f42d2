'use client';

import React from 'react';
import { StripeInvoice, SolidgateInvoice } from '@/components/Invoices';
import requireAuth from '@/components/requireAuth';

const Invoice = () => {
  return (
    <div className="flex flex-col items-center w-full mt-5 2xl:mt-10 px-[5%] md:px-[5.69444%]">
      <div className="mt-[150px]">
        <StripeInvoice />
        <SolidgateInvoice />
      </div>
    </div>
  );
};

export default requireAuth(Invoice, []);
