import { useTranslations } from 'next-intl';
import React from 'react';
import { BasePaymentProps } from '@/types/payment';
import { getFormattedOneTimePrice, getFormattedSubscriptionPrice } from '@/utils/getSubscriptionPlan';

const PriceTotalText: React.FC<BasePaymentProps> = ({ isTrial, isOneTime, prices, plan }) => {
  const t = useTranslations('checkout.payment_details.form');
  const price = isOneTime ? getFormattedOneTimePrice(prices) : getFormattedSubscriptionPrice(prices, plan);

  return (
    <div className="flex justify-between bg-grey-bg p-5 mx-4 md:mx-0 mb-4">
      <h5 className="items-center">{t('total')}</h5>
      <h5>{isTrial ? prices.trial.formatted : price}</h5>
    </div>
  );
};

export default PriceTotalText;
