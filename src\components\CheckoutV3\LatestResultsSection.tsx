'use client';

import { useTranslations } from 'next-intl';
import LatestResultPart from '@/components/LatestResultPart';

interface Person {
  name: string;
  image: string;
  IQ: number;
}

interface LatestResultsSectionProps {
  people: Array<Person>;
}

export default function LatestResultsSection({ people }: LatestResultsSectionProps) {
  const t = useTranslations('checkout_v2.latest_results');

  return (
    <div className="max-w-[1276px] mx-auto pt-16 pb-24">
      <div className="px-5 md:px-0">
          <h1 className="text-[52px] text-center font-bold tracking-tight leading-[56px] pb-20 text-[#191919]">
            {t('title')}
          </h1>
          <div>
            <div className="md:flex w-full">
                {people[0] && (
                <LatestResultPart
                  image={people[0].image}
                  name={people[0].name}
                  iq={people[0].IQ}
                  mobileColored={false}
                  isFirst={true}
                />
                )}
                {people[2] && (
                <LatestResultPart
                  image={people[2].image}
                  name={people[2].name}
                  iq={people[2].IQ}
                  mobileColored={true}
                  isFirst={false}
                />
                )}
            </div>
            <div className="md:flex rounded-md md:bg-[#F6F9FF]">
                {people[1] && (
                <LatestResultPart
                  image={people[1].image}
                  name={people[1].name}
                  iq={people[1].IQ}
                  mobileColored={false}
                  isFirst={false}
                />
                )}
                {people[3] && (
                <LatestResultPart
                  image={people[3].image}
                  name={people[3].name}
                  iq={people[3].IQ}
                  mobileColored={true}
                  isFirst={false}
                />
                )}
            </div>
            <div className="md:flex">
                {people[4] && (
                <LatestResultPart
                  image={people[4].image}
                  name={people[4].name}
                  iq={people[4].IQ}
                  mobileColored={false}
                  isFirst={false}
                  twoMins={true}
                />
                )}
                {people[6] && (
                <LatestResultPart
                  image={people[6].image}
                  name={people[6].name}
                  iq={people[6].IQ}
                  mobileColored={true}
                  isFirst={false}
                  twoMins={true}
                />
                )}
            </div>
            <div className="md:flex rounded-md md:bg-[#F6F9FF]">
                {people[5] && (
                <LatestResultPart
                  image={people[5].image}
                  name={people[5].name}
                  iq={people[5].IQ}
                  mobileColored={false}
                  isFirst={false}
                  twoMins={true}
                />
                )}
                {people[7] && (
                <LatestResultPart
                  image={people[7].image}
                  name={people[7].name}
                  iq={people[7].IQ}
                  mobileColored={true}
                  isFirst={false}
                  twoMins={true}
                />
                )}
            </div>
          </div>
      </div>
    </div>
  );
} 