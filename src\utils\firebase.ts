import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import firebaseConfig from '@/config/firebase.config';

const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);
const functions = getFunctions(app, 'europe-west2');

if (process.env.NEXT_PUBLIC_CONNECT_FUNCTIONS_EMULATOR === 'true') {
  const host = process.env.NEXT_PUBLIC_FUNCTIONS_EMULATOR_HOST || '127.0.0.1';
  const port = Number(process.env.NEXT_PUBLIC_FUNCTIONS_EMULATOR_PORT) || 5001;

  connectFunctionsEmulator(functions, host, port);
}

export { app, firestore, auth, storage, functions };
