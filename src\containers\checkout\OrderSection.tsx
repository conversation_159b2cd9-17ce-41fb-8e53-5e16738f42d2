import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import PaymentTagSection from '@/containers/checkout/PaymentTagSection';
import ProductCard from '@/components/Cards/CheckoutPage/ProductCard';
import SessionContext from '@/store/SessionContext';
import { SubscriptionPlanEnum } from '@/types/plan-types';
import { getFormattedSubscriptionPrice } from '@/utils/getSubscriptionPlan';

interface OrderSectionProps {
  compliantVersion: boolean;
  isTrial: boolean;
  isOneTime: boolean;
}

const OrderSection = ({ compliantVersion, isTrial, isOneTime }: OrderSectionProps) => {
  const t = useTranslations('checkout.order_section');
  const { prices, plan } = useContext(SessionContext);
  const price = getFormattedSubscriptionPrice(prices, plan);

  const products = [
    {
      title: t('products.iq_score.title'),
      text: t('products.iq_score.text'),
      img: '/checkout/score.svg',
    },
    {
      title: t('products.certificate.title'),
      text: t('products.certificate.text'),
      img: '/checkout/certificate.png',
    },
    {
      title: t('products.report.title'),
      text: t('products.report.text'),
      img: '/checkout/report.png',
    },
    ...(compliantVersion && isTrial && !isOneTime
      ? [
          {
            title: t('products.trial.title', { price: prices.trial.formatted }),
            text: t('products.trial.period_text', {
              trialPrice: prices.trial.formatted,
              subscriptionPrice: price,
              vatText: prices.vatIncluded ? ` ${t('products.trial.vat_included')}` : '',
              period:
                plan === SubscriptionPlanEnum.Weekly
                  ? t('products.trial.period.week')
                  : t('products.trial.period.month'),
            }),
            img: '/checkout/percent.png',
          },
        ]
      : []),
  ];

  return (
    <div className="">
      <h4 className="text-center md:text-left mb-4 md:mb-6">{t('title')}</h4>
      <ul className="flex flex-wrap">
        {products.map((product, index) => (
          <ProductCard key={index} {...{ product, index: index + 1, showOrdinals: !compliantVersion }} />
        ))}
      </ul>
      <div className="hidden md:block w-full">
        <PaymentTagSection />
        <hr />
      </div>
    </div>
  );
};

export default OrderSection;
