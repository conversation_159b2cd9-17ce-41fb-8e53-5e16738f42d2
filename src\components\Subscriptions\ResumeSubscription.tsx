'use client';

import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import { httpsCallable } from 'firebase/functions';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';
import { auth, functions } from '@/utils/firebase';
import { PaymentProvider } from '@/store/types';

interface ResumeSubscriptionProps {
  onProcessing: (loading: boolean) => void;
}

export default function ResumeSubscription({ onProcessing }: ResumeSubscriptionProps) {
  const t = useTranslations('members.subscription');
  const { user, canResumeSubscription } = useSubscriptionPlan();

  /**
   * Resumes the user's subscription.
   */
  async function resumeSubscription(): Promise<void> {
    if (!user || !canResumeSubscription) return;

    onProcessing(true);

    try {
      await httpsCallable(
        functions,
        user.activeSubscriptionType === PaymentProvider.SOLIDGATE ? 'resumeSubscriptionSolidgate' : 'resumeSubscription'
      )();

      // Force refresh ID token to reflect subscription status change
      if (auth.currentUser) {
        await auth.currentUser.getIdToken(true);
      }
    } catch (error) {
      console.log('Error while resuming subscription:', error);
    } finally {
      onProcessing(false);
    }
  }

  if (!canResumeSubscription || !user) {
    return null;
  }

  return (
    <>
      <span className="text-lg text-black mb-2">
        {t('scheduled_cancel_message', { date: dayjs(user.scheduled_cancel_at).format('LLL') })}
      </span>
      <button onClick={resumeSubscription} className="primary rounded-lg mt-2">
        {t('btn_resume_subscription')}
      </button>
    </>
  );
}
