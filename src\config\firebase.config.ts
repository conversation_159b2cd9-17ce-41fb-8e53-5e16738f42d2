import { z } from 'zod';

/**
 * Zod schema for validating all required Firebase and files bucket environment variables.
 * All keys are top-level and match the expected usage in the app.
 */
const Schema = z.object({
  /** Firebase API key */
  apiKey: z.string().min(1, 'NEXT_PUBLIC_FB_API_KEY is required'),
  /** Firebase Auth domain */
  authDomain: z.string().min(1, 'NEXT_PUBLIC_FB_AUTH_DOMAIN is required'),
  /** Firebase Project ID */
  projectId: z.string().min(1, 'NEXT_PUBLIC_FB_PROJECT_ID is required'),
  /** Firebase Storage bucket */
  storageBucket: z.string().min(1, 'NEXT_PUBLIC_FB_STORAGE_BUCKET is required'),
  /** Firebase Messaging sender ID */
  messagingSenderId: z.string().min(1, 'NEXT_PUBLIC_FB_MESSAGING_SENDER_ID is required'),
  /** Firebase App ID */
  appId: z.string().min(1, 'NEXT_PUBLIC_FB_APP_ID is required'),
  /** Public files bucket URL (used for reports, certificates, etc.) */
  filesBucket: z.string().url({ message: 'NEXT_PUBLIC_FILES_BUCKET must be a valid URL' }),
});

/**
 * The validated and parsed Firebase configuration object.
 * All properties are guaranteed to be present and valid.
 */
const firebaseConfig = Schema.parse({
  apiKey: process.env.NEXT_PUBLIC_FB_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FB_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FB_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FB_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FB_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FB_APP_ID,
  filesBucket: process.env.NEXT_PUBLIC_FILES_BUCKET,
});

/**
 * The public files bucket URL, validated and ready for use.
 * Use this for constructing links to reports, certificates, etc.
 */
export const filesBucket: string = firebaseConfig.filesBucket;

/**
 * The default export is the full validated config object.
 * Use this for initializing Firebase or accessing any config property.
 */
export default firebaseConfig;
