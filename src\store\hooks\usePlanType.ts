import { useState } from 'react';
import lscache from 'lscache';
import { PlanType, PlanTypeEnum } from '@/types/plan-types';

/**
 * React hook to manage the current plan type.
 *
 * Initializes the planType state from local storage (key: 'se-cp_pt') if available,
 * otherwise defaults to PlanTypeEnum.Subscription.
 *
 * @example
 * const { planType, setPlanType } = usePlanType();
 * setPlanType(PlanTypeEnum.Onetime);
 */
export function usePlanType() {
  const [planType, setPlanType] = useState<PlanType>(lscache.get('se-cp_pt') || PlanTypeEnum.Subscription);
  return { planType, setPlanType };
}
