import { sendGTMEvent } from '@next/third-parties/google';
import { httpsCallable } from 'firebase/functions';
import { useContext } from 'react';
import { UAParser } from 'ua-parser-js';

import { Prices } from '@/app/prices';
import { usePaymentIntentAttempts } from '@/hooks/usePaymentIntentAttempts';
import { COMPLIANCE_CHECKOUT_VERSION, COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';
import { functions } from '@/utils/firebase';
import getLandingUrl from '@/utils/getLandingUrl';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import SessionContext from '@/store/SessionContext';
import { FormData, PaymentProvider } from '@/store/types';
import { FixedPlanDuration, PlanType, PlanTypeEnum } from '@/types/plan-types';
import { PaymentIntentContext } from '@/types/payment';
import { StripePlanMetadata } from '@/types/stripe-types';
import { getPlanName } from '@/utils/getPlanName';
import { getPlanCurrency } from '@/utils/getSubscriptionPlan';

// Helper to get device type string
const getDeviceTypeString = (parser: UAParser) => {
  const device = parser.getDevice();
  return [device.type, device.model, device.vendor].filter(Boolean).join(' ');
};

// Helper to get device browser string
const getDeviceBrowser = (parser: UAParser) => {
  const browser = parser.getBrowser();
  return [browser.name, browser.version].filter(Boolean).join(' ');
};

// Helper to get device OS string
const getDeviceOS = (parser: UAParser) => {
  return parser.getOS().name || '';
};

// Helper to get common payload properties
const getPaymentIntentPayload = (
  formData: FormData,
  sessionId: string,
  prices: Prices,
  checkoutVersion: string,
  parser: UAParser,
  clickId: string | null,
  time: number,
  isTrial: boolean = true
): StripePlanMetadata => ({
  email: formData.email,
  name: formData.name,
  sessionId,
  clickId,
  landingUrl: getLandingUrl(),
  pageType: 'IQ',
  country: prices.country,
  completionTime: time,
  checkoutVersion: time < COMPLIANCE_TIME_THRESHOLD ? COMPLIANCE_CHECKOUT_VERSION : checkoutVersion,
  isTrial,
  deviceType: getDeviceTypeString(parser),
  deviceOS: getDeviceOS(parser),
  browser: getDeviceBrowser(parser),
});

/**
 * Use stripe merchant hook
 *
 * @returns
 */
const useStripeMerchant = () => {
  const { formData, sessionId, prices, merchantData, updateMerchantData } = useContext(SessionContext);
  const { checkoutVersion, paymentSystem, plan } = useContext(SessionContext);
  const parser = new UAParser();

  // Use the shared hook for payment intent attempts
  const { attempts, incrementAttempts, maxAttempts } = usePaymentIntentAttempts();

  // Shared function for creating payment intent and handling tracking/merchant data
  const handlePaymentIntent = async (
    payload: StripePlanMetadata,
    paymentType: PlanType = PlanTypeEnum.Subscription,
    clickId?: string | null
  ) => {
    // Select the correct Firebase function based on payment type
    const createPaymentIntentFn =
      paymentType === PlanTypeEnum.Onetime
        ? httpsCallable(functions, 'createOneTimePaymentIntent')
        : httpsCallable(functions, 'createPaymentIntent');
    const trackingPostback = httpsCallable(functions, 'trackingPostback');

    // If max attempts reached, use the existing clientSecret from merchantData (from context)
    if (paymentSystem === PaymentProvider.STRIPE && attempts >= maxAttempts) {
      // If merchantData (which is the clientSecret) exists and is a string, return it; otherwise, proceed to create a new payment intent
      if (merchantData && typeof merchantData === 'string') {
        return merchantData;
      }
      // If no clientSecret is available, fall through to try creating a new one (should be rare)
    }

    try {
      // Call the backend to create a new payment intent
      const paymentIntent: any = await createPaymentIntentFn(payload);
      const clientSecret = paymentIntent.data.clientSecret as string;

      if (clientSecret) {
        // Track the event if clickId is present
        if (clickId) trackingPostback({ clickId, event: 'cho' });
        // Optionally send GTM event if initialized
        if (isGTMInitialized()) sendGTMEvent({ leadsUserData: { email: formData.email } });
        // Update merchant data in context and local storage
        updateMerchantData(clientSecret);
        // Increment the attempts counter in state and local storage
        incrementAttempts();
        return clientSecret;
      }

      // If no clientSecret is returned, return null
      return null;
    } catch (error) {
      // Log and return null on error
      console.error('Error getting Stripe client secret:', error);
      return null;
    }
  };

  // For subscription plans
  const getSubscriptionMerchantData = async (context: PaymentIntentContext) => {
    const { clickId, time, isTrial, isOneTime } = context;

    return handlePaymentIntent(
      {
        ...getPaymentIntentPayload(formData, sessionId, prices, checkoutVersion, parser, clickId, time, isTrial),
        currency: getPlanCurrency(!!isOneTime, prices),
        planType: PlanTypeEnum.Subscription,
        plan: getPlanName(PlanTypeEnum.Subscription, plan),
      },
      PlanTypeEnum.Subscription,
      clickId
    );
  };

  // For onetime/fixed plans
  const getOnetimeMerchantData = async (context: PaymentIntentContext, duration: FixedPlanDuration) => {
    const { clickId, time, isOneTime } = context;
    return handlePaymentIntent(
      {
        ...getPaymentIntentPayload(formData, sessionId, prices, checkoutVersion, parser, clickId, time, false),
        currency: getPlanCurrency(!!isOneTime, prices),
        planType: PlanTypeEnum.Onetime,
        plan: getPlanName(PlanTypeEnum.Onetime, plan),
        duration,
      },
      PlanTypeEnum.Onetime,
      clickId
    );
  };

  return { getSubscriptionMerchantData, getOnetimeMerchantData };
};

export default useStripeMerchant;
