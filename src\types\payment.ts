import { Prices } from '@/app/prices';
import { PaymentProvider } from '@/store/types';
import { SubscriptionPlan } from '@/types/plan-types';

export interface PaymentIntentContext {
  provider?: PaymentProvider;
  clickId: string | null;
  time: number;
  isTrial?: boolean;
  isOneTime?: boolean;
}

export interface PaymentContextProps {
  context: PaymentIntentContext;
}

/**
 * Common props for payment display components.
 */
export interface BasePaymentProps {
  provider?: PaymentProvider;
  prices: Prices;
  plan: SubscriptionPlan;
  isTrial?: boolean;
  isOneTime?: boolean;
}

/**
 * Props for payment components that require compliance info.
 */
export interface PaymentWithComplianceProps extends BasePaymentProps {
  compliantVersion: boolean;
}
