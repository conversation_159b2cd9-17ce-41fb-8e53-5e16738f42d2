import { memo } from 'react';
import Image from 'next/image';

const TestimonialCard = ({ testimonial: { title, text } }: { testimonial: { title: string; text: string } }) => {
  return (
    <div className='w-full md:w-[calc(50%-10px)] xl:w-[calc(50%-10px)] bg-white mr-2 mb-4'>
      <div className='m-5 mb-6'>
        <Image src={`/checkout/testimonial/quote-sign.svg`} alt={`Quote sign`} width={36} height={24} priority />
      </div>
      <div className='m-5 mb-6 flex flex-wrap gap-2'>
        <h6>{title}</h6>
        <p className='small'>{text}</p>
      </div>
    </div>
  );
};

export default memo(TestimonialCard);
