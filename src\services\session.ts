import { httpsCallable } from 'firebase/functions';
import { SaveSessionResponse } from '@/store/types';
import { functions } from '@/utils/firebase';

/**
 * Saves form data to the session database using Firebase callable function.
 *
 * @param sessionData - Data to be saved in the session.
 * @returns A boolean indicating success.
 * @throws Error if the session save fails.
 */
export const addFormDataToSessionDb = async (sessionData: any): Promise<boolean> => {
  try {
    const saveSession = httpsCallable(functions, 'saveSession');
    const { data } = (await saveSession(sessionData)) as SaveSessionResponse;

    if (!data.success) {
      throw new Error('Failed to save session data to the database.');
    }

    return true;
  } catch (error) {
    console.log('Error saving session data to the database.', error);
    return false;
  }
};
