'use client';

import '@/sass/form.scss';
import { useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { z } from 'zod';
import { functions } from '@/utils/firebase';

export default function ForgotPassword() {
  const t = useTranslations('forgot_password');
  const [processing, setProcessing] = useState(false);
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');

  async function sendResetLink() {
    setProcessing(true);
    const isValidEmail = z.string().email().safeParse(email).success;
    if (!isValidEmail) {
      setMessage(t('valid_email_required'));
      setProcessing(false);
      return;
    }

    const sendEmail = httpsCallable(functions, 'sendPasswordResetEmail');
    const result: any = await sendEmail({ email });
    setMessage(result.data.success ? t('reset_link_sent') : result.data.message);
    setProcessing(false);
  }

  return (
    <div className="flex flex-col justify-center items-center">
      <h3>{t('title')}</h3>
      <form id="msform" className="flex flex-col justify-center items-center">
        <input
          type="email"
          name="email"
          placeholder={t('email_placeholder')}
          value={email}
          onChange={e => setEmail(e.target.value)}
          required
        />
        {message != '' && <span className="block mb-2 text-left pl-[10px]">{message}</span>}
        <button disabled={processing} onClick={sendResetLink} className="primary rounded-lg mt-2 flex justify-center">
          {processing ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2" /> : ''}
          {t('btn_send_reset_link')}
        </button>
      </form>
    </div>
  );
}
