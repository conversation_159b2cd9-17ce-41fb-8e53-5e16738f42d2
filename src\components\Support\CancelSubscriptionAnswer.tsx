import { useTranslations } from 'next-intl';

const CancelSubscriptionAnswer = () => {
  const t = useTranslations('support_page.faq_details.cancel-subscription.answer');

  return (
    <>
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t.raw('paragraph1') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph2') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph3') }} 
      />
      <p 
        className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]" 
        dangerouslySetInnerHTML={{ __html: t('paragraph4') }} 
      />
    </>
  );
};

export default CancelSubscriptionAnswer; 