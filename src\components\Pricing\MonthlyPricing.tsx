'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Prices } from '@/app/prices';

type MonthlyPricingProps = {
  prices: Prices;
  showAfterTrial?: boolean;
};

const MonthlyPricing: React.FC<MonthlyPricingProps> = ({ prices, showAfterTrial }) => {
  const t = useTranslations('pricing');

  return (
    <div className="flex flex-col items-center gap-7 border border-primary pb-5 rounded-md w-[320px]">
      <span className="p-3 bg-primary text-white font-semibold rounded-sm w-full text-center">
        {t('subscription_header')} {showAfterTrial && t('subscription_after_trial')}
      </span>
      <div className="flex flex-col items-center">
        <div className="flex items-center gap-2">
          <span className="text-4xl font-bold">{prices.subscription.formatted}</span>
          <span>{t('monthly_period')}</span>
        </div>
        {prices.vatIncluded && <span>{t('vat_included')}</span>}
      </div>
    </div>
  );
};

export default MonthlyPricing;
