import { useMemo } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

const DistributionSection = () => {
  const t = useTranslations('home_page');
  
  const legend = useMemo(
    () => [
      { point: t('label_above144'), interpretation: t('label_above144text') },
      { point: t('label_130144'), interpretation: t('label_130144text') },
      { point: t('label_115129'), interpretation: t('label_115129text') },
      { point: t('label_85114'), interpretation: t('label_85114text') },
      { point: t('label_7084'), interpretation: t('label_7084text') },
      { point: t('label_below70'), interpretation: t('label_below70text') },
    ],
    [t],
  );
  
  return (
    <section className='pt-0' style={{ textAlign: 'center' }}>
      <h2 className='big max-w-[90vw] md:max-w-[508px]' style={{ margin: '0 auto' }}>
        {t('header_IQ_distribution')}
      </h2>
      <p className='max-w-[90vw] md:max-w-[508px]' style={{ margin: '24px auto 60px auto' }}>
        {t('body_IQ_distribution')}
      </p>
      <div className='bg-grey-bg flex flex-wrap justify-center relative py-[70px] md:p-6'>
        <div className='px-[4.444%] md:py-[2.917%]'>
          <ul style={{ fontSize: 20, lineHeight: '130%' }}>
            {legend.map((item, i) => {
              const { point, interpretation } = item;
              return (
                <li key={point} style={{ display: 'flex', marginBottom: i === legend.length - 1 ? 0 : 42 }}>
                  <div className='font-semibold' style={{ width: 110, textAlign: 'left', color: '#191919' }}>
                    {point}{' '}
                  </div>
                  <Image
                    //style={{ float: 'left', marginRight: 10 }}
                    src={`/home/<USER>/arrow.svg`}
                    alt={t('alt.distribution_arrow')}
                    width={7}
                    height={14}
                  />
                  <div
                    className='font-semibold'
                    style={{ marginLeft: 20, color: '#8893AC', fontSize: 20, lineHeight: '130%' }}
                  >
                    {interpretation}
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
        <div className='pt-16 px-5'>
          <Image
            style={{ float: 'left', marginRight: 10 }}
            src={`/home/<USER>/normal-distribution.png`}
            alt={t('alt.distribution_chart')}
            width={774}
            height={401.5}
          />
        </div>
        <Image
          style={{ position: 'absolute', top: '-60px', right: '-50px' }}
          src={`/home/<USER>/wave.svg`}
          alt={t('alt.distribution_wave')}
          width={177}
          height={154}
        />
      </div>
    </section>
  );
};

export default DistributionSection;
