'use client';

import { useState, useEffect, useContext } from 'react';
import { useTranslations } from 'next-intl';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { filesBucket } from '@/config/firebase.config';
import requireAuth from '@/components/requireAuth';
import { useRouter } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';
import { functions } from '@/utils/firebase';
import '@/sass/spinner.scss';
dayjs.extend(localizedFormat);

type GeneratedData = {
  success: boolean;
  sessionId: string;
};

type HistoryItem = {
  iq: number;
  date: string;
  type?: string;
  loveScore?: { category: string; sum: number; percentage: string }[];
  emotionalScores?: { [key: string]: number };
  answers?: any;
};

/**
 * Map of test type labels to their corresponding values.
 */
const TEST_TYPE_LABELS: Record<string, string> = {
  iq: 'IQ',
  'love-languages': 'Love Languages',
  'emotional-intelligence': 'Emotional Intelligence',
};

const TestHistory = () => {
  const router = useRouter();
  const t = useTranslations('members.test_history');
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [loadingRow, setLoadingRow] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const { updateEmotionalScores, updateResults, updateAllAnswers } = useContext(SessionContext);
  const getTestHistory = httpsCallable(functions, 'getTestHistory');
  const rowsPerPage = 10;

  /**
   * Returns a user-friendly label for a test type.
   * Falls back to capitalized type if not found in the map.
   */
  const formatType = (value?: string): string => {
    if (!value) return TEST_TYPE_LABELS['iq'];
    return TEST_TYPE_LABELS[value] || value.charAt(0).toUpperCase() + value.slice(1).replace(/-/g, ' ');
  };

  /**
   * Computes the score for a given test history item.
   * - For 'love-languages', returns the first category name.
   * - For 'emotional-intelligence', returns the sum of all emotional scores.
   * - For other types, returns the IQ score.
   *
   * @param item - The history item to compute the score for.
   * @returns The computed score (string for love-languages, number otherwise).
   */
  const computeScore = (item: HistoryItem): string | number => {
    switch (item.type) {
      case 'love-languages':
        return item.loveScore?.[0]?.category ?? '';
      case 'emotional-intelligence':
        return item.emotionalScores ? Object.values(item.emotionalScores).reduce((acc, curr) => acc + curr, 0) : 0;
      default:
        return item.iq;
    }
  };

  /**
   * Calls the Firebase Cloud Function 'generateReportAndCertificate' to generate a report and/or certificate PDF.
   *
   * @param answers - The user's answers to be included in the report/certificate.
   * @param showResults - Whether to show the results after generation.
   * @returns A promise that resolves to an object containing the success status and sessionId.
   */
  async function generateReportAndCertificate(answers: any, showResults: boolean): Promise<GeneratedData> {
    const generate = httpsCallable(functions, 'generateReportAndCertificate');
    const result = await generate({ answers, showResults });
    const data = result.data as GeneratedData;
    return data;
  }

  /**
   * Handles displaying the result for a given test history item.
   * - For 'emotional-intelligence' and 'love-languages', updates context and navigates to the results page.
   * - For other types, generates a report or certificate and opens the corresponding PDF.
   *
   * @param rowData - The test history item to show results for.
   * @param type - (Optional) The type of file to open: 'report' or 'certificate'.
   * @returns A promise that resolves when the operation is complete.
   */
  const showResult = async (rowData: HistoryItem, type?: 'report' | 'certificate'): Promise<void> => {
    if (rowData.type === 'emotional-intelligence') {
      updateEmotionalScores(rowData.emotionalScores);
      router.push('/emotional-intelligence/results?showResults=true');
      return;
    }

    if (rowData.type === 'love-languages') {
      updateResults(rowData.loveScore);
      router.push('/love-languages/results?showResults=true');
      return;
    }

    setLoadingRow(rowData.date);
    updateAllAnswers(rowData?.answers);
    const generatedData: GeneratedData = await generateReportAndCertificate(rowData?.answers, true);

    if (type) {
      const fileType = type === 'report' ? 'report' : 'certificate';
      const fileUrl = `${filesBucket}/${generatedData.sessionId}/${fileType}.pdf`;
      window.open(fileUrl, '_blank');
    }
    setLoadingRow(null);
  };

  /**
   * Filters out invalid history items based on their type and required properties.
   * - Keeps only items with emotionalScores for 'emotional-intelligence'.
   * - Keeps only items with loveScore for 'love-languages'.
   * - Keeps all other types.
   * @param items Array of HistoryItem
   * @returns Filtered array of valid HistoryItem
   */
  function filterValidHistoryItems(items: HistoryItem[]): HistoryItem[] {
    return items.filter(item => {
      if (item.type === 'emotional-intelligence') return !!item.emotionalScores;
      if (item.type === 'love-languages') return !!item.loveScore;
      return true;
    });
  }

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const data: any = (await getTestHistory()).data;
        const history = filterValidHistoryItems(data.history.reverse());
        setHistory(history);
      } catch (error) {
        console.log('Error while fetching test history:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchHistory();
  }, [getTestHistory]);

  const totalPages = Math.ceil(history.length / rowsPerPage);
  const lastIndex = currentPage * rowsPerPage;
  const firstIndex = lastIndex - rowsPerPage;
  const currentItems = history.slice(firstIndex, lastIndex);
  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);

  return (
    <div className="flex mx-auto flex-col my-10">
      <h1 className="fs-title mb-5 text-[32px] text-center">{t('title')}</h1>

      <table className="table-auto mt-5">
        <thead>
          <tr className="bg-gray-200">
            <th className="p-4 w-[200px] text-center">{t('table.headers.date')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.type')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.score')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.results')}</th>
          </tr>
        </thead>
        <tbody>
          {loading && currentItems.length === 0 && (
            <tr>
              <td colSpan={4} className="p-4 text-center">
                {t('table.loading')}
              </td>
            </tr>
          )}
          {!loading &&
            currentItems.map((item: any) => {
              return (
                <tr key={item.date}>
                  <td className="p-4 text-center">{dayjs(item.date).format('lll')}</td>
                  <td className="p-4 text-center">{formatType(item.type)}</td>
                  <td className="p-4 text-center">{computeScore(item)}</td>
                  <td className="p-4 text-center text-primary">
                    {item.type === 'love-languages' || item.type === 'emotional-intelligence' ? (
                      <p className="text-primary text-[16px] cursor-pointer" onClick={() => showResult(item)}>
                        {t('table.actions.show_result')}
                      </p>
                    ) : loadingRow === item.date ? (
                      <div className="flex justify-center">
                        <Loader2 className="h-4 w-4 animate-spin m-[5px] " />
                      </div>
                    ) : (
                      <div className="flex justify-center items-center space-x-2">
                        <p
                          className="text-primary text-[16px] cursor-pointer"
                          onClick={() => showResult(item, 'report')}>
                          {t('table.actions.report')}
                        </p>
                        <span className="text-primary text-[16px]">•</span> {/* Bullet dot here */}
                        <p
                          className="text-primary text-[16px] cursor-pointer"
                          onClick={() => showResult(item, 'certificate')}>
                          {t('table.actions.certificate')}
                        </p>
                      </div>
                    )}
                  </td>
                </tr>
              );
            })}
        </tbody>
      </table>
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-4 space-x-2">
          {pageNumbers.map(page => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`px-3 py-1 cursor-pointer ${currentPage === page ? 'text-primary text-[16px]' : ''}`}>
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default requireAuth(TestHistory, []);
