'use client';

import { useContext } from 'react';
import PaymentStripe from '@/components/PaymentProviders/PaymentStripe';
import PaymentSolidgate from '@/components/PaymentProviders/PaymentSolidgate';
import UiContext from '@/store/UiContext';
import { PaymentProvider } from '@/store/types';
import SessionContext from '@/store/SessionContext';
import { PaymentIntentContext } from '@/types/payment';
import { getClickId } from '@/utils/getClickId';

interface PaymentProvidersProps {
  provider: PaymentProvider;
}

/**
 * Renders the correct payment provider form based on the 'provider' prop.
 * Passes a unified context object to the payment form.
 */
function PaymentProviders({ provider }: PaymentProvidersProps) {
  const { getIsTrial, getIsOneTime } = useContext(SessionContext);
  const { time } = useContext(UiContext);

  const context: PaymentIntentContext = {
    clickId: getClickId(), // A unique identifier (e.g., from a tracking or analytics system) for the current click/session.
    isTrial: getIsTrial(), // A boolean flag indicating whether the current session is for a trial subscription.
    isOneTime: getIsOneTime(), // A boolean flag indicating if the subscription is a one-time payment.
    time,
  };

  return provider === PaymentProvider.SOLIDGATE ? (
    <PaymentSolidgate context={context} />
  ) : (
    <PaymentStripe context={context} />
  );
}

export default PaymentProviders;
