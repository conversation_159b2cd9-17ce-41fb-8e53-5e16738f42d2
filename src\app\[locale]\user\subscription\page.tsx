'use client';

// Styles
import 'react-responsive-modal/styles.css';
import { useContext, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { Modal } from 'react-responsive-modal';
import requireAuth from '@/components/requireAuth';
import { ActiveSubscription, CreateSubscription, ResumeSubscription } from '@/components/Subscriptions';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';
import { useRouter } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';
import { auth, functions } from '@/utils/firebase';

dayjs.extend(localizedFormat);

function Subscription() {
  const t = useTranslations('members.subscription');
  const router = useRouter();
  const successfulSubscription = new URLSearchParams(window.location.search).get('success');
  const { paymentSystem, updateStripeInvoices, updateSolidgateInvoices } = useContext(SessionContext);
  const { user, hasActiveSubscription, isSubscriptionPayment, isSubscribed } = useSubscriptionPlan();
  const [processing, setProcessing] = useState(false);
  const [loadingPaymentMethod, setLoadingPaymentMethod] = useState(false);
  const [loadingInvoices, setLoadingInvoices] = useState(false);

  /**
   * Opens the self-service portal for managing payment methods.
   * Shows a loading spinner while the request is in progress.
   * Prevents multiple simultaneous requests.
   */
  async function goToSelfServicePortal() {
    if (loadingPaymentMethod) return;

    setLoadingPaymentMethod(true);

    try {
      const createPortalSession = httpsCallable(functions, 'createPortalSession');
      const result: any = await createPortalSession();
      window.location = result.data.url;
    } catch (error) {
      console.error('Error accessing customer portal:', error);
    } finally {
      setLoadingPaymentMethod(false);
    }
  }

  /**
   * Fetches the user's invoices and navigates to the invoices page.
   * Shows a loading spinner while the request is in progress.
   * Prevents multiple simultaneous requests.
   */
  const goToInvoices = async (): Promise<void> => {
    if (loadingInvoices) return;

    setLoadingInvoices(true);

    try {
      if (!user?.email) {
        console.warn('User email is missing');
        setLoadingInvoices(false);
        return;
      }

      const isSolidgate = user.activeSubscriptionType === PaymentProvider.SOLIDGATE;

      const getInvoices = httpsCallable<{ email: string }, any>(
        functions,
        isSolidgate ? 'getSolidgateInvoicesInfo' : 'getStripeInvoicesInfo'
      );
      const invoices = await getInvoices({ email: user.email });

      if (isSolidgate) {
        updateSolidgateInvoices(invoices);
      } else {
        updateStripeInvoices(invoices);
      }

      router.push('/invoices');
    } catch (error) {
      console.error('Error fetching invoices:', error);
    } finally {
      setLoadingInvoices(false);
    }
  };

  useEffect(() => {
    auth.currentUser?.getIdToken(true);
  }, [user]);

  return (
    <div className="flex flex-col items-center my-10">
      <h1 className="fs-title mb-2 text-[32px] leading-[112.5%]">{t('title')}</h1>

      <div className="mt-1 border-b w-full mb-10 flex justify-center md:justify-end pb-2">
        <div className="flex flex-col items-end">
          {paymentSystem !== 'solidgate' && (
            <button
              onClick={goToSelfServicePortal}
              className="text-primary hover:opacity-90 font-semibold text-sm flex gap-1">
              {loadingPaymentMethod && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
              <span>{t('btn_manage_payment_methods')}</span>
            </button>
          )}

          <button onClick={goToInvoices} className="text-primary hover:opacity-90 inline-flex font-semibold text-sm">
            {loadingInvoices && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
            <span>{t('btn_invoices')}</span>
          </button>
        </div>
      </div>

      {successfulSubscription && <p className="text-black mb-5 font-bold">{t('success_message')}</p>}

      {isSubscribed ? (
        <>
          {hasActiveSubscription ? (
            <ActiveSubscription onProcessing={setProcessing} />
          ) : (
            <ResumeSubscription onProcessing={setProcessing} />
          )}
        </>
      ) : (
        isSubscriptionPayment && <CreateSubscription onProcessing={setProcessing} />
      )}

      {processing && (
        <Modal
          open={processing}
          showCloseIcon={false}
          onClose={() => {}}
          center
          classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
          <h2 className="text-lg tracking-normal w-[calc(100%-20px)] text-center">{t('processing_modal.title')}</h2>
        </Modal>
      )}
    </div>
  );
}

export default requireAuth(Subscription, []);
