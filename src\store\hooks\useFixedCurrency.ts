import { useState } from 'react';
import lscache from 'lscache';
import { FixedCurrency } from '@/types/plan-types';

/**
 * React hook to manage the current fixed currency for payments.
 *
 * Initializes the fixed currency state from local storage (key: 'se-cp_fpc') if available.
 *
 * @example
 * const { fixedCurrency, setFixedCurrency } = useFixedCurrency();
 * setFixedCurrency('usd');
 */
export function useFixedCurrency() {
  const [fixedCurrency, setFixedCurrency] = useState<FixedCurrency>(lscache.get('se-cp_fpc'));
  return { fixedCurrency, setFixedCurrency };
}
