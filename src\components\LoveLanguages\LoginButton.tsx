'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';

const LoginButton: React.FC = () => {
  const router = useRouter(); // Initialize the router
  const t = useTranslations('love-languages.landing');
  
  const onLoginClickHandler = () => {
    router.push('/login'); // Navigate to the /login route
  };

  return (
    <button
      className="bg-white border border-[#5DC4FF] rounded-[10px] 
                        w-[87px] h-[38px] md:w-[240px] md:h-[62px] 
                        text-[#5DC4FF] hover:bg-[#5DC4FF] hover:text-white 
                        transition-colors duration-300 
                        font-raleway font-bold text-[16px] md:text-[20px] leading-[24px] 
                        tracking-[-0.03em] z-20"
      onClick={onLoginClickHandler}>
      {t('btn_login')}
    </button>
  );
};

export default LoginButton;
