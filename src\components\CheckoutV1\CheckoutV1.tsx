import { useContext, useMemo } from 'react';
import dynamic from 'next/dynamic';
import TimeBasedTitle from '@/components/ClientComponents/TimeBasedTitle';
import PaymentProviders from '@/components/PaymentProviders';
import OrderSection from '@/containers/checkout/OrderSection';
import GuaranteeSection from '@/containers/checkout/GuaranteeSection';
import BillingFaqSection from '@/containers/checkout/BillingFaqSection';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { COMPLIANCE_TIME_THRESHOLD } from '@/utils/constants';

const DynamicTimeBasedTitle = dynamic(() => import('@/components/ClientComponents/TimeBasedTitle'), {
  ssr: false,
  loading: () => <TimeBasedTitle />,
});

const CheckoutV1 = () => {
  const { paymentSystem, getIsTrial, getIsOneTime } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const compliantVersion = useMemo(() => time < COMPLIANCE_TIME_THRESHOLD, [time]);
  const isTrial = getIsTrial();
  const isOneTime = getIsOneTime();

  return (
    <div className="md:px-[5.69444%] max-w-[1920px] m-auto mt-10">
      <div>
        <DynamicTimeBasedTitle />
      </div>
      <div className="flex flex-wrap justify-between">
        <div className="flex flex-col flex-wrap justify-center lg:w-[calc(50%-20px)] mb-[30px] md:mb-0">
          <OrderSection compliantVersion={compliantVersion} isTrial={isTrial} isOneTime={isOneTime} />
          <GuaranteeSection />
        </div>
        <div className="flex flex-wrap lg:w-[calc(50%-20px)]">
          {paymentSystem ? <PaymentProviders provider={paymentSystem} /> : null}
        </div>
      </div>
      <div>
        <BillingFaqSection />
      </div>
    </div>
  );
};
export default CheckoutV1;
