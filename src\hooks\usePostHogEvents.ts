import { useCallback } from 'react';
import { PaymentIntent } from '@stripe/stripe-js';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { CheckoutVersion, PageTypes, PaymentProvider, PostHogEventEnum, TestTypes } from '@/store/types';

/**
 * Base interface for PostHog payment event payloads.
 */
interface PostHogPaymentEventBase {
  paymentSystem: PaymentProvider;
  checkoutVersion: CheckoutVersion;
  testType?: TestTypes;
  pageType?: PageTypes;
}

/**
 * Interface for the PostHog purchase event payload.
 */
interface PostHogPurchaseEventPayload extends PostHogPaymentEventBase, Partial<PaymentIntent> {}

/**
 * Interface for the PostHog payment failed event payload.
 */
interface PostHogPaymentFailedEventPayload extends PostHogPaymentEventBase {
  reason?: string;
}

/**
 * Interface for the PostHog checkout confirmed event payload.
 */
interface PostHogCheckoutConfirmedPayload extends PostHogPaymentEventBase {}

/**
 * Custom hook for tracking PostHog events related to payments.
 * @returns Object with PostHog event tracking functions.
 */
export function usePostHogEvents() {
  const { captureEvent } = usePostHogAnalytics();

  /**
   * Track a purchase event in PostHog.
   * @param payload - The event payload including payment info, system, and version
   */
  const trackPostHogPurchaseEvent = useCallback(
    (payload: PostHogPurchaseEventPayload) => {
      captureEvent(PostHogEventEnum.PAYMENT_SUCCESS, payload);
    },
    [captureEvent]
  );

  /**
   * Track a checkout confirmed event in PostHog.
   * @param payload - The event payload including payment system and checkout version
   */
  const trackPostHogCheckoutConfirmedEvent = useCallback(
    (payload: PostHogCheckoutConfirmedPayload) => {
      captureEvent(PostHogEventEnum.CHECKOUT_CONFIRMED, payload);
    },
    [captureEvent]
  );

  /**
   * Track a payment failed event in PostHog.
   * @param payload - The event payload including payment info, system, and version
   */
  const trackPostHogPaymentFailedEvent = useCallback(
    (payload: PostHogPaymentFailedEventPayload) => {
      captureEvent(PostHogEventEnum.PAYMENT_FAILED, payload);
    },
    [captureEvent]
  );

  // Add more event trackers here as needed

  return { trackPostHogPaymentFailedEvent, trackPostHogPurchaseEvent, trackPostHogCheckoutConfirmedEvent };
}
