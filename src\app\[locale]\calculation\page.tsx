'use client';

import { memo, useContext, useEffect, useCallback, useMemo } from 'react';
import { httpsCallable } from 'firebase/functions';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import QuestionCategoryTag from '@/components/Tags/QuestionCategoryTag';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useRouter } from '@/lib/i18n/routing';
import { functions } from '@/utils/firebase';
import SessionContext from '@/store/SessionContext';
import { PostHogEventEnum } from '@/store/types';
import { useSubscriptionPlan } from '@/hooks/useSubscriptionPlan';

// Performance optimization: Extract constants
const CALCULATION_DELAY = 7 * 1000; // 7 seconds

// Type safety improvement
interface ReportResult {
  data: {
    sessionId: string;
  };
}

const Calculation = () => {
  const t = useTranslations('calculation');
  const router = useRouter();
  const { captureEvent } = usePostHogAnalytics();
  const { user, isSubscribed } = useSubscriptionPlan();
  const { answers, updateSessionId } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const showResults = searchParams.get('showResults');
  const origin = searchParams.get('origin');

  // Performance optimization: Memoize tags array to prevent recreation on every render
  const tags = useMemo(
    () => [
      t('intelligence_aspects.memory'),
      t('intelligence_aspects.reaction'),
      t('intelligence_aspects.logic'),
      t('intelligence_aspects.concentration'),
      t('intelligence_aspects.speed'),
    ],
    [t]
  );

  // Performance optimization: Memoize function to prevent recreation
  const generateReportAndCertificate = useCallback(async (): Promise<ReportResult> => {
    const generate = httpsCallable(functions, 'generateReportAndCertificate');
    const result = await generate({ answers, showResults });
    return result as ReportResult;
  }, [answers, showResults]);

  useEffect(() => {
    if (user && !isSubscribed) return;

    // Performance optimization: Add error handling while preserving exact logic
    const executeCalculation = async () => {
      try {
        const promises = [new Promise(res => setTimeout(res, CALCULATION_DELAY))];

        if (user && isSubscribed) {
          promises.push(generateReportAndCertificate().then((x: ReportResult) => updateSessionId(x.data.sessionId)));
        }

        await Promise.all(promises);
        router.push(isSubscribed ? '/results' : '/form?origin=calculation');
      } catch (error) {
        // Fallback to form page on error
        router.push('/form?origin=calculation');
      }
    };

    executeCalculation();

    // eslint-disable-next-line
  }, [user, router]);

  useEffect(() => {
    if (origin === 'questions') {
      captureEvent(PostHogEventEnum.CALCULATION_PAGE_VIEWED, {});
    }

    // eslint-disable-next-line
  }, [origin]);

  if (user && !isSubscribed) {
    return (
      <div className="w-full items-center flex flex-col">
        <h5 className="mt-5">{t('reactivate_subscription.message')}</h5>
        <button
          className="button primary text-base font-semibold mb-5 rounded-lg mt-5 text-center"
          onClick={() => router.push('/user/subscription')}>
          {t('reactivate_subscription.button')}
        </button>
      </div>
    );
  }

  return (
    <div className="flex justify-center my-[12vh]">
      <div style={{ flexWrap: 'wrap', maxWidth: 430 }}>
        <div
          className="flex flex-wrap justify-center mb-28 md:mb-28"
          style={{ padding: '0 5.69444%', position: 'relative' }}>
          <svg width="132" height="132" viewBox="0 0 132 132" className="circular-progress">
            <circle className="bg"></circle>
            <circle className="fg"></circle>
          </svg>
          <div className="counter-container">
            <span className="counter"></span>
          </div>
        </div>
        <div className="text-center">
          <h3 className="" style={{ maxWidth: '70%', margin: '0 auto 20px auto' }}>
            {t('title')}
          </h3>
          <p className="" style={{ maxWidth: '90%', margin: '0 auto 36px auto' }}>
            {t('description')}
          </p>
          <ul className="flex flex-wrap justify-center">
            {tags.map((tag, index) => (
              <QuestionCategoryTag key={tag} {...{ tag, index }} />
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default memo(Calculation);
